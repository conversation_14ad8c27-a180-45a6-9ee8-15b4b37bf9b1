// WBAssist Content Script - 网页与扩展程序通信桥梁
// 运行在网页环境中，负责接收网站的认证消息并转发给扩展程序

console.log('🔌 WBAssist Content Script 已加载');

// 防重复处理标志
let authProcessed = false;
let lastAuthToken = null;
let lastMessageTime = {};  // 按消息类型记录时间

// 监听来自网站的 postMessage
window.addEventListener('message', (event) => {
    // 验证消息来源
    if (event.source !== window) return;

    const message = event.data;
    console.log('🔔 Content Script 收到网站消息:', message);
    
    // 特别关注认证相关消息
    if (message.type === 'WBASSIST_AUTH_SUCCESS') {
        console.log('🔑 收到认证成功消息，详细信息:', {
            type: message.type,
            hasPayload: !!message.payload,
            hasToken: !!message.payload?.token,
            tokenLength: message.payload?.token?.length,
            user: message.payload?.user
        });
    }

    // 处理WBAssist相关的消息
    if (message.type && message.type.startsWith('WBASSIST_')) {
        // 🕐 对特定消息类型限制处理频率（但不限制AUTH_SUCCESS）
        const now = Date.now();
        const messageType = message.type;
        
        if (messageType !== 'WBASSIST_AUTH_SUCCESS' && messageType !== 'WBASSIST_LOGOUT') {
            if (lastMessageTime[messageType] && now - lastMessageTime[messageType] < 1000) {
                console.log('⏱️ 消息处理太频繁，跳过:', messageType);
                return;
            }
        }
        lastMessageTime[messageType] = now;
        
        switch (message.type) {
            case 'WBASSIST_WEBSITE_READY':
                console.log('🌐 网站已准备就绪');
                // 🔄 不再响应网站的WEBSITE_READY消息，避免消息循环
                // 初始化时已经发送过EXTENSION_READY消息
                break;

            case 'WBASSIST_AUTH_SUCCESS':
                const token = message.payload.token;
                
                // 改进的重复处理检查：检查时间间隔和token
                const authMessageTime = now;
                if (authProcessed && lastAuthToken === token && 
                    lastMessageTime['WBASSIST_AUTH_SUCCESS'] && 
                    authMessageTime - lastMessageTime['WBASSIST_AUTH_SUCCESS'] < 5000) {
                    console.log('⚠️ 重复的认证消息（5秒内相同token），跳过处理');
                    return;
                }
                
                console.log('✅ 收到认证成功消息，转发给扩展');
                authProcessed = true;
                lastAuthToken = token;
                
                // 转发认证成功消息给扩展程序
                chrome.runtime.sendMessage({
                    action: 'auth_success',
                    data: {
                        access_token: token,
                        user: message.payload.user,
                        supabase_url: message.payload.supabase_url || getBackendUrl() // 优先使用网站传来的URL
                    }
                }).then(() => {
                    console.log('✅ 认证成功消息已发送给background script');
                }).catch(error => {
                    console.error('❌ 发送认证成功消息失败:', error);
                });
                break;

            case 'WBASSIST_LOGOUT':
                console.log('🚪 收到登出消息，转发给扩展');
                // 重置认证状态
                authProcessed = false;
                lastAuthToken = null;
                
                // 转发登出消息给扩展程序
                chrome.runtime.sendMessage({
                    action: 'auth_logout'
                });
                break;
        }
    }
});

// 获取后端URL配置（登录网站URL，与插件API分开）
function getBackendUrl() {
    // 🔧 使用统一的API配置
    try {
        // 如果API_CONFIG已加载，使用智能检测
        if (typeof window !== 'undefined' && window.API_CONFIG) {
            return window.API_CONFIG.getApiUrl();
        }
    } catch (e) {
        console.warn('API_CONFIG not loaded, using fallback');
    }
    
    // 备用方案：手动检测
    if (typeof window !== 'undefined' && window.location) {
        if (window.location.hostname === 'localhost') {
            return 'http://localhost:3000';
        }
        return 'https://wb-assist-web.vercel.app';
    }
    
    // 扩展环境中默认使用生产环境
    return 'https://wb-assist-web.vercel.app';
}

// 监听来自扩展程序的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 Content Script 收到扩展消息:', message);
    
    if (message.action === 'request_auth_status') {
        // 请求网站提供认证状态
        window.postMessage({
            type: 'WBASSIST_REQUEST_AUTH_STATUS',
            payload: { timestamp: Date.now() }
        }, '*');
        sendResponse({ success: true });
    }
    
    return true;
});

// 初始化时通知网站扩展已准备好
setTimeout(() => {
    window.postMessage({
        type: 'WBASSIST_EXTENSION_READY',
        payload: { timestamp: Date.now() }
    }, '*');
    console.log('🚀 Content Script 初始化完成，已通知网站');
    
    // 🚀 延迟并减少初始认证请求
    setTimeout(() => {
        window.postMessage({
            type: 'WBASSIST_REQUEST_AUTH_STATUS',
            payload: { timestamp: Date.now() }
        }, '*');
        console.log('🔍 请求网站认证状态');
    }, 1500); // 🚀 增加延迟，确保网站完全加载
}, 300); // 🚀 适当增加初始延迟 