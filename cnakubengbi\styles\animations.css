/* 动画和过渡效果 */

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 淡出动画 */
@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* 滑入动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 缩放动画 */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 弹跳动画 */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 旋转动画 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 摇摆动画 */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

/* 渐变背景动画 */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 进度条动画 */
@keyframes progressBar {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

/* 点点点加载动画 */
@keyframes dots {
    0%, 20% {
        color: transparent;
        text-shadow: 0.25em 0 0 transparent, 0.5em 0 0 transparent;
    }
    40% {
        color: var(--primary-color);
        text-shadow: 0.25em 0 0 transparent, 0.5em 0 0 transparent;
    }
    60% {
        text-shadow: 0.25em 0 0 var(--primary-color), 0.5em 0 0 transparent;
    }
    80%, 100% {
        text-shadow: 0.25em 0 0 var(--primary-color), 0.5em 0 0 var(--primary-color);
    }
}

/* 应用动画的类 */
.animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-in forwards;
}

.animate-slide-in {
    animation: slideIn 0.4s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

/* 悬停效果 */
.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.02);
}

.hover-glow {
    transition: var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 预约进度特效 */
.booking-progress-effect {
    background: linear-gradient(45deg, 
        rgba(37, 99, 235, 0.1) 0%, 
        rgba(37, 99, 235, 0.2) 25%, 
        rgba(37, 99, 235, 0.1) 50%, 
        rgba(37, 99, 235, 0.2) 75%, 
        rgba(37, 99, 235, 0.1) 100%);
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
}

/* 成功状态动画 */
.success-animation {
    animation: bounce 0.6s ease-out, pulse 2s ease-in-out 0.6s;
}

/* 错误状态动画 */
.error-animation {
    animation: shake 0.5s ease-in-out;
}

/* 文字打字机效果 */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-color);
    }
}

/* 加载点动画 */
.loading-dots::after {
    content: '...';
    animation: dots 1.5s steps(5, end) infinite;
}

/* 进入和离开动画 */
.slide-enter {
    transform: translateX(-100%);
    opacity: 0;
}

.slide-enter-active {
    transform: translateX(0);
    opacity: 1;
    transition: all 0.3s ease-out;
}

.slide-exit {
    transform: translateX(0);
    opacity: 1;
}

.slide-exit-active {
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-in;
}

/* 卡片翻转效果 */
.card-flip {
    perspective: 1000px;
}

.card-flip-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.card-flip:hover .card-flip-inner {
    transform: rotateY(180deg);
}

.card-flip-front,
.card-flip-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
}

.card-flip-back {
    transform: rotateY(180deg);
}

/* 微交互动画 */
.micro-bounce {
    transition: var(--transition-fast);
}

.micro-bounce:active {
    transform: scale(0.98);
}

.micro-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* 渐变文字动画 */
.gradient-text {
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease-in-out infinite;
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 页面加载动画 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.3s ease-out;
    backdrop-filter: blur(5px);
}

.page-loader.hidden {
    opacity: 0;
    pointer-events: none;
}

.loader-content {
    text-align: center;
    max-width: 400px;
    padding: var(--spacing-xl);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid #f3f4f6;
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

.loader-text {
    font-size: 18px;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
}

.loader-steps {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: left;
}

.loader-step {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    color: var(--text-secondary);
    background: var(--surface-color);
}

.loader-step.active {
    background: #eff6ff;
    color: var(--primary-color);
    border: 1px solid #dbeafe;
}

.loader-step.completed {
    background: #f0fdf4;
    color: #059669;
    border: 1px solid #bbf7d0;
}

.loader-step.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.loader-step i {
    font-size: 12px;
    min-width: 12px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ✦ v4.0 全局权限共享动画效果 */
@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 共享权限徽章的特殊样式 */
.plan-badge-with-icon.shared-access {
    position: relative;
    overflow: hidden;
}

.plan-badge-with-icon.shared-access::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 共享权限图标的呼吸效果 */
.shared-access .plan-icon {
    animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
    0% {
        opacity: 0.8;
        transform: scale(1);
    }
    100% {
        opacity: 1;
        transform: scale(1.1);
    }
}
