<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WBAssist 自动预约系统 - Wildberries仓位预约管理</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header 导航栏 -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <i class="fas fa-warehouse"></i>
                <span>WBAssist 自动预约系统</span>
            </div>
            <div class="user-info">
                <div class="connection-status" id="connectionStatus">
                    <div class="status-indicator connecting" id="statusIndicator"></div>
                    <span class="status-text" id="statusText">正在初始化...</span>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">


        <!-- 店铺信息面板 -->
        <section class="store-panel">
            <div class="store-info-card">
                <div class="store-header">
                    <h2>店铺信息</h2>
                    <div class="store-header-actions">
                        <div class="store-selector">
                            <div class="custom-select-wrapper">
                                <select id="storeSelect" class="store-dropdown">
                                    <option value="">选择店铺...</option>
                                </select>
                                <div class="select-arrow">
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                            </div>
                        </div>
                        <div class="license-actions">
                            <button class="btn btn-sm btn-primary" data-action="open-license-bind-modal" title="绑定或管理店铺卡密">
                                <i class="fas fa-key"></i>
                                卡密管理
                            </button>
                        </div>
                    </div>
                </div>
                <div class="store-details" id="storeDetails">
                    <!-- 表头行 -->
                    <div class="store-details-header">
                        <div class="store-header-item">店铺名称</div>
                        <div class="store-header-item">ИНН</div>
                        <div class="store-header-item">店铺等级</div>
                        <div class="store-header-item">剩余天数</div>
                        <div class="store-header-item">剩余预约次数</div>
                    </div>
                    <!-- 数据行 -->
                    <div class="store-details-data">
                        <div class="store-data-item" id="storeName">-</div>
                        <div class="store-data-item" id="storeInn">-</div>
                        <div class="store-data-item">
                            <div class="plan-badge-with-icon" id="storePlanBadge">
                                <i class="plan-icon" id="storePlanIcon"></i>
                                <span id="storePlan" class="plan-text">-</span>
                            </div>
                        </div>
                        <div class="store-data-item">
                            <span id="storeExpiration" class="days-remaining">-</span>
                        </div>
                        <div class="store-data-item" id="remainingCredits">-</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 订单列表和预约面板 -->
        <section class="orders-section">
            <div class="section-header">
                <h2>供货计划订单</h2>
                <div class="header-actions">
                    <button id="refreshOrdersBtn" class="btn-refresh" title="刷新订单列表">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <div class="filters">
                        <select id="statusFilter" class="filter-select">
                            <option value="">全部状态</option>
                            <option value="not_planned">未计划</option>
                            <option value="booking">预约中</option>
                            <option value="accepted">已预约</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 订单列表 -->
            <div class="orders-container" id="ordersContainer">
                <!-- 订单卡片将通过JavaScript动态生成 -->
            </div>

            <!-- 分页控件 -->
            <div class="pagination" id="pagination">
                <button class="pagination-btn" id="prevPage" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span class="pagination-info" id="pageInfo">第 1 页，共 1 页</span>
                <button class="pagination-btn" id="nextPage" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>
    </main>

    <!-- 连接失败提示弹窗 -->
    <div class="modal-overlay" id="connectionErrorModal">
        <div class="modal-content connection-error-modal">
            <div class="modal-header">
                <h3>连接失败</h3>
                <button class="modal-close" id="closeConnectionErrorModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p class="error-message">请检查Wildberries是否未登录。如已登录请联系客服。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="retryConnection">重试连接</button>
                <button class="btn btn-secondary" id="closeErrorModal">关闭</button>
            </div>
        </div>
    </div>

    <!-- 预约向导模态框 -->
    <div class="modal-overlay" id="bookingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>预约向导</h3>
                <button class="modal-close" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 步骤指示器 -->
                <div class="steps-indicator">
                    <div class="step active" data-step="1">
                        <div class="step-number">1</div>
                        <div class="step-label">选择日期</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-number">2</div>
                        <div class="step-label">设置缓冲</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-number">3</div>
                        <div class="step-label">设置系数</div>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-number">4</div>
                        <div class="step-label">确认预约</div>
                    </div>
                </div>

                <!-- 步骤内容 -->
                <div class="step-content" id="stepContent">
                    <!-- 步骤内容将通过JavaScript动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="prevStep" style="display: none;">上一步</button>
                <button class="btn btn-primary" id="nextStep">下一步</button>
            </div>
        </div>
    </div>

    <!-- 预约中状态模态框 -->
    <div class="modal-overlay" id="bookingProgressModal">
        <div class="modal-content booking-progress">
            <div class="modal-header">
                <h3>正在预约</h3>
            </div>
            <div class="modal-body">
                <div class="booking-animation" id="bookingAnimation">
                    <div class="simple-spinner">
                        <div class="spinner-circle"></div>
                    </div>
                </div>
                <div class="booking-status">
                    <h4>系统自动搜索可用仓位...</h4>
                    <p>请不要关闭浏览器，否则插件会停止工作！</p>
                    <div class="warning-text">
                         在预约搜索过程中，请勿删除该供应单，否则会导致流程失败。
                    </div>
                </div>
            </div>
            <div class="modal-footer booking-progress-footer">
                <div class="progress-footer-content">
                    <div class="dont-show-again">
                        <input type="checkbox" id="dontShowAgain" class="checkbox-input">
                        <label for="dontShowAgain" class="checkbox-label">不再显示此提醒</label>
                    </div>
                    <button class="btn btn-secondary" id="closeProgressModal">我已知晓，关闭弹窗</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 卡密绑定模态框 -->
    <div class="modal-overlay" id="licenseBindModal">
        <div class="modal-content license-bind">
            <div class="modal-header">
                <h3>绑定卡密</h3>
                <button class="modal-close" data-action="close-license-bind-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="license-bind-form">
                    <div class="form-group">
                        <label for="storeSelectModal">选择店铺:</label>
                        <select id="storeSelectModal" class="form-control">
                            <option value="">请选择店铺...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="licenseKeyInput">输入卡密:</label>
                        <input type="text" id="licenseKeyInput" class="form-control" 
                               placeholder="请输入卡密，格式如：WBPRO-2024-XXXX-XXXX">
                        <div class="form-help">
                            <p>卡密格式：WB{类型}-{年份}-{随机码}-{随机码}</p>
                            <p>例如：WBPRO-2024-A8X9-K3M7</p>
                            <div class="upgrade-notice">
                                <i class="fas fa-info-circle"></i>
                                <span>支持升级：免费版可升级到专业版或旗舰版，专业版可升级到旗舰版</span>
                            </div>
                        </div>
                    </div>
                    <div class="license-info" id="licenseInfo" style="display: none;">
                        <h4>卡密信息</h4>
                        <div class="info-item">
                            <span class="label">类型:</span>
                            <span class="value" id="licenseType"></span>
                        </div>
                        <div class="info-item">
                            <span class="label">有效期:</span>
                            <span class="value" id="licenseDuration"></span>
                        </div>
                        <div class="info-item">
                            <span class="label">月度配额:</span>
                            <span class="value" id="licenseQuota"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-action="close-license-bind-modal">取消</button>
                <button class="btn btn-primary" id="bindLicenseBtn" data-action="bind-license">
                    <i class="fas fa-link"></i>
                    绑定卡密
                </button>
            </div>
        </div>
    </div>

    <!-- 店铺权限状态模态框 -->
    <div class="modal-overlay" id="storeCapabilitiesModal">
        <div class="modal-content store-capabilities">
            <div class="modal-header">
                <h3>店铺权限状态</h3>
                <button class="modal-close" data-action="close-store-capabilities-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="capabilities-info" id="capabilitiesInfo">
                    <!-- 动态填充店铺权限信息 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-action="close-store-capabilities-modal">关闭</button>
                <button class="btn btn-primary" data-action="open-license-bind-modal">
                    <i class="fas fa-plus"></i>
                    绑定新卡密
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <!-- 先加载常量和工具函数 -->
    <script src="shared/constants.js"></script>
    <script src="shared/utils.js"></script>
    <!-- 然后加载数据和组件 -->
    <script src="js/booking-storage.js"></script>
    <script src="js/real-data-service.js"></script>
    <script src="js/data-converter.js"></script>
    <script src="js/wbassist-api-service.js"></script>
    <script src="js/license-manager.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/booking-wizard.js"></script>
    <!-- 最后加载主应用 -->
    <script src="js/main.js"></script>
</body>
</html>
