import Link from "next/link";

export default function Cta() {
  return (
    <section>
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="py-8">
          {/* CTA box */}
          <div className="relative py-12 px-8 md:py-20 md:px-12">
            {/* Dark box */}
            <div
              className="absolute inset-0 bg-gray-800 -rotate-1 -z-10"
              aria-hidden="true"
            />
            <div className="flex flex-col lg:flex-row justify-between items-center">
              {/* CTA content */}
              <div className="relative mb-6 lg:mr-16 lg:mb-0 text-center lg:text-left">
                <svg
                  className="absolute left-0 bottom-full mb-4"
                  width="82"
                  height="75"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M34.308 36.61c4.659-.575 9.306-1.16 13.811-1.994 2.244-.419 4.472-.907 6.476-1.568.496-.164.982-.34 1.414-.532.22-.088.484-.225.433-.191l.135-.07c.171-.09.365-.28.298-.502-.02-.144-.11-.263-.207-.373a.343.343 0 0 0-.071-.069l-.15-.117c-.205-.164-.508-.312-.754-.474-1.107-.614-2.436-1.153-3.822-1.617-2.791-.923-5.794-1.625-8.81-2.296-5.547-1.181-11.221-2.21-16.876-3.811-2.908-.895-5.829-1.905-8.651-3.44-1.392-.772-2.846-1.573-4.169-2.731-1.304-1.126-2.593-2.72-3.01-4.778-.296-1.588.195-2.856.64-3.83.478-1.001 1.051-1.827 1.648-2.603 1.193-1.555 2.504-2.879 3.84-4.16 2.68-2.541 3.807 1.395 1.68 3.538-1.054 1.07-2.07 2.188-2.877 3.344-.812 1.09-1.384 2.516-1.228 2.89.314 1.246 1.415 2.475 2.927 3.438 1.48 1.026 3.36 1.564 5.18 2.188 3.716 1.116 7.624 1.977 11.544 2.8 4.759.957 9.574 1.902 14.426 3.091 2.428.61 4.864 1.272 7.318 2.25a20.253 20.253 0 0 1 3.692 1.877l1.087.846c.367.323.636.725.959 1.084.355.345.513.79.725 1.207.235.408.351.87.488 1.319a4.44 4.44 0 0 1-.02 1.686c-.16.543-.368 1.075-.716 1.526-.645.938-1.648 1.618-2.689 2.2-.987.51-1.623.733-2.438 1.03a34.62 34.62 0 0 1-2.295.703c-3.044.817-6.054 1.34-9.062 1.818-9.71 1.533-19.432 2.25-28.955 3.732-1.795.301-3.582.63-5.328 1.035-.874.202-1.73.43-2.568.684-.416.13-.825.267-1.221.417l-.577.232c-.177.07-.243.112-.37.17-.429.22-.908.5-1.083.988-.176.525.255.951.497 1.373.088.191.375.465.654.785.289.292.51.638.86.884.637.537 1.27 1.084 2.022 1.511 2.894 1.84 6.348 2.955 9.835 3.785 3.502.828 7.093 1.38 10.696 1.816 11.218 1.389 22.412 1.492 33.724 3.707 2.488.487 4.935 1.073 7.416 1.935 4.596 1.627 7.443 3.63 9.07 5.38.401.45.745.863 1.036 1.248.265.413.46.819.627 1.181.335.729.481 1.367.46 2.006.003.568.068 1.23-.047 1.58-.19.588-.958.146-2.04-.929-1.239-.98-2.926-2.541-5.687-3.957-2.008-1.086-4.259-1.882-6.586-2.526-2.312-.676-4.75-1.136-7.2-1.573-4.933-.813-10.03-1.33-15.21-1.798-5.184-.467-10.453-.891-15.79-1.523-5.34-.685-10.756-1.444-16.225-3.252-2.024-.66-4.06-1.522-6.043-2.678l-1.47-.937c-.485-.33-.946-.745-1.413-1.12a8.756 8.756 0 0 1-1.335-1.279l-.633-.7-.356-.405c-.13-.21-.234-.42-.351-.632L.219 48.906c-.292-.85-.28-1.788-.034-2.665a5.642 5.642 0 0 1 1.386-2.31 9.114 9.114 0 0 1 2.367-1.627c.205-.101.467-.251.627-.312l.424-.179c.284-.124.55-.215.822-.317a27.109 27.109 0 0 1 1.593-.52 46.502 46.502 0 0 1 3.09-.788c8.102-1.642 15.934-2.664 23.779-3.705l.035.126Z"
                    fill="#93C5FD"
                    fillRule="evenodd"
                  />
                </svg>
                <h3 className="h2 font-cabinet-grotesk text-gray-100">
                  开启您的Wildberries智能预约之旅
                </h3>
                <p className="text-xl text-gray-300 mt-4">
                  加入1000+成功卖家的选择，让WBAssist为您的业务增长助力
                </p>
              </div>
              {/* CTA buttons */}
              <div className="shrink-0 flex flex-col sm:flex-row gap-4">
                <Link
                  className="btn text-white bg-blue-600 hover:bg-blue-700 shadow-sm"
                  href="/signin"
                >
                  立即开始使用
                </Link>
                <Link
                  className="btn text-gray-300 bg-gray-700 hover:bg-gray-600 shadow-sm"
                  href="#pricing"
                >
                  查看定价方案
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
