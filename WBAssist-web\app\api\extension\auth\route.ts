import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

// 获取当前用户认证状态（插件专用API）
export async function GET(request: NextRequest) {
  try {
    // 从Authorization header获取token
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        authenticated: false,
        user: null
      }, { status: 401 })
    }

    const token = authHeader.substring(7) // 移除 "Bearer " 前缀
    const supabase = createClient()
    
    // 使用提供的token验证用户
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return NextResponse.json({
        authenticated: false,
        user: null
      }, { status: 401 })
    }

    // 返回简化的用户信息（符合架构蓝本）
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email
      }
    })

  } catch (error) {
    console.error('获取认证状态API错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 验证Token（用于插件验证）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token } = body

    if (!token) {
      return NextResponse.json(
        { error: 'Token不能为空' },
        { status: 400 }
      )
    }

    const supabase = createClient()
    
    // 使用提供的token验证用户
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return NextResponse.json({
        valid: false,
        error: 'Token无效或已过期'
      }, { status: 401 })
    }

    // 获取用户的店铺和卡密绑定信息（使用正确的表结构）
    const { data: licenseBindings, error: bindingsError } = await supabase
      .from('license_bindings')
      .select(`
        id,
        status,
        activated_at,
        expires_at,
        current_month_used,
        licenses (
          license_type,
          monthly_quota
        ),
        stores (
          wb_supplier_id,
          store_name
        )
      `)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .order('activated_at', { ascending: false })

    if (bindingsError) {
      console.error('获取用户绑定信息失败:', bindingsError)
    }

    return NextResponse.json({
      valid: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.user_metadata?.full_name || user.email
      },
      license_bindings: licenseBindings || []
    })

  } catch (error) {
    console.error('验证Token API错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
