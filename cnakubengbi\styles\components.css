/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    min-height: 40px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--surface-color);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--background-color);
    border-color: var(--text-secondary);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: #d97706;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 12px;
    min-height: 32px;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 16px;
    min-height: 48px;
}

.btn-cancel-header {
    background: var(--warning-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-height: auto;
}

.btn-cancel-header:hover:not(:disabled) {
    background: #d97706;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 订单卡片 */
.order-card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.order-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.order-card.not_planned {
    border-color: rgba(245, 158, 11, 0.4);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(245, 158, 11, 0.12) 100%);
}

.order-card.booking {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.02) 0%, rgba(37, 99, 235, 0.05) 100%);
}

.order-card.scheduled {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.02) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.order-card.accepted {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.02) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.order-header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.order-number {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.order-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.order-status.not_planned {
    background: var(--warning-color);
    color: white;
}

.order-status.booking {
    background: var(--primary-color);
    color: white;
}

.order-status.scheduled {
    background: var(--success-color);
    color: white;
}

.order-status.accepted {
    background: var(--success-color);
    color: white;
}

/* 监控状态样式 */
.monitoring-status {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(37, 99, 235, 0.1) 100%);
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: var(--radius-md);
}

.monitoring-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.monitoring-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
}

.monitoring-indicator i {
    color: var(--primary-color);
    animation: spin 2s linear infinite;
}

.monitoring-details {
    color: var(--text-secondary);
    font-size: 12px;
    margin-left: calc(var(--spacing-sm) + 16px); /* 对齐图标后的文本 */
}

/* 监控状态动画 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 监控状态变体 */
.monitoring-status.found {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-color: rgba(16, 185, 129, 0.2);
}

.monitoring-status.found .monitoring-indicator {
    color: var(--success-color);
}

.monitoring-status.found .monitoring-indicator i {
    color: var(--success-color);
    animation: none;
}

.monitoring-status.failed {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.1) 100%);
    border-color: rgba(239, 68, 68, 0.2);
}

.monitoring-status.failed .monitoring-indicator {
    color: var(--danger-color);
}

.monitoring-status.failed .monitoring-indicator i {
    color: var(--danger-color);
    animation: none;
}

.order-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

/* 基础信息和扩展信息的分组样式 */
.basic-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.extended-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    margin-top: var(--spacing-md);
    position: relative;
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.extended-info::before {
    content: '详细信息';
    position: absolute;
    top: -8px;
    left: var(--spacing-md);
    background: var(--surface-color);
    padding: 0 var(--spacing-sm);
    font-size: 11px;
    font-weight: 600;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border-radius: var(--radius-sm);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 统一的详情项样式 - 支持 detail-item 和 order-detail-item */
.detail-item,
.order-detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    background: rgba(248, 250, 252, 0.5);
    border-radius: var(--radius-sm);
    border: 1px solid transparent;
    transition: var(--transition-fast);
}

.detail-item:hover,
.order-detail-item:hover {
    background: rgba(248, 250, 252, 0.8);
    border-color: var(--border-color);
}

.detail-item .label,
.detail-item label,
.order-detail-item .label,
.order-detail-item label {
    font-size: 11px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: var(--spacing-xs);
}

.detail-item .value,
.detail-item span:not(.label),
.order-detail-item .value,
.order-detail-item span:not(.label) {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.4;
    word-break: break-word;
}

/* 供货系数颜色样式 */
.coefficient-free {
    color: var(--success-color) !important;
    font-weight: 600;
}

.coefficient-good {
    color: var(--success-color) !important;
    font-weight: 600;
}

.coefficient-ok {
    color: var(--primary-color) !important;
    font-weight: 600;
}

.coefficient-warning {
    color: var(--warning-color) !important;
    font-weight: 600;
}

.coefficient-danger {
    color: var(--danger-color) !important;
    font-weight: 600;
}

/* 验收成本颜色样式 */
.cost-free {
    color: var(--success-color) !important;
    font-weight: 600;
}

.cost-paid {
    color: var(--text-primary) !important;
    font-weight: 600;
}

.order-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* 预约详情（已预约状态显示） */
.booking-details {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

/* 预约进度动画区域 */
.booking-progress-area {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(37, 99, 235, 0.1) 100%);
    border-radius: var(--radius-md);
    text-align: center;
}

.booking-animation-container {
    width: 180px;
    height: 180px;
    margin: 0 auto var(--spacing-xs);
}

.booking-progress-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.booking-progress-subtext {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: var(--transition-normal);
}

.modal-overlay.active .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-xl);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* 步骤指示器 */
.steps-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

.steps-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--border-color);
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--background-color);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.step.active .step-number {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.step.completed .step-number {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.step-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    text-align: center;
}

.step.active .step-label {
    color: var(--primary-color);
}

.step.completed .step-label {
    color: var(--success-color);
}

/* 表单元素 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface-color);
    font-size: 14px;
    transition: var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-help {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* 预约进度模态框特殊样式 */
.modal-content.booking-progress {
    max-width: 500px;
    text-align: center;
}

.booking-progress .booking-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: var(--spacing-md) auto;
    height: 150px;
    width: 100%;
}

/* 简单的CSS动画替代Lottie */
.simple-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.spinner-circle {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: breathe 2s ease-in-out infinite;
    position: relative;
}

.spinner-circle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80%;
    height: 80%;
    background: var(--bg-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.spinner-circle::after {
    content: '⚡';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    color: var(--primary-color);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes breathe {
    0% { 
        transform: scale(1);
        box-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(37, 99, 235, 0.6);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
    }
}

.booking-progress .booking-status {
    margin-top: var(--spacing-md);
}

.booking-progress .booking-status h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.booking-progress .booking-status p {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.booking-progress .warning-text {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    font-size: 13px;
    color: var(--warning-color);
    margin: var(--spacing-md) 0;
}

/* 预约进度模态框底部样式 */
.booking-progress-footer {
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    text-align: center;
}

.progress-footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    width: 100%;
}

.dont-show-again {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.checkbox-input {
    width: 16px;
    height: 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
}

.checkbox-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label {
    font-size: 13px;
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

.progress-footer-content .btn {
    margin: 0 auto;
}

.booking-animation {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-lg);
    display: flex;
    justify-content: center;
    align-items: center;
}

.booking-status h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.booking-status p {
    font-size: 14px;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.warning-text {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    font-size: 13px;
    color: var(--warning-color);
    text-align: center;
    font-weight: 500;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

/* 订单摘要样式 */
.order-summary {
    background: var(--background-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.order-summary h5 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item span:first-child {
    color: var(--text-secondary);
    font-weight: 500;
}

.summary-item span:last-child {
    color: var(--text-primary);
    font-weight: 600;
}

/* 升级提示样式 */
.upgrade-notice {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.upgrade-notice i {
    font-size: 24px;
    color: var(--warning-color);
}

.upgrade-notice h6 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.upgrade-notice p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
}

/* 系数指南样式 */
.coefficient-guide {
    background: var(--background-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.coefficient-guide h6 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.guide-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) 0;
}

.coefficient-value {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 24px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 600;
    color: white;
    min-width: 32px;
}

.coefficient-value.good {
    background: var(--success-color);
}

.coefficient-value.ok {
    background: var(--primary-color);
}

.coefficient-value.warning {
    background: var(--warning-color);
}

.coefficient-value.danger {
    background: var(--danger-color);
}

.coefficient-value.info {
    background: var(--secondary-color);
}



/* 免费版说明样式 */
.free-version-note {
    background: rgba(37, 99, 235, 0.05);
    border: 1px solid rgba(37, 99, 235, 0.1);
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    margin-top: var(--spacing-sm);
}

.free-version-note p {
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
}

.free-version-note p:last-child {
    margin-bottom: 0;
}

/* 确认摘要样式 */
.confirmation-summary {
    background: var(--background-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.summary-section {
    margin-bottom: var(--spacing-lg);
}

.summary-section:last-child {
    margin-bottom: 0;
}

.summary-section h6 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

/* 警告通知样式 */
.warning-notice {
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
}

.warning-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    color: var(--danger-color);
}

.warning-header i {
    font-size: 18px;
}

.warning-notice ul {
    margin: 0;
    padding-left: var(--spacing-lg);
    color: var(--text-primary);
}

.warning-notice li {
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.warning-notice li:last-child {
    margin-bottom: 0;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--surface-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    max-width: 400px;
    z-index: 1001;
    transform: translateX(100%);
    transition: var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--primary-color);
}

.notification i {
    font-size: 18px;
}

.notification.success i {
    color: var(--success-color);
}

.notification.error i {
    color: var(--danger-color);
}

.notification.warning i {
    color: var(--warning-color);
}

.notification.info i {
    color: var(--primary-color);
}

.notification span {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.notification-close:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

/* 网络状态通知 */
.network-notification {
    position: fixed;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    background: var(--surface-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    z-index: 1001;
    transition: var(--transition-normal);
}

.network-notification.show {
    transform: translateX(-50%) translateY(0);
}

.network-notification.online {
    border-left: 4px solid var(--success-color);
}

.network-notification.offline {
    border-left: 4px solid var(--danger-color);
}

.network-notification.online i {
    color: var(--success-color);
}

.network-notification.offline i {
    color: var(--danger-color);
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .basic-info,
    .extended-info {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: var(--spacing-sm);
    }
}

@media (max-width: 768px) {
    .order-details {
        grid-template-columns: 1fr;
    }

    .basic-info,
    .extended-info {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .detail-item,
    .order-detail-item {
        padding: var(--spacing-xs);
    }

    .detail-item .label,
    .detail-item label,
    .order-detail-item .label,
    .order-detail-item label {
        font-size: 10px;
    }

    .detail-item .value,
    .detail-item span:not(.label),
    .order-detail-item .value,
    .order-detail-item span:not(.label) {
        font-size: 13px;
    }

    .order-actions {
        justify-content: stretch;
    }

    .order-actions .btn {
        flex: 1;
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-lg);
    }

    .steps-indicator {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }

    .steps-indicator::before {
        display: none;
    }

    .notification {
        top: var(--spacing-md);
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
    }

    .network-notification {
        bottom: var(--spacing-md);
        left: var(--spacing-md);
        right: var(--spacing-md);
        transform: translateY(100%);
    }

    .network-notification.show {
        transform: translateY(0);
    }
}

@media (max-width: 480px) {
    .basic-info,
    .extended-info {
        grid-template-columns: 1fr;
    }

    .order-card {
        padding: var(--spacing-lg);
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .order-header-actions {
        width: 100%;
        justify-content: space-between;
    }
}

/* 日期选择器样式 */
.date-selector {
    position: relative;
}

.date-selector-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: #8b5cf6;
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.date-selector-btn:hover {
    background: #7c3aed;
}

.calendar-icon {
    font-size: 1.1rem;
}

.date-picker-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    margin-top: 4px;
    backdrop-filter: blur(10px);
}

/* 紧凑日历样式 */
.compact-calendar {
    padding: var(--spacing-md);
    color: #495057;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
}

.calendar-months {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.compact-month {
    min-width: 0;
}

.compact-month-header {
    text-align: center;
    font-size: 1rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: var(--spacing-sm);
    padding: 8px 0;
    background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 100%);
    border-radius: 6px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.compact-day-headers {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 2px;
}

.compact-day-header {
    text-align: center;
    font-size: 0.75rem;
    color: #6c757d;
    padding: 6px 2px;
    font-weight: 500;
}

.compact-days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    background: transparent;
    padding: 4px;
}

.compact-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    font-size: 0.8rem;
    cursor: default;
    transition: all 0.2s ease;
    color: #adb5bd;
    min-height: 32px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.compact-day.selectable {
    cursor: pointer;
    color: #495057;
    background: #ffffff;
    border-color: #dee2e6;
}

.compact-day.selectable:hover {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
    border-color: #8b5cf6;
    transform: scale(1.05);
}

.compact-day.selected {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    color: white;
    font-weight: 600;
    border-color: #8b5cf6;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}

.compact-day.disabled {
    color: #ced4da;
    background: #f8f9fa;
    border-color: #e9ecef;
}

.compact-day.other-month {
    color: #ced4da;
    background: #f8f9fa;
}

/* 日历操作按钮 */
.calendar-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    margin: var(--spacing-md) -var(--spacing-md) -var(--spacing-md) -var(--spacing-md);
    border-top: 1px solid #dee2e6;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
}

.calendar-action-btn {
    background: #ffffff;
    border: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 0.8rem;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex: 1;
}

.calendar-action-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.calendar-action-btn.primary {
    background: #8b5cf6;
    border-color: #8b5cf6;
    color: white;
}

.calendar-action-btn.primary:hover {
    background: #7c3aed;
    border-color: #7c3aed;
}

/* 缓冲天数滑动条样式 */
.buffer-slider-container {
    margin: var(--spacing-md) 0;
}

.buffer-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.buffer-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    transition: all 0.2s ease;
}

.buffer-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.buffer-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    transition: all 0.2s ease;
}

.buffer-slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.buffer-slider:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.buffer-slider:disabled::-webkit-slider-thumb {
    cursor: not-allowed;
    background: #adb5bd;
}

.buffer-slider:disabled::-moz-range-thumb {
    cursor: not-allowed;
    background: #adb5bd;
}

.slider-track {
    margin-top: var(--spacing-sm);
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 4px;
}

.buffer-explanation {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: var(--spacing-md);
    margin-top: var(--spacing-sm);
}

.buffer-explanation p {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

.buffer-explanation p:last-child {
    margin-bottom: 0;
}

.buffer-explanation .upgrade-hint {
    color: #8b5cf6;
    font-weight: 500;
}

/* 已选择日期显示 */
.selected-dates-display {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.no-dates-selected {
    color: #888;
    font-style: italic;
    font-size: 0.85rem;
}

.selected-dates-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    align-items: center;
}

.selected-dates-list strong {
    color: #ccc;
    margin-right: var(--spacing-xs);
    font-size: 0.85rem;
}

.selected-date-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #8b5cf6;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.remove-date {
    background: none;
    border: none;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
    margin-left: 2px;
}

.remove-date:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* ===== 认证相关样式 ===== */

/* 登录提示界面 */
.login-prompt {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 500px;
    padding: 2rem;
}

.login-prompt-content {
    text-align: center;
    max-width: 500px;
    background: white;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-icon {
    font-size: 4rem;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
}

.login-prompt h2 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

.login-prompt p {
    color: var(--text-muted);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.login-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.login-actions .btn {
    min-width: 120px;
}

.login-features {
    border-top: 1px solid var(--border-light);
    padding-top: 2rem;
    margin-top: 2rem;
}

.login-features h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.login-features ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.login-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    color: var(--text-secondary);
}

.login-features li i {
    color: var(--success-color);
    width: 16px;
}

/* 用户信息显示 */
.user-display {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-avatar {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-email {
    font-size: 0.85rem;
    color: var(--text-light);
    font-weight: 500;
}

.auth-status {
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.auth-status.authenticated {
    background: var(--success-color);
    color: white;
}

.logout-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* ===== 卡密绑定相关样式 ===== */

/* 卡密绑定模态框 */
.modal-content.license-bind {
    width: 500px;
    max-width: 90vw;
}

.license-bind-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.form-control {
    padding: 0.75rem;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
}

.form-help {
    margin-top: 0.5rem;
}

.form-help p {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 0.25rem 0;
}

.form-help .upgrade-notice {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding: 0.5rem;
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 4px;
    font-size: 0.8rem;
    color: #3498db;
}

.form-help .upgrade-notice i {
    color: #3498db;
}

.license-info {
    background: var(--bg-light);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

.license-info h4 {
    color: var(--text-dark);
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-light);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.info-item .value {
    color: var(--text-dark);
    font-weight: 600;
}

/* 店铺权限状态模态框 */
.modal-content.store-capabilities {
    width: 600px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
}

.store-capability-item {
    padding: 1.5rem;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    margin-bottom: 1rem;
    background: white;
}

.store-capability-item h4 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.capability-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.capability-status.success {
    background: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.capability-status.warning {
    background: var(--warning-light);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.capability-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item .label {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item .value {
    font-weight: 600;
    color: var(--text-dark);
}

.detail-item .value.active {
    color: var(--success-color);
}

.detail-item .value.expired {
    color: var(--error-color);
}

.capability-actions {
    margin-top: 1rem;
}

.btn-sm {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-prompt-content {
        padding: 2rem 1.5rem;
    }
    
    .login-actions {
        flex-direction: column;
    }
    
    .capability-details {
        grid-template-columns: 1fr;
    }
    
    .user-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* 订单操作按钮组样式 */
.order-actions-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.order-actions-group .btn {
    margin: 0;
    font-size: 13px;
}

.order-actions-group .btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
    border: 1px solid #ffc107;
}

.btn-warning:hover {
    background-color: #ffca2c;
    border-color: #ffc720;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .order-actions-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .order-actions-group .btn {
        width: 100%;
        text-align: center;
    }
}


