import { NextRequest, NextResponse } from "next/server";
import { createServerAdminClient, createServerSupabaseClient } from "@/utils/supabase/server";

// 強制動態渲染，因為使用了 cookies 進行身份驗證
export const dynamic = 'force-dynamic';

interface PurchasedLicenseResponse {
  order_id: string;
  product_name: string;
  purchase_date: string;
  purchase_amount: string;
  license_id: string;
  license_key: string;
  license_type: string;
  duration_days: number;
  monthly_quota: number;
  binding_id?: string;
  store_name?: string;
  activated_at?: string;
  expires_at?: string;
  current_month_used?: number;
  status?: string;
}

export async function GET(request: NextRequest) {
  try {
    // 🔐 获取已验证的用户信息（安全方式）- 防止用户篡改
    const supabase = createServerSupabaseClient();
    const {
      data: { user },
      error: authError
    } = await supabase.auth.getUser(); // 向Supabase服务器验证，无法篡改

    if (authError || !user) {
      return NextResponse.json(
        { error: "Unauthorized. Please login to proceed." },
        { status: 401 }
      );
    }

    const userId = user.id; // 这个userId现在是经过服务器验证的，安全！

    // 使用管理员客户端进行复杂查询
    const adminClient = createServerAdminClient();

    // 查询用户购买的所有卡密信息
    // 连接 zpay_transactions、licenses 和 license_bindings 表
    const { data: licensesData, error: licensesError } = await adminClient
      .from("zpay_transactions")
      .select(`
        id,
        out_trade_no,
        amount,
        created_at,
        metadata,
        status
      `)
      .eq("user_id", userId)
      .eq("status", "success")
      .order("created_at", { ascending: false });

    if (licensesError) {
      console.error("Error fetching transactions:", licensesError);
      return NextResponse.json(
        { error: "Failed to fetch purchase history" },
        { status: 500 }
      );
    }

    if (!licensesData || licensesData.length === 0) {
      return NextResponse.json({
        success: true,
        licenses: [],
      });
    }

    // 获取每个交易对应的卡密信息
    const licenses: PurchasedLicenseResponse[] = [];

    for (const transaction of licensesData) {
      const metadata = transaction.metadata || {};
      const licenseKey = metadata.generated_license_key;

      if (!licenseKey) {
        // 跳过没有生成卡密的交易
        continue;
      }

      // 查找对应的license记录
      const { data: licenseData, error: licenseError } = await adminClient
        .from("licenses")
        .select("*")
        .eq("license_key", licenseKey)
        .single();

      if (licenseError || !licenseData) {
        console.warn("License not found for key:", licenseKey);
        continue;
      }

      // 查找对应的绑定记录（如果存在）
      const { data: bindingData, error: bindingError } = await adminClient
        .from("license_bindings")
        .select(`
          id,
          activated_at,
          expires_at,
          current_month_used,
          status,
          stores (
            store_name
          )
        `)
        .eq("license_id", licenseData.id)
        .single();

      // 构建返回数据
      const licenseInfo: PurchasedLicenseResponse = {
        order_id: transaction.out_trade_no,
        product_name: metadata.product_name || "Unknown Product",
        purchase_date: transaction.created_at,
        purchase_amount: transaction.amount.toString(),
        license_id: licenseData.id,
        license_key: licenseData.license_key,
        license_type: licenseData.license_type,
        duration_days: licenseData.duration_days,
        monthly_quota: licenseData.monthly_quota,
      };

      // 如果有绑定记录，添加绑定信息
      if (!bindingError && bindingData) {
        licenseInfo.binding_id = bindingData.id;
        licenseInfo.activated_at = bindingData.activated_at;
        licenseInfo.expires_at = bindingData.expires_at;
        licenseInfo.current_month_used = bindingData.current_month_used || 0;
        licenseInfo.status = bindingData.status;
        
        // 添加店铺名称（如果有）
        if (bindingData.stores && typeof bindingData.stores === 'object' && 'store_name' in bindingData.stores) {
          licenseInfo.store_name = (bindingData.stores as any).store_name;
        }
      }

      licenses.push(licenseInfo);
    }

    return NextResponse.json({
      success: true,
      licenses,
    });

  } catch (error) {
    console.error("Error in /api/me/licenses:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 