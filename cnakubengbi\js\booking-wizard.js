// 预约向导管理器
class BookingWizard {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.bookingData = {};
        this.currentOrder = null;
        this.init();
    }

    init() {
        this.bindEvents();
    }

    // 绑定事件
    bindEvents() {
        // 模态框关闭
        document.getElementById('closeModal').addEventListener('click', () => {
            this.closeModal();
        });



        // 步骤导航
        document.getElementById('prevStep').addEventListener('click', () => {
            this.previousStep();
        });

        document.getElementById('nextStep').addEventListener('click', () => {
            this.nextStep();
        });

        // ESC 键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });

        // 预约进度模态框关闭按钮
        document.getElementById('closeProgressModal').addEventListener('click', () => {
            this.closeProgressModal();
        });

        // 使用事件委托处理动态生成的表单元素
        document.addEventListener('input', (event) => {
            if (event.target.getAttribute('data-action') === 'update-buffer-days') {
                this.updateBufferDays(event.target.value);
            }
        });

        document.addEventListener('change', (event) => {
            if (event.target.getAttribute('data-action') === 'update-coefficient') {
                this.updateCoefficient(event.target.value);
            }
        });

        // 使用事件委托处理按钮点击
        document.addEventListener('click', (event) => {
            const action = event.target.getAttribute('data-action');

            // 处理日期点击（没有data-action属性）
            if (!action) {
                const dayElement = event.target.closest('[data-selectable="true"]');
                if (dayElement) {
                    const dateStr = dayElement.getAttribute('data-date');
                    if (dateStr) {
                        this.toggleCompactDate(dateStr);
                    }
                }
                return;
            }

            switch (action) {
                case 'toggle-date-picker':
                    this.toggleDatePicker();
                    break;
                case 'clear-dates':
                    this.clearAllDates();
                    break;
                case 'select-next-7-days':
                    this.selectNext7Days();
                    break;
                case 'confirm-date-selection':
                    this.confirmDateSelection();
                    break;
            }
        });
    }

    // 开始预约流程
    async startBooking(orderId, type) {
        // 确保ID是正确的数据类型
        const id = parseInt(orderId);

        // 尝试从真实数据服务获取订单信息
        let order = type === 'order'
            ? window.realDataService.getOrder(id, null)
            : window.realDataService.getOrder(null, id);

        // 如果数据服务中没有找到订单，尝试重新加载订单数据
        if (!order) {

            try {
                // 重新加载订单数据
                await window.realDataService.getOrders({ page: 1, pageSize: 50 });

                // 再次尝试获取订单
                order = type === 'order'
                    ? window.realDataService.getOrder(id, null)
                    : window.realDataService.getOrder(null, id);
            } catch (error) {
                console.error('重新加载订单数据失败:', error);
            }
        }

        // 如果仍然没有找到订单，创建一个基础的订单对象用于预约流程
        if (!order) {
            order = {
                orderNumber: type === 'order' ? id : null,
                supplyId: type === 'supply' ? id : null,
                deliveryType: "未知配送类型",
                creationDate: new Date().toISOString().split('T')[0],
                warehouse: "未知仓库",
                itemsQuantity: 0,
                bookingStatus: 'not_planned'
            };
        }

        this.currentOrder = order;
        this.currentStep = 1;
        this.bookingData = {
            orderId: orderId,
            type: type,
            selectedDates: [],
            bufferDays: 1,
            maxCoefficient: 20,
            autoAccept: true,
            // 确保originalPreorderId始终是字符串格式（这是订单界面的"供应单: -"ID值）
            originalPreorderId: order.preorderId ? String(order.preorderId) : 
                               (type === 'order' ? String(orderId) : null)
        };



        this.showModal();
        this.updateStepIndicator();
        this.renderStepContent();
    }

    // 显示模态框
    showModal() {
        const modal = document.getElementById('bookingModal');
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // 关闭模态框
    closeModal() {
        const modal = document.getElementById('bookingModal');
        modal.classList.remove('active');
        document.body.style.overflow = '';
        
        // 重置状态
        this.currentStep = 1;
        this.bookingData = {};
        this.currentOrder = null;
    }

    // 上一步
    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepIndicator();
            this.renderStepContent();
            this.updateButtons();
        }
    }

    // 下一步
    nextStep() {
        if (this.validateCurrentStep()) {
            if (this.currentStep < this.totalSteps) {
                this.currentStep++;
                this.updateStepIndicator();
                this.renderStepContent();
                this.updateButtons();
            } else {
                this.confirmBooking();
            }
        }
    }

    // 验证当前步骤
    validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                if (!this.bookingData.selectedDates || this.bookingData.selectedDates.length === 0) {
                    this.showError('请至少选择一个预约日期');
                    return false;
                }
                return true;

            case 2:
                const bufferSlider = document.getElementById('bufferDaysSlider');
                if (bufferSlider) {
                    this.bookingData.bufferDays = parseInt(bufferSlider.value) || 1;
                }
                return true;

            case 3:
                const coefficientInput = document.getElementById('maxCoefficient');
                const coefficient = parseFloat(coefficientInput.value);
                if (isNaN(coefficient) || coefficient < 0) {
                    this.showError('请输入有效的系数上限');
                    return false;
                }
                this.bookingData.maxCoefficient = coefficient;
                return true;

            case 4:
                return true;

            default:
                return true;
        }
    }

    // 更新步骤指示器
    updateStepIndicator() {
        const steps = document.querySelectorAll('.step');
        steps.forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
            }
        });
    }

    // 渲染步骤内容
    renderStepContent() {
        const content = document.getElementById('stepContent');
        
        switch (this.currentStep) {
            case 1:
                content.innerHTML = this.renderDateSelection();
                break;
            case 2:
                content.innerHTML = this.renderBufferSettings();
                break;
            case 3:
                content.innerHTML = this.renderCoefficientSettings();
                break;
            case 4:
                content.innerHTML = this.renderConfirmation();
                break;
        }

        this.updateButtons();
    }

    // 渲染日期选择步骤
    renderDateSelection() {
        // 如果没有选择日期，默认选择接下来7天
        if (!this.bookingData.selectedDates || this.bookingData.selectedDates.length === 0) {
            this.setDefaultDates();
        }

        return `
            <div class="step-content-wrapper">
                <h4>选择预约日期</h4>
                <p class="step-description">请选择您希望预约的送仓日期</p>

                <div class="form-group">
                    <label class="form-label">预约日期</label>
                    <div class="date-selector">
                        <button type="button" class="date-selector-btn" id="datePickerBtn" data-action="toggle-date-picker">
                            <i class="calendar-icon">📅</i>
                            <span id="datePickerText">${this.getDatePickerText()}</span>
                        </button>
                        <div class="date-picker-dropdown" id="datePickerDropdown" style="display: none;">
                            ${this.renderCompactCalendar()}
                        </div>
                    </div>
                    <div class="form-help">系统将在选择的日期中搜索可用时段</div>
                </div>

                <div class="order-summary">
                    <h5>订单信息</h5>
                    <div class="summary-item">
                        <span>订单号:</span>
                        <span>${this.currentOrder.orderNumber || this.currentOrder.supplyId}</span>
                    </div>
                    <div class="summary-item">
                        <span>仓库:</span>
                        <span>${this.currentOrder.warehouse}</span>
                    </div>
                    <div class="summary-item">
                        <span>配送类型:</span>
                        <span>${this.currentOrder.deliveryType}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取本地日期字符串（避免时区问题）
    getLocalDateString(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // 设置默认日期（从今天开始的7天）
    setDefaultDates() {
        const today = new Date();
        const dates = [];

        for (let i = 0; i < 7; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() + i);
            dates.push(this.getLocalDateString(date));
        }

        this.bookingData.selectedDates = dates;
    }

    // 获取日期选择器显示文本
    getDatePickerText() {
        if (!this.bookingData.selectedDates || this.bookingData.selectedDates.length === 0) {
            return '选择日期';
        }

        const count = this.bookingData.selectedDates.length;
        return `已选择：${count} 天`;
    }

    // 切换日期选择器显示
    toggleDatePicker() {
        const dropdown = document.getElementById('datePickerDropdown');
        const isVisible = dropdown.style.display !== 'none';

        if (isVisible) {
            dropdown.style.display = 'none';
        } else {
            dropdown.style.display = 'block';
            // 重新渲染日历以确保状态正确
            dropdown.innerHTML = this.renderCompactCalendar();
        }
    }

    // 渲染紧凑日历
    renderCompactCalendar() {
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        return `
            <div class="compact-calendar">
                <div class="calendar-months">
                    ${this.renderCompactMonth(currentYear, currentMonth)}
                    ${this.renderCompactMonth(currentYear, currentMonth + 1)}
                </div>
                <div class="calendar-actions">
                    <button type="button" class="calendar-action-btn" data-action="clear-dates">清空</button>
                    <button type="button" class="calendar-action-btn" data-action="select-next-7-days">未来一周</button>
                    <button type="button" class="calendar-action-btn primary" data-action="confirm-date-selection">确定</button>
                </div>
            </div>
        `;
    }

    // 渲染紧凑月份
    renderCompactMonth(year, month) {
        const date = new Date(year, month, 1);
        const actualMonth = date.getMonth();
        const actualYear = date.getFullYear();

        const firstDay = new Date(actualYear, actualMonth, 1);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                           '7月', '8月', '9月', '10月', '11月', '12月'];
        const dayNames = ['一', '二', '三', '四', '五', '六', '日'];

        let html = `
            <div class="compact-month">
                <div class="compact-month-header">
                    ${monthNames[actualMonth]} ${actualYear}
                </div>
                <div class="compact-day-headers">
                    ${dayNames.map(day => `<div class="compact-day-header">${day}</div>`).join('')}
                </div>
                <div class="compact-days-grid">
        `;

        const today = new Date();
        const minDate = new Date(today);
        // 允许选择从今天开始的日期
        minDate.setHours(0, 0, 0, 0); // 确保时间为当天开始
        const maxDate = new Date(today);
        maxDate.setDate(today.getDate() + 31); // 从今天开始的31天范围

        const currentDate = new Date(startDate);
        for (let week = 0; week < 6; week++) {
            for (let day = 0; day < 7; day++) {
                const dateStr = this.getLocalDateString(currentDate);
                const isCurrentMonth = currentDate.getMonth() === actualMonth;
                const isSelectable = currentDate >= minDate && currentDate <= maxDate;
                const isSelected = this.bookingData.selectedDates && this.bookingData.selectedDates.includes(dateStr);

                let dayClass = 'compact-day';
                if (!isCurrentMonth) dayClass += ' other-month';
                if (!isSelectable) dayClass += ' disabled';
                if (isSelected) dayClass += ' selected';
                if (isSelectable) dayClass += ' selectable';

                html += `
                    <div class="${dayClass}"
                         data-date="${dateStr}"
                         ${isSelectable ? `data-selectable="true"` : ''}>
                        ${currentDate.getDate()}
                    </div>
                `;

                currentDate.setDate(currentDate.getDate() + 1);
            }
        }

        html += `
                </div>
            </div>
        `;

        // 注意：日期点击事件现在通过全局事件委托处理，无需单独绑定

        return html;
    }

    // 注意：日期点击事件现在通过事件委托在bindEvents方法中处理









    // 渲染缓冲设置步骤
    renderBufferSettings() {
        const store = window.realDataService.getCurrentStore();
        const isProOrUltimate = ['Pro', 'Ultimate'].includes(store.subscriptionPlan);

        return `
            <div class="step-content-wrapper">
                <h4>设置缓冲天数</h4>
                <p class="step-description">缓冲天数是为了确保商品能够及时送达仓库的安全时间</p>

                <div class="form-group">
                    <label class="form-label">缓冲天数 (${this.bookingData.bufferDays} 天)</label>
                    <div class="buffer-slider-container">
                        <input type="range"
                               id="bufferDaysSlider"
                               class="buffer-slider"
                               min="0"
                               max="30"
                               value="${this.bookingData.bufferDays}"
                               data-action="update-buffer-days">
                        <div class="slider-track">
                            <div class="slider-labels">
                                <span>0</span>
                                <span>5</span>
                                <span>10</span>
                                <span>15</span>
                                <span>20</span>
                                <span>25</span>
                                <span>30</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-help">
                        <div class="buffer-explanation">
                            <p><strong>缓冲天数的作用：</strong></p>
                            <p>• 用于控制自动预约任务的<strong>提前停止时间</strong>。</p>
                            <p>• <strong>示例 1</strong>：预约日期 2025/08/27，缓冲=1 → 系统将在 <strong>2025/08/26 00:00</strong> 停止任务。</p>
                            <p>• <strong>示例 2</strong>：预约日期 [2025/08/27, 2025/08/30]，缓冲=2 → 系统将以最晚日期为准，在 <strong>2025/08/28 00:00</strong> 停止任务。</p>
                            <p>• 这能确保系统不会在临近目标日期时才执行预约，从而规避风险。</p>
                        </div>
                    </div>
                </div>

                ${!isProOrUltimate ? `
                <div class="upgrade-notice">
                    <i class="fas fa-crown"></i>
                    <div>
                        <h6>升级解锁更多功能</h6>
                        <p>专业版和至尊版支持更多高级功能，大幅提升预约成功率</p>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }

    // 渲染系数设置步骤
    renderCoefficientSettings() {
        const store = window.realDataService.getCurrentStore();
        const isProOrUltimate = ['Pro', 'Ultimate'].includes(store.subscriptionPlan);

        return `
            <div class="step-content-wrapper">
                <h4>设置系数上限</h4>
                <p class="step-description">设置可接受的最大验收系数，超过此值的时段将被跳过</p>

                <div class="form-group">
                    <label class="form-label">最大验收系数</label>
                    <input type="number"
                           id="maxCoefficient"
                           class="form-input"
                           min="0"
                           max="${isProOrUltimate ? '10' : '20'}"
                           step="${isProOrUltimate ? '1' : '0.1'}"
                           value="${this.bookingData.maxCoefficient}"
                           ${!isProOrUltimate ? 'readonly' : ''}
                           data-action="update-coefficient">
                </div>

                ${!isProOrUltimate ? `
                <div class="coefficient-guide">
                    <h6>系数说明</h6>
                    <div class="free-version-note">
                        <p>免费版只能搜索所有接受系数的仓库，升级后可自定义设置</p>
                        <p>如果您选择"免费"，插件将只搜索免费的仓位。</p>
                        <p>如果选择系数 X 小于或等于 1，插件将搜索所有系数为 X 或更低的仓位，包括免费的。</p>
                    </div>
                </div>
                ` : `
                <div class="coefficient-guide">
                    <h6>系数说明</h6>
                    <div class="guide-item">
                        <span class="coefficient-value good">0</span>
                        <span>免费时段，最优选择</span>
                    </div>
                    <div class="guide-item">
                        <span class="coefficient-value ok">1-2</span>
                        <span>低成本时段，性价比较高</span>
                    </div>
                    <div class="guide-item">
                        <span class="coefficient-value warning">3-5</span>
                        <span>中等成本时段</span>
                    </div>
                    <div class="guide-item">
                        <span class="coefficient-value danger">5+</span>
                        <span>高成本时段，建议避免</span>
                    </div>
                </div>
                `}
            </div>
        `;
    }

    // 渲染确认步骤
    renderConfirmation() {
        return `
            <div class="step-content-wrapper">
                <h4>确认预约信息</h4>
                <p class="step-description">请确认以下预约配置，点击开始预约后系统将自动搜索并预约</p>
                
                <div class="confirmation-summary">
                    <div class="summary-section">
                        <h6>订单信息</h6>
                        <div class="summary-item">
                            <span>订单号:</span>
                            <span>${this.currentOrder.orderNumber || this.currentOrder.supplyId}</span>
                        </div>
                        <div class="summary-item">
                            <span>仓库:</span>
                            <span>${this.currentOrder.warehouse}</span>
                        </div>
                        <div class="summary-item">
                            <span>商品数量:</span>
                            <span>${this.currentOrder.itemsQuantity}</span>
                        </div>
                    </div>

                    <div class="summary-section">
                        <h6>预约配置</h6>
                        <div class="summary-item">
                            <span>目标日期:</span>
                            <span>${this.formatSelectedDates()}</span>
                        </div>
                        <div class="summary-item">
                            <span>缓冲天数:</span>
                            <span>${this.bookingData.bufferDays}天</span>
                        </div>
                        <div class="summary-item">
                            <span>系数上限:</span>
                            <span>${this.bookingData.maxCoefficient}</span>
                        </div>
                    </div>


                </div>

                <div class="warning-notice">
                    <div class="warning-header">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>重要提醒</strong>
                    </div>
                    <ul>
                        <li>确认后，我们将开始搜索时间段。插件会自动预订时间段。</li>
                        <li><strong>请不要关闭浏览器，否则插件会停止工作！！！</strong></li>
                        <li> 在预约搜索过程中，请勿删除该供应单，否则会导致流程失败。</li>
                        <li>预约成功后将自动扣减1次预约次数。</li>
                    </ul>
                </div>
            </div>
        `;
    }

    // 更新按钮状态
    updateButtons() {
        const prevBtn = document.getElementById('prevStep');
        const nextBtn = document.getElementById('nextStep');

        // 上一步按钮
        if (this.currentStep === 1) {
            prevBtn.style.display = 'none';
        } else {
            prevBtn.style.display = 'inline-flex';
        }

        // 下一步/确认按钮
        if (this.currentStep === this.totalSteps) {
            nextBtn.textContent = '开始预约';
            nextBtn.className = 'btn btn-success';
        } else {
            nextBtn.textContent = '下一步';
            nextBtn.className = 'btn btn-primary';
        }
    }

    // 确认预约 - 符合新架构的版本，调用WBAssist API创建任务
    async confirmBooking() {
        try {
            // 1. 验证WBAssist认证状态
            if (!window.wbAssistApiService || !window.wbAssistApiService.isAuthenticated) {
                this.showError('请先登录WBAssist系统');
                return;
            }

            // 2. 获取当前选中的店铺ID
            const currentSupplierId = await window.wbApiService.getCurrentSupplierId();
            if (!currentSupplierId) {
                this.showError('请先选择一个有效的店铺');
                return;
            }

            // 3. 检查店铺权限和配额
            try {
                const capabilities = await window.wbAssistApiService.getStoreCapabilities(currentSupplierId);
                if (!capabilities.has_valid_license) {
                    this.showError('该店铺未绑定有效卡密，请先绑定卡密');
                    return;
                }
                
                if (capabilities.license_binding.remaining_quota <= 0) {
                    this.showError('月度配额已用完，请等待下月周期或升级套餐');
                    return;
                }
            } catch (error) {
                this.showError(`权限检查失败：${error.message}`);
                return;
            }

            // 显示加载状态
            this.showLoadingState('正在创建预约任务...');

            // 4. 准备任务配置（符合Edge Function要求的格式）
            const taskConfig = {
                date_range: this.bookingData.selectedDates,  // 数组格式：["2024-09-01", "2024-09-03"]
                time_slots: ["09:00", "14:00"],  // 默认时段，可以后续改为用户选择
                coefficient_min: this.bookingData.maxCoefficient,
                buffer_days: this.bookingData.bufferDays
            };

            // 5. 获取preorderId（这是订单界面的"供应单: -"ID值）
            const orderId = parseInt(this.bookingData.orderId);
            
            // 直接使用originalPreorderId，如果没有则使用orderId，确保是字符串格式
            const preorderId = this.bookingData.originalPreorderId || String(orderId);

            console.log('🔍 详细的preorderId调试信息:', {
                'originalPreorderId': this.bookingData.originalPreorderId,
                'originalPreorderId类型': typeof this.bookingData.originalPreorderId,
                'orderId': orderId,
                'orderId类型': typeof orderId,
                'preorderId最终值': preorderId,
                'preorderId类型': typeof preorderId,
                'preorderId长度': preorderId.length,
                'preorderId.trim()长度': preorderId.trim().length,
                'preorderId是否为空': preorderId === '',
                'preorderId是否undefined': preorderId === undefined,
                'preorderId是否null': preorderId === null
            });

            console.log('准备创建任务:', {
                wb_supplier_id: currentSupplierId,
                wb_preorder_id: preorderId,  // 确保是字符串格式
                task_config: taskConfig,
                raw_order_id: orderId,  // 调试信息
                original_preorder_id: this.bookingData.originalPreorderId  // 调试信息
            });

            // 6. 调用WBAssist API创建任务
            const taskResult = await window.wbAssistApiService.createTask(
                currentSupplierId,
                preorderId,  // 这是订单界面显示的"供应单: -"ID值
                taskConfig
            );

            console.log('任务创建成功:', taskResult);

            // 7. 启动后台监控服务
            const monitoringConfig = {
                taskId: taskResult.task_id,
                orderId: orderId,
                orderType: this.bookingData.type,
                preorderId: preorderId,
                selectedDates: this.bookingData.selectedDates,
                bufferDays: this.bookingData.bufferDays,
                maxCoefficient: this.bookingData.maxCoefficient,
                storeId: currentSupplierId,
                startTime: new Date().toISOString(),
                status: 'monitoring'
            };

            const success = await this.startBackgroundMonitoring(monitoringConfig);

            if (success) {
                // 8. 更新订单状态为监控中
                await window.realDataService.updateOrderStatus(
                    this.bookingData.type === 'order' ? orderId : null,
                    this.bookingData.type === 'supply' ? orderId : null,
                    'booking',
                    {
                        startTime: new Date().toISOString(),
                        taskId: taskResult.task_id,
                        config: monitoringConfig
                    }
                );

                // 9. 触发状态变化事件
                window.dispatchEvent(new CustomEvent('bookingStatusChanged', {
                    detail: {
                        orderId: orderId,
                        taskId: taskResult.task_id,
                        status: 'booking',
                        message: '预约监控已启动'
                    }
                }));

                // 关闭向导模态框
                this.closeModal();

                // 显示监控进度模态框
                this.showMonitoringProgress(monitoringConfig);

                // 确保界面状态立即更新
                if (window.app) {
                    await window.app.loadOrders();
                }

                this.showSuccess('预约任务已创建！系统将自动搜索符合条件的时段并预约。');
            } else {
                this.showError('预约监控启动失败，请重试');
            }

        } catch (error) {
            console.error('创建预约任务失败:', error);
            
            // 🔧 根据错误代码和架构蓝本进行精确处理
            if (error.code === 'DUPLICATE_ACTIVE_TASK' || error.code === 'DUPLICATE_TASK') {
                // 重复任务冲突 - 自动重建任务
                this.handleTaskConflictError(error);
            } else if (error.code === 'PREORDER_ID_EXISTS') {
                // preorderId已存在正在进行的任务 - 自动重建任务
                this.handleTaskConflictError(error);
            } else if (error.code === 'QUOTA_EXCEEDED') {
                // 配额不足
                this.showError('月度预约配额已用完，请等待下月周期或升级套餐');
            } else if (error.code === 'LICENSE_REQUIRED') {
                // 卡密问题
                this.showError('未绑定有效卡密或卡密已过期，请先绑定卡密');
            } else if (error.code === 'STORE_NOT_FOUND') {
                // 店铺问题
                this.showError('店铺不存在或无权限访问，请确认店铺信息');
            } else if (error.message && error.message.includes('该preorderId已存在正在进行的任务')) {
                // 兼容旧版本错误消息格式
                this.handleTaskConflictError(error);
            } else if (error.message && error.message.includes('unique constraint')) {
                // 数据库约束冲突 - 也是重复任务的情况
                this.handleTaskConflictError(error);
            } else {
                // 其他错误
                this.showError(`创建预约任务失败: ${error.message}`);
            }
        } finally {
            this.hideLoadingState();
        }
    }

    // 处理任务冲突错误（按照架构蓝本2.4.1监控服务启动流程）
    async handleTaskConflictError(error) {
        const preorderId = this.bookingData.originalPreorderId || String(parseInt(this.bookingData.orderId));
        
        try {
            console.log('🔧 检测到任务冲突，开始自动重建流程...');
            this.showInfo('检测到现有任务，正在智能处理冲突...');
            
            // 🔧 按照架构蓝本：先查询剩余次数 → 有次数则创建任务
            console.log('📊 第一步：验证权限和配额状态...');
            
            // 🔧 修复：获取当前正确的supplier ID
            const currentSupplierId = await window.wbApiService.getCurrentSupplierId();
            if (!currentSupplierId) {
                this.showError('无法获取当前店铺信息，请刷新页面重试');
                return;
            }
            
            console.log('📋 当前店铺ID:', currentSupplierId);
            const capabilities = await window.wbAssistApiService.getStoreCapabilities(currentSupplierId);
            
            if (!capabilities.has_valid_license) {
                this.showError('店铺未绑定有效卡密，无法创建新任务');
                return;
            }
            
            if (capabilities.license_binding.remaining_quota <= 0) {
                this.showError('月度配额已用完，无法创建新任务');
                return;
            }
            
            console.log('✅ 权限验证通过，剩余配额:', capabilities.license_binding.remaining_quota);
            
            // 🔧 第二步：检查任务的最新状态（可能任务已经完成或取消了）
            this.showInfo('正在检查现有任务状态...');
            
            const tasks = await window.wbAssistApiService.getTasks();
            const existingTask = tasks.tasks.find(task => 
                task.wb_preorder_id === preorderId && 
                ['pending', 'running'].includes(task.status)
            );

            if (!existingTask) {
                // 🔧 没有活跃的冲突任务，直接重试创建
                console.log('🔄 未发现活跃的冲突任务，直接重试创建...');
                this.showInfo('未发现冲突任务，正在重新创建...');
                setTimeout(() => {
                    this.confirmBooking();
                }, 1000);
                return;
            }

            // 🔧 第三步：按照架构蓝本的失败处理流程，自动取消现有任务并重新创建
            console.log(`🔧 发现活跃任务 ${existingTask.id}，状态: ${existingTask.status}`);
            this.showInfo(`发现现有任务(${existingTask.status})，正在自动取消并重新创建...`);
            
            await this.cancelExistingTaskAndRetry(existingTask.id, preorderId);
            
        } catch (checkError) {
            console.warn('🔧 冲突处理失败:', checkError);
            // 🔧 如果智能处理失败，提供手动选项
            this.showError(`任务冲突处理失败: ${checkError.message}。请手动刷新页面后重试。`);
        }
    }

    // 取消现有任务并重试创建（按照架构蓝本2.5失败处理流程）
    async cancelExistingTaskAndRetry(taskId, preorderId) {
        try {
            console.log('🔧 开始取消现有任务:', taskId);
            this.showInfo('正在取消现有任务...');

            // 🔧 按照架构蓝本：调用失败上报API来取消任务（不扣配额）
            await window.wbAssistApiService.reportTaskFailure(
                taskId,
                'cancelled',
                {
                    error_code: 'USER_CANCELLED_FOR_RETRY',
                    error_message: '用户重新创建任务，自动取消现有任务',
                    cancelled_reason: 'duplicate_task_conflict',
                    retry_attempts: 0
                }
            );

            console.log('✅ 现有任务已取消，准备重新创建');
            this.showInfo('现有任务已取消，正在创建新任务...');
            
            // 🔧 等待一小段时间确保数据库状态更新
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // 🔧 按照架构蓝本：重新创建任务
            console.log('🔄 开始重新创建任务...');
            this.confirmBooking();

        } catch (error) {
            console.error('🔧 取消现有任务失败:', error);
            
            if (error.message.includes('任务不存在') || error.message.includes('Task not found')) {
                // 🔧 任务可能已经被其他操作取消了，直接重新创建
                console.log('🔄 任务可能已被取消，直接重新创建...');
                this.showInfo('任务状态已更新，正在重新创建...');
                setTimeout(() => {
                    this.confirmBooking();
                }, 1000);
            } else {
                // 🔧 取消失败，提供用户手动选项
                this.showError(`取消现有任务失败: ${error.message}。请手动刷新页面后重试。`);
            }
        }
    }

    // 等待服务器状态同步完成
    async waitForServerStateSync(preorderId, taskId, maxAttempts = 10) {
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                console.log(`🔍 第${attempt}次检查服务器状态同步 (preorderId: ${preorderId})`);
                
                const tasks = await window.wbAssistApiService.getTasks();
                const targetTask = tasks.tasks.find(task => task.id === taskId);
                
                if (!targetTask) {
                    console.log(`✅ 任务已被删除，状态同步完成`);
                    return true;
                }
                
                if (!['pending', 'running'].includes(targetTask.status)) {
                    console.log(`✅ 任务状态已更新为: ${targetTask.status}`);
                    return true;
                }
                
                // 检查是否还有其他进行中的任务使用相同的preorderId
                const conflictTasks = tasks.tasks.filter(task => 
                    task.wb_preorder_id === preorderId && 
                    ['pending', 'running'].includes(task.status)
                );
                
                if (conflictTasks.length === 0) {
                    console.log(`✅ 没有冲突的进行中任务，状态同步完成`);
                    return true;
                }
                
                if (attempt < maxAttempts) {
                    console.log(`⏳ 状态尚未同步，等待${attempt}秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, attempt * 1000)); // 递增延迟
                }
                
            } catch (error) {
                console.warn(`❌ 第${attempt}次状态检查失败:`, error);
                if (attempt < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, attempt * 1000));
                }
            }
        }
        
        throw new Error('服务器状态同步超时，请稍后手动重试');
    }

    // 显示预约进度模态框
    showBookingProgress() {
        // 检查用户是否选择了不再显示
        if (!this.shouldShowProgressModal()) {
            return;
        }

        const modal = document.getElementById('bookingProgressModal');
        modal.classList.add('active');

        // 重置复选框状态
        const dontShowAgain = document.getElementById('dontShowAgain');
        if (dontShowAgain) {
            dontShowAgain.checked = false;
        }

        // 监听预约完成事件
        const handleBookingComplete = (e) => {
            modal.classList.remove('active');
            window.removeEventListener('bookingStatusChanged', handleBookingComplete);

            if (e.detail.status === 'completed') {
                this.showSuccess('预约成功！系统已为您预约到合适的时段。');
            } else {
                this.showError('预约失败，未找到符合条件的时段，请调整预约条件后重试。');
            }
        };

        window.addEventListener('bookingStatusChanged', handleBookingComplete);
    }

    // 关闭预约进度模态框
    closeProgressModal() {
        const modal = document.getElementById('bookingProgressModal');
        const dontShowAgain = document.getElementById('dontShowAgain');

        // 保存"不再显示"设置
        if (dontShowAgain && dontShowAgain.checked) {
            localStorage.setItem('hideBookingProgressModal', 'true');
        }

        modal.classList.remove('active');
        document.body.style.overflow = '';
    }

    // 检查是否应该显示预约进度模态框
    shouldShowProgressModal() {
        return localStorage.getItem('hideBookingProgressModal') !== 'true';
    }

    // 显示错误信息
    showError(message) {
        this.showToast(message, 'error');
    }

    // 显示信息通知
    showInfo(message) {
        this.showToast(message, 'info');
    }

    // 显示成功信息
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    // 显示Toast通知
    showToast(message, type = 'info') {
        // 创建toast容器（如果不存在）
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(toastContainer);
        }

        // 创建toast元素
        const toast = document.createElement('div');
        toast.style.cssText = `
            background: ${type === 'error' ? '#f44336' : type === 'success' ? '#4caf50' : '#2196f3'};
            color: white;
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            pointer-events: auto;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        toast.textContent = message;

        // 添加到容器
        toastContainer.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 紧凑日历中切换日期选择
    toggleCompactDate(dateStr) {
        console.log('toggleCompactDate - 点击的日期字符串:', dateStr);
        
        if (!this.bookingData.selectedDates) {
            this.bookingData.selectedDates = [];
        }

        const index = this.bookingData.selectedDates.indexOf(dateStr);
        if (index > -1) {
            this.bookingData.selectedDates.splice(index, 1);
            console.log('toggleCompactDate - 移除日期:', dateStr);
        } else {
            this.bookingData.selectedDates.push(dateStr);
            console.log('toggleCompactDate - 添加日期:', dateStr);
        }
        
        console.log('toggleCompactDate - 当前选择的所有日期:', this.bookingData.selectedDates);

        // 重新渲染日历
        const dropdown = document.getElementById('datePickerDropdown');
        if (dropdown) {
            dropdown.innerHTML = this.renderCompactCalendar();
        }

        // 更新按钮文本
        this.updateDatePickerButtonText();
    }



    // 清空所有日期
    clearAllDates() {
        this.bookingData.selectedDates = [];
        const dropdown = document.getElementById('datePickerDropdown');
        if (dropdown) {
            dropdown.innerHTML = this.renderCompactCalendar();
        }
        // 更新按钮文本
        this.updateDatePickerButtonText();
    }

    // 选择接下来7天
    selectNext7Days() {
        this.setDefaultDates();
        const dropdown = document.getElementById('datePickerDropdown');
        if (dropdown) {
            dropdown.innerHTML = this.renderCompactCalendar();
        }
        // 更新按钮文本
        this.updateDatePickerButtonText();
    }

    // 更新日期选择按钮文本
    updateDatePickerButtonText() {
        const datePickerText = document.getElementById('datePickerText');
        if (datePickerText) {
            datePickerText.textContent = this.getDatePickerText();
        }
    }

    // 更新缓冲天数
    updateBufferDays(value) {
        this.bookingData.bufferDays = parseInt(value);

        // 更新标签显示
        const label = document.querySelector('.form-label');
        if (label && label.textContent.includes('缓冲天数')) {
            label.textContent = `缓冲天数 (${value} 天)`;
        }
    }

    // 更新系数
    updateCoefficient(value) {
        this.bookingData.maxCoefficient = parseFloat(value);
    }

    // 调整系数（整数增减）
    adjustCoefficient(delta) {
        const input = document.getElementById('maxCoefficient');
        const store = window.realDataService.getCurrentStore();
        const isProOrUltimate = ['Pro', 'Ultimate'].includes(store.subscriptionPlan);

        if (!isProOrUltimate) {
            return; // 免费版不允许调整
        }

        const currentValue = parseInt(input.value) || 0;
        const newValue = Math.max(0, Math.min(10, currentValue + delta));
        input.value = newValue;
        this.updateCoefficient(newValue);
    }

    // 确认日期选择
    confirmDateSelection() {
        // 更新按钮文本
        this.updateDatePickerButtonText();

        // 关闭下拉框
        const dropdown = document.getElementById('datePickerDropdown');
        if (dropdown) {
            dropdown.style.display = 'none';
        }
    }

    // 格式化选择的日期
    formatSelectedDates() {
        if (!this.bookingData.selectedDates || this.bookingData.selectedDates.length === 0) {
            return '未选择';
        }

        const sortedDates = this.bookingData.selectedDates.sort();
        console.log('formatSelectedDates - 原始选择的日期:', this.bookingData.selectedDates);
        console.log('formatSelectedDates - 排序后的日期:', sortedDates);
        
        const formattedDates = sortedDates.map(dateStr => {
            // 避免时区问题，直接解析日期字符串
            const [year, month, day] = dateStr.split('-').map(Number);
            console.log(`formatSelectedDates - 解析日期: ${dateStr} -> ${month}月${day}日`);
            return `${month}月${day}日`;
        });
        
        console.log('formatSelectedDates - 格式化后的日期:', formattedDates);

        if (formattedDates.length <= 3) {
            return formattedDates.join('、');
        } else {
            return `${formattedDates.slice(0, 2).join('、')} 等${formattedDates.length}个日期`;
        }
    }

    // 验证当前店铺
    async validateCurrentStore() {
        try {
            // 检查API服务是否可用
            if (!window.wbApiService || !window.wbApiService.isAvailable()) {
                throw new Error('WB API服务不可用，请先登录WB卖家中心');
            }

            // 获取当前店铺ID
            const currentSupplierId = await window.wbApiService.getCurrentSupplierId();
            if (!currentSupplierId) {
                throw new Error('未选择店铺，请先选择一个有效的店铺');
            }

            // 验证店铺在供应商列表中存在
            const suppliers = await window.wbApiService.getSuppliers();
            const currentSupplier = suppliers.find(s => s.id === currentSupplierId);
            if (!currentSupplier) {
                throw new Error('当前店铺不存在或已失效，请重新选择店铺');
            }

            return currentSupplier;

        } catch (error) {
            console.error('店铺验证失败:', error);
            throw new Error(`店铺验证失败: ${error.message}`);
        }
    }

    // 验证订单/供应单
    async validateOrder(orderId, type) {
        try {
            // 首先检查本地数据
            const localOrder = window.realDataService.getOrder(
                type === 'order' ? orderId : null,
                type === 'supply' ? orderId : null
            );

            if (!localOrder) {
                throw new Error('订单不存在于本地数据中，请刷新订单列表');
            }

            // 然后通过API验证wb系统中是否存在
            const today = new Date();
            const futureDate = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000); // 未来7天

            try {
                await window.wbApiService.getAcceptanceCosts(
                    orderId,
                    today.toISOString(),
                    futureDate.toISOString()
                );

                return localOrder;

            } catch (apiError) {
                console.warn('API验证订单失败:', apiError);
                // 如果API调用失败，仍然使用本地数据，但给出警告
                return localOrder;
            }

        } catch (error) {
            console.error('订单验证失败:', error);
            throw new Error(`订单验证失败: ${error.message}`);
        }
    }



    // 检查两个日期是否为同一天
    isSameDay(date1, date2) {
        return date1.toDateString() === date2.toDateString();
    }

    // 启动真实预约流程
    async startRealBookingProcess(orderId, optimalSlot) {
        try {

            // 调用后台脚本的自动预约功能（基于2.md的成功实现）
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                const result = await this.sendMessageToBackground('autoBookSupply', {
                    preorderID: orderId,
                    optimalDate: optimalSlot.date
                });

                if (result.success) {
                    return true;
                } else {
                    throw new Error(result.message || '后台预约启动失败');
                }
            } else {
                // 如果不在扩展环境中，使用本地预约流程
                return window.realDataService.startBooking(
                    this.bookingData.type === 'order' ? orderId : null,
                    this.bookingData.type === 'supply' ? orderId : null,
                    this.bookingData
                );
            }

        } catch (error) {
            console.error('启动真实预约流程失败:', error);
            throw error;
        }
    }

    // 发送消息到后台脚本
    async sendMessageToBackground(action, data = {}) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action, ...data }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    // 显示加载状态
    showLoadingState(message) {
        const nextBtn = document.getElementById('nextStep');
        if (nextBtn) {
            nextBtn.disabled = true;
            nextBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${message}`;
        }
    }

    // 隐藏加载状态
    hideLoadingState() {
        const nextBtn = document.getElementById('nextStep');
        if (nextBtn) {
            nextBtn.disabled = false;
            nextBtn.textContent = '开始预约';
        }
    }

    // 启动后台监控服务
    async startBackgroundMonitoring(monitoringConfig) {
        try {

            // 发送监控配置到后台脚本
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                const result = await this.sendMessageToBackground('startMonitoring', {
                    config: monitoringConfig
                });

                if (result.success) {
                    return true;
                } else {
                    throw new Error(result.message || '后台监控服务启动失败');
                }
            } else {
                // 如果不在扩展环境中，使用本地模拟
                this.startLocalMonitoringSimulation(monitoringConfig);
                return true;
            }

        } catch (error) {
            console.error('启动后台监控服务失败:', error);
            throw error;
        }
    }



    // 显示监控进度模态框
    showMonitoringProgress(config) {
        // 检查用户是否选择了不再显示
        if (!this.shouldShowProgressModal()) {
            return;
        }

        const modal = document.getElementById('bookingProgressModal');

        // 更新模态框内容为监控状态
        const statusElement = modal.querySelector('.booking-status h4');
        const descElement = modal.querySelector('.booking-status p');

        if (statusElement) {
            statusElement.textContent = '系统正在监控仓位费用变化...';
        }
        if (descElement) {
            descElement.textContent = '请保持浏览器开启，系统将自动搜索符合条件的时段并预约！';
        }

        modal.classList.add('active');

        // 重置复选框状态
        const dontShowAgain = document.getElementById('dontShowAgain');
        if (dontShowAgain) {
            dontShowAgain.checked = false;
        }

        // 监听监控状态变化事件
        const handleMonitoringStatusChange = (e) => {
            const { orderId, status, optimalSlot, result } = e.detail;

            if (orderId !== config.orderId) return; // 不是当前订单的事件

            if (status === 'found') {
                if (statusElement) {
                    statusElement.textContent = '找到符合条件的时段，正在自动预约...';
                }
                if (descElement) {
                    descElement.textContent = `找到最优时段：${new Date(optimalSlot.date).toLocaleDateString()}，系数：${optimalSlot.coefficient}`;
                }
            } else if (status === 'completed') {
                modal.classList.remove('active');
                window.removeEventListener('monitoringStatusChanged', handleMonitoringStatusChange);

                if (result.success) {
                    // 预约成功后，保存ID映射关系
                    this.handleBookingSuccess(result);
                    this.showSuccess(`预约成功！已为您预约到 ${new Date(result.bookedDate).toLocaleDateString()} 的时段。`);
                } else {
                    this.showError('自动预约失败，请手动重试。');
                }
            } else if (status === 'failed') {
                modal.classList.remove('active');
                window.removeEventListener('monitoringStatusChanged', handleMonitoringStatusChange);
                this.showError('监控过程中发生错误，请重新启动预约。');
            }
        };

        window.addEventListener('monitoringStatusChanged', handleMonitoringStatusChange);
    }

    // 显示成功消息
    showSuccess(message) {
        // 创建成功提示元素
        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success';
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 15px 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 400px;
        `;
        successDiv.innerHTML = `
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            ${message}
        `;

        document.body.appendChild(successDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }

    // 处理预约成功后的ID映射保存
    async handleBookingSuccess(result) {
        try {
            // 检查是否有原始的preorderId和新的supplyId
            const originalPreorderId = this.bookingData?.originalPreorderId;
            const newSupplyId = result?.newSupplyId || result?.supplyId;

            if (originalPreorderId && newSupplyId && originalPreorderId !== newSupplyId) {
                // 保存ID映射关系
                if (window.bookingStorage && window.bookingStorage.isAvailable()) {
                    await window.bookingStorage.saveIdMapping(originalPreorderId, newSupplyId, {
                        bookedDate: result.bookedDate,
                        coefficient: result.coefficient,
                        bookingMethod: 'auto_monitoring',
                        orderId: this.bookingData.orderId,
                        orderType: this.bookingData.type
                    });

                    console.log(`已保存ID映射: ${originalPreorderId} -> ${newSupplyId}`);
                }

                // 通知数据服务更新缓存
                if (window.realDataService) {
                    window.realDataService.handleIdMapping(originalPreorderId, newSupplyId);
                }
            }
        } catch (error) {
            console.error('保存ID映射失败:', error);
            // 不影响用户体验，只记录错误
        }
    }
}

// 创建全局预约向导实例
window.bookingWizard = new BookingWizard();

