// 数据转换服务 - 处理API数据与本地格式的转换
class DataConverter {
    
    // 将API供应商数据转换为本地store格式
    static convertSuppliersToStores(suppliers) {
        if (!suppliers || !Array.isArray(suppliers)) {
            return [];
        }

        return suppliers.map(supplier => {
            // 根据subscriptions判断订阅计划
            let subscriptionPlan = 'Free';
            let remainingCredits = 1;
            
            if (supplier.subscriptions && supplier.subscriptions.length > 0) {
                const activeSubscription = supplier.subscriptions.find(sub => sub.active);
                if (activeSubscription) {
                    switch (activeSubscription.id) {
                        case '1002':
                            subscriptionPlan = 'Pro';
                            remainingCredits = 3;
                            break;
                        case '1003':
                            subscriptionPlan = 'Ultimate';
                            remainingCredits = '∞';
                            break;
                        default:
                            subscriptionPlan = 'Free';
                            remainingCredits = 1;
                    }
                }
            }

            return {
                id: supplier.id,
                displayName: supplier.name,
                inn: supplier.inn || supplier.taxpayerCode || '',
                subscriptionPlan: subscriptionPlan,
                remainingCredits: remainingCredits,
                cardCodeLast4: null, // API中没有此信息
                region: supplier.address?.region || '',
                isActive: !supplier.deactivated && supplier.approved,
                lastUpdated: supplier.updatedAt,
                expiresAt: null, // 暂时设为null，可以根据需要计算
                // 店铺特定的标识符，用于API请求
                supplierId: supplier.id,
                supplierIdExternal: supplier.supplierIdExternal || supplier.id
            };
        });
    }

    // 将API供应计划数据转换为本地订单格式
    static convertSupplyOrdersToLocal(apiOrders) {
        if (!apiOrders || !Array.isArray(apiOrders)) {
            return [];
        }

        return apiOrders.map(apiOrder => {
            // 根据statusId映射预约状态
            let bookingStatus = 'not_planned';
            let deliveryStatus = apiOrder.statusName || '未知状态';
            
            switch (apiOrder.statusId) {
                case 1:  // 新建
                case 2:  // 在сборке
                    bookingStatus = 'not_planned';
                    break;
                case 3:  // 在пути
                case 4:  // 在сортировке
                case 5:  // 在приемке
                    bookingStatus = 'booking';
                    break;
                case 7:  // 已接收
                case 10: // 已发货
                    bookingStatus = 'accepted';
                    break;
                default:
                    bookingStatus = 'not_planned';
            }

            return {
                orderNumber: apiOrder.preorderId,
                supplyId: apiOrder.supplyId,
                deliveryType: apiOrder.boxTypeName || 'Короб',
                creationDate: this.formatDate(apiOrder.createDate),
                warehouse: apiOrder.warehouseName || '',
                transitWarehouse: apiOrder.transitWarehouseName || null,
                itemsQuantity: apiOrder.detailsQuantity || 0,
                bookingStatus: bookingStatus,
                deliveryStatus: deliveryStatus,
                planDate: apiOrder.supplyDate ? this.formatDate(apiOrder.supplyDate) : null,
                acceptanceCoefficient: apiOrder.paidAcceptanceCoefficient,
                acceptanceCost: apiOrder.acceptanceCost || 0,
                // 额外的API字段
                warehouseId: apiOrder.warehouseId,
                warehouseAddress: apiOrder.warehouseAddress,
                factDate: apiOrder.factDate ? this.formatDate(apiOrder.factDate) : null,
                incomeQuantity: apiOrder.incomeQuantity || 0,
                supplierAssignName: apiOrder.supplierAssignName || '',
                volume: apiOrder.volume,
                hasPass: apiOrder.hasPass || false,
                statusId: apiOrder.statusId
            };
        });
    }

    // 格式化日期
    static formatDate(dateString) {
        if (!dateString) return null;
        try {
            const date = new Date(dateString);
            return date.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
        } catch (error) {
            return dateString;
        }
    }

    // 将本地状态映射为API状态ID
    static mapLocalStatusToAPI(localStatus) {
        const statusMap = {
            'not_planned': -2,  // 所有状态
            'booking': 3,       // 在пути
            'accepted': 7       // 已接收
        };
        return statusMap[localStatus] || -2;
    }

    // 将API状态ID映射为本地状态
    static mapAPIStatusToLocal(statusId) {
        const statusMap = {
            1: 'not_planned',   // 新建
            2: 'not_planned',   // 在сборке
            3: 'booking',       // 在пути
            4: 'booking',       // 在сортировке
            5: 'booking',       // 在приемке
            7: 'accepted',      // 已接收
            10: 'accepted'      // 已发货
        };
        return statusMap[statusId] || 'not_planned';
    }

    // 获取状态的中文显示文本
    static getStatusDisplayText(statusId, statusName) {
        const statusMap = {
            1: '新建',
            2: '在сборке',
            3: '在пути',
            4: '在сортировке',
            5: '在приемке',
            7: '已接收',
            10: '已发货'
        };
        return statusMap[statusId] || statusName || '未知状态';
    }
}

// 导出到全局
window.DataConverter = DataConverter;
