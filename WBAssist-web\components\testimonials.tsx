"use client";

import { useEffect } from "react";

import Image from "next/image";
import TestimonialsImage01 from "@/public/images/notion_9.png";
import TestimonialsImage02 from "@/public/images/notion_12.png";
import TestimonialsImage03 from "@/public/images/notion_13.png";

// Import Swiper
import Swiper, { Pagination } from "swiper";
import "swiper/swiper.min.css";
import "swiper/css/pagination";
Swiper.use([Pagination]);

export default function Testimonials() {
  useEffect(() => {
    const testimonial = new Swiper(".testimonial-carousel", {
      slidesPerView: 1,
      watchSlidesProgress: true,
      pagination: {
        el: ".testimonial-carousel-pagination",
        clickable: true,
      },
    });
  }, []);

  return (
    <section>
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="pb-8">
          <div className="relative">
            {/* Gray box */}
            <div
              className="absolute inset-0 bg-gray-100 rotate-2 -z-10"
              aria-hidden="true"
            />
            {/* Content */}
            <div className="relative px-6 pb-8 md:px-12 lg:pb-0">
              {/* Carousel built with Swiper.js [https://swiperjs.com/] */}
              {/* * Custom styles in src/css/additional-styles/theme.scss */}
              <div className="testimonial-carousel swiper-container">
                <div className="swiper-wrapper">
                  {/* Testimonial */}
                  <div className="swiper-slide space-y-8 lg:flex items-center lg:space-y-0 lg:space-x-8 text-center lg:text-left">
                    <div className="shrink-0 relative inline-flex">
                      <Image
                        className=" object-cover object-center"
                        src={TestimonialsImage01}
                        width={180}
                        height={180}
                        alt="用户评价 01"
                      />
                      <div className="absolute right-0 bottom-0 mr-4 mb-6">
                        <div className="flex items-center font-cabinet-grotesk font-bold px-3 py-1 bg-white shadow-sm rounded">
                          <span>@张先生</span>
                        </div>
                      </div>
                    </div>
                    <div className="relative">
                      <h4 className="h3 font-cabinet-grotesk mb-4">
                        "使用WBAssist后，我的预约成功率从60%提升到98%！再也不用熬夜抢仓位了，系统自动帮我搞定一切。"
                      </h4>
                      <div>
                        <a
                          className="btn-sm text-white bg-blue-500 hover:bg-blue-600 group shadow-sm"
                          href="/signin"
                        >
                          立即体验{" "}
                          <span className="tracking-normal text-blue-200 group-hover:translate-x-0.5 transition-transform duration-150 ease-in-out ml-1">
                            -&gt;
                          </span>
                        </a>
                      </div>
                    </div>
                  </div>
                  {/* Testimonial */}
                  <div className="swiper-slide space-y-8 lg:flex items-center lg:space-y-0 lg:space-x-8 text-center lg:text-left">
                    <div className="shrink-0 relative inline-flex">
                      <Image
                        className=" object-cover object-center"
                        src={TestimonialsImage02}
                        width={180}
                        height={180}
                        alt="用户评价 02"
                      />
                      <div className="absolute right-0 bottom-0 mr-4 mb-6">
                        <div className="flex items-center font-cabinet-grotesk font-bold px-3 py-1 bg-white shadow-sm rounded">
                          <span>@李女士</span>
                        </div>
                      </div>
                    </div>
                    <div className="relative">
                      <h4 className="h3 font-cabinet-grotesk mb-4">
                        "WBAssist的智能监控太棒了！24小时不间断工作，我再也不用担心错过预约时机。现在我可以专心做其他业务了。"
                      </h4>
                      <div>
                        <a
                          className="btn-sm text-white bg-blue-500 hover:bg-blue-600 group shadow-sm"
                          href="/signin"
                        >
                          立即体验{" "}
                          <span className="tracking-normal text-blue-200 group-hover:translate-x-0.5 transition-transform duration-150 ease-in-out ml-1">
                            -&gt;
                          </span>
                        </a>
                      </div>
                    </div>
                  </div>
                  {/* Testimonial */}
                  <div className="swiper-slide space-y-8 lg:flex items-center lg:space-y-0 lg:space-x-8 text-center lg:text-left">
                    <div className="shrink-0 relative inline-flex">
                      <Image
                        className=" object-cover object-center"
                        src={TestimonialsImage03}
                        width={180}
                        height={180}
                        alt="用户评价 03"
                      />
                      <div className="absolute right-0 bottom-0 mr-4 mb-6">
                        <div className="flex items-center font-cabinet-grotesk font-bold px-3 py-1 bg-white shadow-sm rounded">
                          <span>@王总</span>
                        </div>
                      </div>
                    </div>
                    <div className="relative">
                      <h4 className="h3 font-cabinet-grotesk mb-4">
                        "作为拥有50多个店铺的大卖家，WBAssist帮我实现了批量管理，团队效率翻倍，整体利润增长了40%！"
                      </h4>
                      <div>
                        <a
                          className="btn-sm text-white bg-blue-500 hover:bg-blue-600 group shadow-sm"
                          href="/signin"
                        >
                          立即体验{" "}
                          <span className="tracking-normal text-blue-200 group-hover:translate-x-0.5 transition-transform duration-150 ease-in-out ml-1">
                            -&gt;
                          </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Bullets */}
              <div className="mt-4 lg:absolute bottom-0 right-0 lg:mt-0 lg:mr-12 lg:mb-16 z-10">
                <div className="testimonial-carousel-pagination text-center" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
