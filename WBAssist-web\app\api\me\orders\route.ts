import { NextRequest, NextResponse } from "next/server";
import { createServerAdminClient, createServerSupabaseClient } from "@/utils/supabase/server";

// 強制動態渲染，因為使用了 cookies 進行身份驗證
export const dynamic = 'force-dynamic';

interface LicenseDetails {
  id: string;
  status: string;
  expires_at?: string;
  bound_to_store?: string;
}

interface OrderItem {
  id: string;
  out_trade_no: string;
  product_name: string;
  amount: string;
  status: string;
  payment_method: string;
  created_at: string;
  license?: LicenseDetails;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

interface OrdersResponse {
  orders: OrderItem[];
  pagination: PaginationInfo;
}

export async function GET(request: NextRequest) {
  try {
    // Get the user session from cookies
    // 🔐 获取已验证的用户信息（安全方式）- 防止篡改订单数据
    const supabase = createServerSupabaseClient();
    const {
      data: { user },
      error: authError
    } = await supabase.auth.getUser(); // 向服务器验证，防止查看别人的订单

    // Check if the user is authenticated
    if (authError || !user) {
      return NextResponse.json(
        { error: "Unauthorized. Please login to proceed." },
        { status: 401 }
      );
    }

    const userId = user.id; // 经过验证的安全userId
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
    const limit = Math.min(50, Math.max(1, parseInt(searchParams.get('limit') || '20')));
    const status = searchParams.get('status');
    
    // Get the admin client for database operations
    const adminClient = createServerAdminClient();

    // Build the query
    let query = adminClient
      .from("zpay_transactions")
      .select("*", { count: 'exact' })
      .eq("user_id", userId);

    // Add status filter if provided
    if (status && ['pending', 'success', 'failed', 'cancelled'].includes(status)) {
      query = query.eq("status", status);
    }

    // Add pagination and sorting
    const offset = (page - 1) * limit;
    query = query
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: transactions, error: transactionsError, count } = await query;

    if (transactionsError) {
      console.error("Error fetching orders:", transactionsError);
      return NextResponse.json(
        { error: "Failed to fetch orders" },
        { status: 500 }
      );
    }

    // Process orders and check license binding status
    const orders: OrderItem[] = [];
    
    for (const transaction of transactions || []) {
      const metadata = transaction.metadata || {};
      let licenseDetails: LicenseDetails | undefined;
      
      // If this transaction has a generated license, check its binding status
      if (metadata.generated_license_id) {
        const { data: license, error: licenseError } = await adminClient
          .from("licenses")
          .select(`
            id,
            status,
            license_bindings (
              store_id,
              expires_at,
              status,
              stores (
                wb_supplier_id,
                store_name
              )
            )
          `)
          .eq("id", metadata.generated_license_id)
          .single();

        if (!licenseError && license) {
          const binding = license.license_bindings?.[0];
          licenseDetails = {
            id: license.id,
            status: license.status,
            expires_at: binding?.expires_at,
            bound_to_store: binding?.stores?.[0]?.wb_supplier_id || undefined
          };
        }
      }

      orders.push({
        id: transaction.id,
        out_trade_no: transaction.out_trade_no,
        product_name: metadata.product_name || "Unknown Product",
        amount: transaction.amount.toString(),
        status: transaction.status,
        payment_method: transaction.payment_method,
        created_at: transaction.created_at,
        license: licenseDetails
      });
    }

    // Calculate pagination info
    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    const pagination: PaginationInfo = {
      total: totalCount,
      page: page,
      limit: limit,
      total_pages: totalPages
    };

    const response: OrdersResponse = {
      orders: orders,
      pagination: pagination
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error("Orders API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 