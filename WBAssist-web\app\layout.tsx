import './css/style.css'

import { Inter } from 'next/font/google'
import localFont from 'next/font/local'
import ExtensionBridge from '@/components/ExtensionBridge'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap'
})

const cabinet = localFont({
  src: [
    {
      path: '../public/fonts/CabinetGrotesk-Medium.woff2',
      weight: '500',
    },
    {
      path: '../public/fonts/CabinetGrotesk-Bold.woff2',
      weight: '700',
    },
    {
      path: '../public/fonts/CabinetGrotesk-Extrabold.woff2',
      weight: '800',
    },
  ],
  variable: '--font-cabinet-grotesk',
  display: 'swap',
})

export const metadata = {
  title: 'WBAssist - Wildberries自动预约系统',
  description: 'WBAssist是专业的Wildberries仓位预约助手，智能化管理您的供货计划，提升预约成功率',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={`${inter.variable} ${cabinet.variable} font-inter antialiased bg-white text-gray-800 tracking-tight`}>
        <div className="flex flex-col min-h-screen overflow-hidden">
          {children}
        </div>


        {/* 插件通信桥接组件 */}
        <ExtensionBridge />
      </body>
    </html>
  )
}
