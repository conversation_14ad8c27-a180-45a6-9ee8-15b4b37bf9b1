# ==============================================
# WBAssist 必需环境变量配置
# 请将此内容复制到 .env.local 文件中
# ==============================================

# ✅ 必需 - Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://cdnpddlbecxqnpkfkvid.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNkbnBkZGxiZWN4cW5wa2ZrdmlkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ1ODU3NDUsImV4cCI6MjA3MDE2MTc0NX0.zceDPlJvR9-3unj5JrSbcQRdm0IXFMd3fZ0qy3zt5XE
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# ✅ 必需 - 网站基础URL（用于支付回调）
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# ✅ 必需 - ZPay 支付配置
ZPAY_PID=your_zpay_merchant_id
ZPAY_KEY=your_zpay_secret_key

# ==============================================
# 注意事项：
# 1. 只需要填写上面6个环境变量即可
# 2. SUPABASE_SERVICE_ROLE_KEY 从 Supabase Dashboard > Settings > API 获取
# 3. ZPAY_PID 和 ZPAY_KEY 从 ZPay 商户后台获取
# 4. 其他环境变量（NODE_ENV、CORS等）都不是必需的
# 5. Edge Functions 会自动获得 SUPABASE_URL 等配置，无需重复设置
# ============================================== 