// 主应用程序
// 纯前端订单交互界面

class WildberriesBookingApp {
    constructor() {
        this.isLoading = false;
        this.isInitialized = false;
        this.isCheckingAuth = false; // 防止重复认证检查
        this.currentStore = null;
        this.currentPage = 1; // 初始化当前页码
        this.processingActions = new Set(); // 防重复操作
        this.pendingStoreUpdate = null; // 待更新的店铺信息

        this.init();
    }

    async init() {
        this.showPageLoader();
        this.bindGlobalEvents();

        // 先进行认证检查，认证成功后会自动触发应用初始化
        await this.performAuthCheck();
        
        // 注意：不再在这里调用 initializeApp()
        // 因为 checkWBAssistAuth() 中会根据认证状态决定是否初始化应用
    }
    // 初始化店铺选择器
    async initStoreSelector() {
        const storeSelect = document.getElementById('storeSelect');
        if (!storeSelect) {
            console.warn('⚠️ 店铺选择器元素未找到，HTML结构可能还未准备好');
            return;
        }
        
        console.log('🔄 开始初始化店铺选择器');

        // 清空现有选项
        storeSelect.innerHTML = '<option value="">选择店铺...</option>';

        // 获取店铺数据（从真实API获取）
        const stores = window.realDataService.getStores();
        stores.forEach(store => {
            const option = document.createElement('option');
            option.value = store.id;
            option.textContent = store.displayName;
            storeSelect.appendChild(option);
        });

        // 默认选择第一个店铺
        if (stores.length > 0) {
            storeSelect.value = stores[0].id;
            
            // 直接更新store显示
            const store = window.realDataService.setCurrentStore(stores[0].id);
            
            // 确保权限信息已加载
            if (!this.storeCapabilities?.[store.id]) {
                try {
                    await this.checkStoreCapabilities(store.id);
                } catch (error) {
                    console.warn(`加载当前店铺权限失败:`, error);
                }
            }
            
            await this.updateStoreDisplay(store);
            console.log('✅ 店铺选择器初始化完成，默认选择:', store.displayName);
        } else {
            console.log('⚠️ 没有可用的店铺数据');
        }
    }

    // 显示页面加载器
    showPageLoader() {
        // 隐藏除了header之外的所有内容
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.style.display = 'none';
        }

        // 检查是否已经存在loader，如果存在就直接显示
        let loader = document.getElementById('page-loader');
        if (loader) {
            loader.style.display = 'flex';
            loader.style.opacity = '1';
            console.log('✅ 重新显示现有的页面加载器');
            return;
        }

        // 创建新的loader
        loader = document.createElement('div');
        loader.id = 'page-loader';
        loader.className = 'page-loader';
        loader.innerHTML = `
            <div class="loader-content">
                <div class="loader-spinner"></div>
                <div class="loader-text">正在初始化插件...</div>
                <div class="loader-steps">
                    <div class="loader-step" id="step-auth">验证登录状态</div>
                    <div class="loader-step" id="step-stores">加载店铺信息</div>
                    <div class="loader-step" id="step-permissions">检查店铺权限</div>
                    <div class="loader-step" id="step-orders">加载订单数据</div>
                </div>
            </div>
        `;
        document.body.appendChild(loader);
        console.log('✅ 创建新的页面加载器');
    }

    // 更新加载器步骤状态
    updateLoaderStep(stepId, status = 'active') {
        const step = document.getElementById(stepId);
        if (step) {
            // 保存原始文本内容（如果还没有保存过）
            if (!step.dataset.originalText) {
                step.dataset.originalText = step.textContent;
            }
            
            step.className = `loader-step ${status}`;
            const originalText = step.dataset.originalText;
            
            if (status === 'completed') {
                step.innerHTML = `<i class="fas fa-check"></i> ${originalText}`;
            } else if (status === 'error') {
                step.innerHTML = `<i class="fas fa-times"></i> ${originalText}`;
            } else if (status === 'active') {
                step.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${originalText}`;
            }
        } else {
            console.log(`⚠️ 加载步骤 ${stepId} 元素未找到（可能加载器已隐藏）`);
        }
    }

    // 重置所有加载器步骤状态
    resetLoaderSteps() {
        const stepIds = ['step-auth', 'step-stores', 'step-permissions', 'step-orders'];
        stepIds.forEach(stepId => {
            const step = document.getElementById(stepId);
            if (step && step.dataset.originalText) {
                step.className = 'loader-step';
                step.innerHTML = step.dataset.originalText;
            }
        });
    }

    // 隐藏页面加载器
    hidePageLoader() {
        console.log('🔄 开始隐藏页面加载器...');
        const loader = document.getElementById('page-loader');
        if (loader) {
            console.log('✅ 找到页面加载器，开始隐藏动画');
            loader.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(loader)) {
                    document.body.removeChild(loader);
                    console.log('✅ 页面加载器已从DOM中移除');
                } else {
                    console.log('⚠️ 页面加载器已不在DOM中');
                }
                // 显示主要内容
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.style.display = 'block';
                    mainContent.style.animation = 'fadeIn 0.5s ease-in';
                    console.log('✅ 主要内容已显示');
                } else {
                    console.log('❌ 未找到主要内容元素');
                }
            }, 300);
        } else {
            console.log('❌ 未找到页面加载器元素');
            // 即使没有找到loader，也要确保显示主要内容
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.display = 'block';
                console.log('✅ 直接显示主要内容（loader不存在）');
            }
        }
    }

    // 绑定全局事件
    bindGlobalEvents() {
        // 店铺选择变化
        const storeSelect = document.getElementById('storeSelect');
        if (storeSelect && !storeSelect.hasAttribute('data-event-bound')) {
            storeSelect.addEventListener('change', () => this.handleStoreChange());
            storeSelect.setAttribute('data-event-bound', 'true');
        }

        // 状态筛选变化
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter && !statusFilter.hasAttribute('data-event-bound')) {
            statusFilter.addEventListener('change', () => this.handleStatusFilter());
            statusFilter.setAttribute('data-event-bound', 'true');
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshOrdersBtn');
        if (refreshBtn && !refreshBtn.hasAttribute('data-event-bound')) {
            refreshBtn.addEventListener('click', () => this.refreshOrders());
            refreshBtn.setAttribute('data-event-bound', 'true');
        }

        // 分页按钮
        const prevPage = document.getElementById('prevPage');
        const nextPage = document.getElementById('nextPage');
        if (prevPage && !prevPage.hasAttribute('data-event-bound')) {
            prevPage.addEventListener('click', () => this.handlePrevPage());
            prevPage.setAttribute('data-event-bound', 'true');
        }
        if (nextPage && !nextPage.hasAttribute('data-event-bound')) {
            nextPage.addEventListener('click', () => this.handleNextPage());
            nextPage.setAttribute('data-event-bound', 'true');
        }

        // 绑定订单操作按钮事件（使用事件委托）
        this.bindOrderActionEvents();

        // 预约状态变化监听
        window.addEventListener('bookingStatusChanged', (event) => {
            this.handleBookingStatusChanged(event.detail);
        });

        // 监控状态变化监听（来自后台脚本）
        window.addEventListener('monitoringStatusChanged', (event) => {
            this.handleMonitoringStatusChanged(event.detail);
        });

        // 监听来自后台脚本的消息
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                if (message.action === 'monitoringStatusChanged') {
                    // 将后台消息转换为前端事件
                    window.dispatchEvent(new CustomEvent('monitoringStatusChanged', {
                        detail: {
                            orderId: message.orderId,
                            status: message.status,
                            ...message.data
                        }
                    }));
                }
            });
        }
    }

    // 初始化应用
    async initializeApp() {
        try {
            // 首先检查是否已通过WBAssist认证
            if (!window.wbAssistApiService.isAuthenticated) {
                console.log('用户未通过WBAssist认证，跳过应用初始化');
                this.updateLoaderStep('step-auth', 'error');
                return;
            }
            this.updateLoaderStep('step-auth', 'completed');

            // 初始化预约状态存储服务
            await window.bookingStorage.initialize();

            // 初始化真实数据服务
            await window.realDataService.initialize();

            // 同步WB店铺到WBAssist系统
            this.updateLoaderStep('step-stores', 'active');
            await this.syncStoresWithBackend();
            this.updateLoaderStep('step-stores', 'completed');

            // 注意：店铺选择器初始化将在showMainInterface之后进行，以确保DOM元素已准备好

            // 初始化分页信息
            this.updatePagination({
                page: this.currentPage,
                totalPages: 1
            });

            // 注意：订单加载将在showMainInterface之后进行，以确保DOM元素已准备好

            // 设置初始化标志
            this.isInitialized = true;
            console.log('✅ 应用初始化完成，设置初始化标志');

            // 监听预约状态变化
            if (window.bookingStorage && window.bookingStorage.isAvailable()) {
                window.bookingStorage.addListener((_changedKeys) => {
                    console.log('🔄 检测到预约状态变化，重新加载订单，changedKeys:', _changedKeys);
                    // 清除缓存以确保获取最新状态
                    if (window.realDataService) {
                        window.realDataService.clearCache();
                    }
                    // 重新加载订单以更新状态显示
                    this.loadOrders();
                });
            }

            // 监听认证状态变化
            window.addEventListener('wbassist-auth-updated', async (event) => {
                console.log('🔔 收到认证状态更新事件:', event.detail);
                
                if (event.detail.authenticated) {
                    console.log('📱 用户已登录，触发认证检查和数据加载...');
                    
                    // 🔧 避免重复调用，直接触发数据刷新
                    if (!this.isCheckingAuth) {
                    await this.checkWBAssistAuth();
                    } else {
                        console.log('⚠️ 认证检查正在进行中，跳过事件触发的检查');
                    }
                } else {
                    console.log('👋 用户已登出，显示登录提示');
                    this.showWBAssistAuthStatus(false);
                    this.isInitialized = false;
                    this.resetLoaderSteps();
                    this.showLoginPrompt();
                }
            });

            // 监听云端任务取消请求（来自后台脚本）
            this.setupCloudCancelListener();

        // 设置页面标题显示当前状态
        document.title = 'WBAssist 自动预约系统 - 已就绪';

        // 检查licenseManager是否正确初始化
        this.checkLicenseManagerStatus();

        // 添加卡密管理按钮的点击测试
        this.testLicenseButton();

        // 先显示主界面（但不隐藏加载器），然后加载订单
        await this.showMainInterface();
        
        // 🔧 在主界面显示后，加载订单数据（确保DOM元素已准备好）
        this.updateLoaderStep('step-orders', 'active');
        try {
            await this.loadOrders(true);
            this.updateLoaderStep('step-orders', 'completed');
            console.log('✅ 订单数据加载完成（在主界面显示后）');
        } catch (error) {
            console.error('❌ 订单数据加载失败:', error);
            this.updateLoaderStep('step-orders', 'error');
        }
        
        // 最后隐藏加载器
        this.hidePageLoader();
        
        // 🔧 双重保险：确保加载器一定被隐藏
        setTimeout(() => {
            const loader = document.getElementById('page-loader');
            if (loader) {
                console.log('⚠️ 检测到加载器仍然存在，强制移除');
                loader.remove();
            }
            // 确保主要内容可见
            const mainContent = document.querySelector('.main-content');
            if (mainContent && mainContent.style.display === 'none') {
                mainContent.style.display = 'block';
                console.log('🔧 强制显示主要内容');
            }
        }, 1000);

            // 静默运行数据一致性检查（延迟执行，不影响初始化性能）
            setTimeout(() => {
                this.checkDataConsistency();
            }, 5000);
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showError('应用初始化失败，请刷新页面重试');
            document.title = 'WBAssist 自动预约系统 - 初始化失败';
            
            // 显示错误状态
            this.updateLoaderStep('step-orders', 'error');
            setTimeout(async () => {
                this.hidePageLoader();
                await this.showMainInterface(); // 即使出错也显示界面，让用户可以重试
            }, 2000);
        }
    }

    // 同步WB店铺到WBAssist后端系统
    async syncStoresWithBackend() {
        try {
            console.log('开始同步店铺到后端系统...');
            
            // 获取WB认证数据
            const authData = await this.sendMessageToBackground('getAuthData');
            if (!authData.success || !authData.data) {
                console.log('未获取到WB认证数据，跳过店铺同步');
                return;
            }

            // 调用后端API同步店铺
            const result = await window.wbAssistApiService.syncStores(
                authData.data.Authorizev3,
                authData.data.wbxValidKey
            );

            if (result.success) {
                console.log('店铺同步成功:', result.message);
                
                // 🎯 v4.0 全局权限共享：处理新的返回格式
                const existingStores = result.existing_stores || [];
                const newStores = result.new_stores || [];
                const allStores = [...existingStores, ...newStores];
                
                console.log('🔍 同步结果分析:', {
                    total_stores: allStores.length,
                    existing_shared_stores: existingStores.filter(s => s.is_shared).length,
                    new_stores: newStores.length,
                    sync_strategy: result.sync_strategy || 'unknown'
                });

                // 分类显示同步信息
                if (existingStores.length > 0) {
                    const sharedStores = existingStores.filter(s => s.is_shared);
                    const ownStores = existingStores.filter(s => !s.is_shared);
                    
                    if (sharedStores.length > 0) {
                        console.log('✦ 共享权限店铺:', sharedStores.map(s => ({
                            name: s.store_name,
                            wb_supplier_id: s.wb_supplier_id,
                            license_type: s.license_type,
                            remaining_quota: s.remaining_quota,
                            original_owner: s.owner_user_id
                        })));
                    }
                    
                    if (ownStores.length > 0) {
                        console.log('👤 自有店铺:', ownStores.map(s => ({
                            name: s.store_name,
                            wb_supplier_id: s.wb_supplier_id,
                            license_type: s.license_type,
                            remaining_quota: s.remaining_quota
                        })));
                    }
                }
                
                if (newStores.length > 0) {
                    console.log('🆕 新增店铺（免费版）:', newStores.map(s => ({
                        name: s.store_name,
                        wb_supplier_id: s.wb_supplier_id,
                        license_type: s.license_type,
                        remaining_quota: s.remaining_quota
                    })));
                }
                
                // 显示增强的同步结果通知
                let notificationMessage = result.message;
                const sharedCount = existingStores.filter(s => s.is_shared).length;
                if (sharedCount > 0) {
                    notificationMessage += `\n✦ 其中 ${sharedCount} 个店铺为共享权限`;
                }
                this.showNotification(notificationMessage, 'success');
                
                // 为每个店铺检查权限状态（等待完成，确保权限信息加载完整）
                const storeCapabilityPromises = allStores.map(async (store) => {
                    try {
                        await this.checkStoreCapabilities(store.wb_supplier_id);
                    } catch (error) {
                        console.warn(`检查店铺 ${store.wb_supplier_id} 权限失败:`, error);
                    }
                });
                
                // 🚀 等待所有权限信息加载完成
                await Promise.allSettled(storeCapabilityPromises);
                console.log('✅ 所有店铺权限信息加载完成');

                // 🎯 兼容处理：为了保持现有代码兼容，也设置synchronized_stores字段
                if (!result.synchronized_stores) {
                    result.synchronized_stores = allStores;
                }
                
            } else {
                console.error('店铺同步失败:', result.message);
                this.showNotification('店铺同步失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('店铺同步过程出错:', error);
            this.showNotification('店铺同步失败: ' + error.message, 'error');
        }
    }

    // 检查店铺权限和配额（适配v4.0全局权限共享）
    async checkStoreCapabilities(wbSupplierId) {
        try {
            const capabilities = await window.wbAssistApiService.getStoreCapabilities(wbSupplierId);
            
            // 🎯 v4.0 增强日志：显示权限共享状态
            const sharingInfo = capabilities.sharing_info || {};
            const storeInfo = capabilities.store || {};
            const licenseInfo = capabilities.license_binding || {};
            
            console.log('🔍 店铺权限详情:', {
                store_name: storeInfo.store_name,
                wb_supplier_id: storeInfo.wb_supplier_id,
                license_type: licenseInfo.license_type,
                remaining_quota: licenseInfo.remaining_quota,
                has_valid_license: capabilities.has_valid_license,
                // 🎯 权限共享状态
                is_shared_access: sharingInfo.is_shared_access,
                sharing_type: sharingInfo.sharing_type,
                original_owner: sharingInfo.original_owner_id,
                current_user: sharingInfo.current_user_id,
                can_use_quota: sharingInfo.can_use_quota
            });

            // ✦ 如果是共享权限，显示特殊提示
            if (sharingInfo.is_shared_access) {
                console.log(`✦ 店铺 "${storeInfo.store_name}" 为共享权限访问:`, {
                    sharing_type: sharingInfo.sharing_type,
                    quota_available: licenseInfo.remaining_quota,
                    can_create_task: capabilities.quota_status?.can_create_task
                });
            } else {
                console.log(`👤 店铺 "${storeInfo.store_name}" 为自有权限:`, {
                    license_type: licenseInfo.license_type,
                    quota_available: licenseInfo.remaining_quota
                });
            }
            
            // 保存权限信息以供后续使用
            if (!this.storeCapabilities) {
                this.storeCapabilities = {};
            }
            this.storeCapabilities[wbSupplierId] = capabilities;
            
            // 如果当前店铺就是这个店铺，更新显示
            const currentStore = window.realDataService.getCurrentStore();
            if (currentStore && currentStore.id === wbSupplierId) {
                await this.updateStoreDisplay(currentStore);
            }
            
            return capabilities;
        } catch (error) {
            console.error('获取店铺权限失败:', error);
            
            // 如果是404错误，说明店铺或卡密未绑定
            if (error.message.includes('店铺或卡密未绑定') || error.message.includes('404')) {
                console.log(`店铺 ${wbSupplierId} 未绑定卡密，可能需要用户手动绑定`);
            }
            
            return null;
        }
    }

    // 处理店铺选择变化
    async handleStoreChange() {
        const storeSelect = document.getElementById('storeSelect');
        const storeId = storeSelect.value;

        if (storeId) {
            const store = window.realDataService.setCurrentStore(storeId);
            await this.updateStoreDisplay(store);

            // 切换店铺时，重置页码
            this.currentPage = 1;

            // 如果在扩展环境中，切换到指定店铺（按照2.md的方式）
            if (window.wbApiService && window.wbApiService.isAvailable()) {
                try {
                    await window.wbApiService.switchToSupplier(store.id);
                    this.showNotification(`已切换到店铺: ${store.displayName}`, 'success');
                } catch (error) {
                    console.warn('切换店铺失败，使用本地数据:', error);
                }
            }

            await this.loadOrders();
        }
    }

    // 更新店铺信息显示（包含权限信息，但避免loader后闪烁）
    async updateStoreDisplay(store) {
        if (!store) {
            return;
        }

        // 🚀 更新基本店铺信息（添加元素存在性检查）
        const storeNameElement = document.getElementById('storeName');
        const storeInnElement = document.getElementById('storeInn');
        
        if (!storeNameElement || !storeInnElement) {
            console.warn('⚠️ 店铺显示元素未找到，HTML结构还未加载完成，将在showMainInterface后重试');
            // 保存店铺信息，待HTML结构准备好后再更新（保存最新的店铺信息）
            this.pendingStoreUpdate = store;
            console.log('📝 已保存待更新的店铺信息:', store.displayName, '(当前店铺ID:', store.id, ')');
            return;
        }
        
        console.log('🔄 开始更新店铺显示信息:', store.displayName);
        
        storeNameElement.textContent = store.displayName || '-';
        storeInnElement.textContent = store.inn || '-';
        
        // 🚀 显示权限信息（如果已获取到）
        const capabilities = this.storeCapabilities?.[store.id];
        if (capabilities) {
            this.updateStorePermissionDisplay(capabilities);
        } else {
            // 如果没有权限信息，显示默认状态
            this.showDefaultStoreState();
        }
    }

    // 更新店铺权限显示（现代化设计 + v4.0权限共享支持）
    updateStorePermissionDisplay(capabilities) {
        const planText = document.getElementById('storePlan');
        const planBadge = document.getElementById('storePlanBadge');
        const planIcon = document.getElementById('storePlanIcon');
        const expirationElement = document.getElementById('storeExpiration');
        const creditsElement = document.getElementById('remainingCredits');
        
        if (capabilities.has_valid_license && capabilities.license_binding) {
            const license = capabilities.license_binding;
            const sharingInfo = capabilities.sharing_info || {};
            
            // 徽章图标显示设计
            if (planText && planBadge) {
                const licenseInfo = this.getLicenseTypeInfo(license.license_type);
                
                // ✦ 权限共享显示逻辑
                let displayName = licenseInfo.name;
                let additionalClass = '';
                
                if (sharingInfo.is_shared_access) {
                    displayName = `${licenseInfo.name} ✦`;  // 添加四角星共享图标
                    additionalClass = ' shared-access';
                    console.log('✦ 显示共享权限标识');
                }
                
                // 应用统一极简样式
                planBadge.className = `plan-badge-with-icon ${license.license_type.toLowerCase()}${additionalClass}`;
                planBadge.style.cssText = `
                    background: ${licenseInfo.bgColor};
                    box-shadow: none;
                    border: none;
                    color: ${licenseInfo.textColor};
                    font-weight: 600;
                    ${licenseInfo.opacity ? `opacity: ${licenseInfo.opacity};` : ''}
                    ${sharingInfo.is_shared_access ? `
                        background: linear-gradient(135deg, ${licenseInfo.bgColor}, #3b82f6);
                        background-size: 200% 200%;
                        animation: gradient 3s ease infinite;
                    ` : ''}
                `;
                planText.textContent = displayName;
                
                // ✦ 为共享权限添加tooltip提示
                if (sharingInfo.is_shared_access) {
                    planBadge.title = `共享权限访问\n权限类型: ${sharingInfo.sharing_type || 'global_store_permission'}\n可使用配额: ${sharingInfo.can_use_quota ? '是' : '否'}`;
                } else {
                    planBadge.title = `自有权限`;
                }
            }
            
            // 应用图标样式（点缀色设计）
            if (planIcon) {
                const licenseInfo = this.getLicenseTypeInfo(license.license_type);
                planIcon.className = `plan-icon ${licenseInfo.icon}`;
                
                // ✦ 共享权限图标特殊处理
                if (sharingInfo.is_shared_access) {
                    planIcon.style.color = '#3b82f6';  // 蓝色表示共享
                    planIcon.title = '共享权限';
                } else {
                    // 专业版和旗舰版的图标使用特殊颜色（金色点缀）
                    if (licenseInfo.iconColor) {
                        planIcon.style.color = licenseInfo.iconColor;
                    }
                    planIcon.title = '自有权限';
                }
            }
            
            // 显示剩余天数（简洁设计，去除背景色）
            if (expirationElement) {
                const expiryDate = new Date(license.expires_at);
                const now = new Date();
                const diffTime = expiryDate - now;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                
                if (diffDays > 0) {
                    expirationElement.style.cssText = `
                        color: ${diffDays > 7 ? '#059669' : '#d97706'};
                        font-weight: 600;
                        font-size: 14px;
                    `;
                    expirationElement.textContent = diffDays.toString();
                } else {
                    expirationElement.style.cssText = `
                        color: #dc2626;
                        font-weight: 600;
                        font-size: 14px;
                    `;
                    expirationElement.textContent = '已过期';
                }
                
                // ✦ 共享权限的有效期提示
                if (sharingInfo.is_shared_access) {
                    expirationElement.title = `共享权限有效期（原始绑定用户的权限）`;
                }
            }
            
            // 显示剩余次数
            if (creditsElement) {
                const remaining = license.monthly_quota - license.current_month_used;
                creditsElement.style.color = remaining > 0 ? 'var(--text-primary)' : '#dc2626';
                creditsElement.style.fontWeight = '600';
                
                if (license.monthly_quota === 999) {
                    creditsElement.textContent = '无限制';
                } else {
                    creditsElement.textContent = remaining.toString();
                }
                
                // ✦ 共享权限的配额提示
                if (sharingInfo.is_shared_access) {
                    creditsElement.title = `共享配额（所有用户共用）\n使用权限: ${sharingInfo.can_use_quota ? '可用' : '不可用'}`;
                } else {
                    creditsElement.title = `个人配额`;
                }
            }

            // ✦ 在控制台输出权限共享状态
            if (sharingInfo.is_shared_access) {
                console.log('✦ 当前显示共享权限店铺:', {
                    store_name: capabilities.store?.store_name,
                    license_type: license.license_type,
                    remaining_quota: license.monthly_quota - license.current_month_used,
                    sharing_type: sharingInfo.sharing_type,
                    can_use_quota: sharingInfo.can_use_quota
                });
            }
            
        } else {
            this.showDefaultStoreState();
        }
    }
    
    // 显示默认店铺状态（现代化设计）
    showDefaultStoreState() {
        const planText = document.getElementById('storePlan');
        const planBadge = document.getElementById('storePlanBadge');
        const planIcon = document.getElementById('storePlanIcon');
        const expirationElement = document.getElementById('storeExpiration');
        const creditsElement = document.getElementById('remainingCredits');
        
        // 未激活状态显示（灰化处理）
        if (planText && planBadge) {
            const licenseInfo = this.getLicenseTypeInfo('default');
            planBadge.className = 'plan-badge-with-icon free';
            planBadge.style.cssText = `
                background: ${licenseInfo.bgColor};
                box-shadow: none;
                border: none;
                color: ${licenseInfo.textColor};
                font-weight: 600;
                opacity: ${licenseInfo.opacity};
            `;
            planText.textContent = licenseInfo.name;
        }
        
        // 应用图标样式（红色警示点缀）
        if (planIcon) {
            const licenseInfo = this.getLicenseTypeInfo('default');
            planIcon.className = `plan-icon ${licenseInfo.icon}`;
            // 未绑定状态使用红色警示图标
            if (licenseInfo.iconColor) {
                planIcon.style.color = licenseInfo.iconColor;
            }
        }
        
        // 简洁状态显示
        if (expirationElement) {
            expirationElement.className = 'days-remaining warning';
            expirationElement.textContent = '请绑定卡密';
        }
        
        if (creditsElement) {
            creditsElement.style.color = '#dc2626';
            creditsElement.style.fontWeight = '600';
            creditsElement.textContent = '0';
        }
    }
    
    // 获取许可证类型信息（徽章图标+功能性文字，去背景边框）
    getLicenseTypeInfo(type) {
        switch (type) {
            case 'basic': 
                return { 
                    name: '免费版',
                    gradient: 'none',
                    shadow: 'none',
                    hoverShadow: 'none',
                    border: 'none',
                    icon: 'fas fa-medal',
                    iconColor: '#8D6E63',  // 青铜色图标
                    textColor: '#1a1a1a',
                    bgColor: 'transparent'
                };
            case 'pro': 
                return { 
                    name: '专业版',
                    gradient: 'none',
                    shadow: 'none',
                    hoverShadow: 'none',
                    border: 'none',
                    icon: 'fas fa-trophy',
                    iconColor: '#FFB300',  // 黄金色图标
                    textColor: '#1a1a1a',
                    bgColor: 'transparent'
                };
            case 'ultimate': 
                return { 
                    name: '旗舰版',
                    gradient: 'none',
                    shadow: 'none',
                    hoverShadow: 'none',
                    border: 'none',
                    icon: 'fas fa-gem',
                    iconColor: '#667eea',  // 蓝紫色宝石
                    textColor: '#1a1a1a',
                    bgColor: 'transparent'
                };
            default: 
                return { 
                    name: '未绑定',
                    gradient: 'none',
                    shadow: 'none',
                    hoverShadow: 'none',
                    border: 'none',
                    icon: 'fas fa-ban',
                    iconColor: '#9E9E9E',  // 灰色
                    textColor: '#9E9E9E',  // 灰色文字
                    bgColor: 'transparent',
                    opacity: '0.6'
                };
        }
    }


    // 更新到期时间显示
    updateStoreExpiration(expiresAt) {
        const expirationElement = document.getElementById('storeExpiration');
        
        if (!expiresAt) {
            expirationElement.textContent = '-';
            expirationElement.className = 'days-remaining';
            return;
        }

        const now = new Date();
        const expiryDate = new Date(expiresAt);
        const diffTime = expiryDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) {
            expirationElement.textContent = '已过期';
            expirationElement.className = 'days-remaining expired';
        } else if (diffDays === 0) {
            expirationElement.textContent = '今天到期';
            expirationElement.className = 'days-remaining critical';
        } else if (diffDays <= 7) {
            expirationElement.textContent = diffDays.toString();
            expirationElement.className = 'days-remaining warning';
        } else if (diffDays <= 30) {
            expirationElement.textContent = diffDays.toString();
            expirationElement.className = 'days-remaining active';
        } else {
            expirationElement.textContent = diffDays.toString();
            expirationElement.className = 'days-remaining active';
        }
    }

    // 更新剩余预约次数显示
    updateRemainingCredits(remaining, total) {
        const creditsElement = document.getElementById('remainingCredits');
        
        if (total === 999) {
            // 旗舰版无限制套餐
            creditsElement.textContent = '无限制';
            creditsElement.style.color = 'var(--text-primary)';
            creditsElement.style.fontWeight = '600';
        } else {
            // Pro版等其他版本显示具体数字
            creditsElement.textContent = remaining.toString();
            creditsElement.style.fontWeight = '600';
            
            if (remaining === 0) {
                creditsElement.style.color = '#ff4757';
            } else if (remaining <= total * 0.3) {
                creditsElement.style.color = '#ffa502';
            } else {
                creditsElement.style.color = 'var(--text-primary)';
            }
        }
    }



    // 显示卡密绑定提示
    showLicenseBindHint() {
        // 如果还没有显示过提示，显示一个温和的提示
        if (!this.hasShownLicenseHint) {
            setTimeout(() => {
                this.showNotification('当前店铺未绑定卡密，点击店铺信息可以绑定卡密获得预约权限', 'info', 5000);
                this.hasShownLicenseHint = true;
            }, 2000);
        }
    }

    // 检查licenseManager初始化状态
    checkLicenseManagerStatus() {
        console.log('🔍 检查licenseManager状态:', {
            hasLicenseManager: !!window.licenseManager,
            licenseManagerType: typeof window.licenseManager,
            hasOpenMethod: !!(window.licenseManager?.openLicenseBindModal)
        });

        if (!window.licenseManager) {
            console.error('❌ licenseManager未初始化！');
            // 尝试手动创建
            try {
                if (typeof LicenseManager !== 'undefined') {
                    window.licenseManager = new LicenseManager();
                    window.licenseManager.initialize();
                    console.log('✅ 手动创建licenseManager成功');
                } else {
                    console.error('❌ LicenseManager类不存在');
                }
            } catch (error) {
                console.error('❌ 手动创建licenseManager失败:', error);
            }
        }
    }

    // 测试卡密管理按钮
    testLicenseButton() {
        setTimeout(() => {
            const licenseBtn = document.querySelector('[data-action="open-license-bind-modal"]');
            console.log('🔍 检查卡密管理按钮:', {
                buttonExists: !!licenseBtn,
                buttonVisible: licenseBtn ? licenseBtn.offsetParent !== null : false,
                buttonText: licenseBtn ? licenseBtn.textContent.trim() : 'N/A'
            });

            if (licenseBtn) {
                // 为按钮添加额外的点击监听器作为备用
                licenseBtn.addEventListener('click', (e) => {
                    console.log('🖱️ 备用点击事件触发');
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('🔍 检查licenseManager状态:', {
                        hasLicenseManager: !!window.licenseManager,
                        hasOpenMethod: !!(window.licenseManager?.openLicenseBindModal),
                        licenseManagerType: typeof window.licenseManager
                    });
                    
                    if (window.licenseManager && window.licenseManager.openLicenseBindModal) {
                        console.log('🔑 调用licenseManager.openLicenseBindModal()');
                        try {
                            window.licenseManager.openLicenseBindModal();
                            console.log('✅ 成功调用openLicenseBindModal');
                        } catch (error) {
                            console.error('❌ 调用openLicenseBindModal时出错:', error);
                        }
                    } else {
                        console.error('❌ licenseManager不存在或方法不可用');
                        
                        // 尝试手动打开卡密绑定模态框
                        this.showSimpleLicenseModal();
                    }
                });
                console.log('✅ 已添加备用点击事件监听器');
            }
        }, 1000);
    }

    // 显示简单的卡密绑定模态框（备用方案）
    showSimpleLicenseModal() {
        console.log('🔧 显示简单卡密模态框');
        
        // 创建模态框HTML
        const modalHTML = `
            <div id="simple-license-modal" class="license-modal" style="
                position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex; 
                align-items: center; justify-content: center;
            ">
                <div class="license-modal-content" style="
                    background: white; padding: 24px; border-radius: 8px; 
                    max-width: 400px; width: 90%; box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                ">
                    <h3 style="margin: 0 0 16px 0; color: #333;">绑定卡密</h3>
                    <p style="margin: 0 0 16px 0; color: #666;">请输入您的卡密来激活店铺权限：</p>
                    <input type="text" id="simple-license-input" placeholder="请输入卡密" style="
                        width: 100%; padding: 8px 12px; border: 1px solid #ddd; 
                        border-radius: 4px; margin-bottom: 16px; box-sizing: border-box;
                    ">
                    <div style="text-align: right;">
                        <button onclick="document.getElementById('simple-license-modal').remove()" style="
                            background: #666; color: white; border: none; padding: 8px 16px; 
                            border-radius: 4px; margin-right: 8px; cursor: pointer;
                        ">取消</button>
                        <button onclick="window.app.handleSimpleLicenseBind()" style="
                            background: #2563eb; color: white; border: none; padding: 8px 16px; 
                            border-radius: 4px; cursor: pointer;
                        ">绑定</button>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // 聚焦输入框
        setTimeout(() => {
            const input = document.getElementById('simple-license-input');
            if (input) input.focus();
        }, 100);
    }

    // 处理简单卡密绑定
    async handleSimpleLicenseBind() {
        const input = document.getElementById('simple-license-input');
        const modal = document.getElementById('simple-license-modal');
        
        if (!input || !input.value.trim()) {
            alert('请输入卡密');
            return;
        }
        
        const licenseKey = input.value.trim();
        console.log('🔑 尝试绑定卡密:', licenseKey);
        
        try {
            // 获取当前选中的店铺
            const storeSelect = document.getElementById('storeSelect');
            const wbSupplierId = storeSelect?.value;
            
            if (!wbSupplierId) {
                alert('请先选择店铺');
                return;
            }
            
            // 调用绑定API
            const result = await window.wbAssistApiService.bindLicense(wbSupplierId, licenseKey);
            
            if (result.success) {
                alert('卡密绑定成功！');
                modal.remove();
                
                // 刷新店铺显示
                await this.checkStoreCapabilities(wbSupplierId);
                const store = window.realDataService.getCurrentStore();
                if (store) {
                    await this.updateStoreDisplay(store);
                }
            } else {
                alert('绑定失败：' + (result.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 卡密绑定失败:', error);
            alert('绑定失败：' + error.message);
        }
    }

    // 获取店铺权限信息（复用现有方法）
    async getStoreCapabilities(wbSupplierId) {
        console.log('🔍 开始获取店铺权限:', {
            wbSupplierId,
            hasApiService: !!window.wbAssistApiService,
            isAuthenticated: window.wbAssistApiService?.isAuthenticated
        });

        if (!window.wbAssistApiService || !window.wbAssistApiService.isAuthenticated) {
            console.log('⚠️ API服务未认证，返回null');
            return null;
        }

        try {
            const capabilities = await window.wbAssistApiService.getStoreCapabilities(wbSupplierId);
            console.log('✅ 获取店铺权限成功:', capabilities);
            return capabilities;
        } catch (error) {
            console.error('❌ 获取店铺权限失败:', error);
            throw error;
        }
    }

    // 处理状态筛选
    async handleStatusFilter() {
        await this.loadOrders();
    }

    // 加载订单列表
    async loadOrders(forceRefresh = false) {
        // 防止重复加载
        if (this.isLoading) {
            console.log('⚠️ 订单正在加载中，跳过重复调用');
            return;
        }

        try {
            this.isLoading = true;
            console.log('🔄 开始加载订单数据，forceRefresh:', forceRefresh);

            const statusFilter = document.getElementById('statusFilter');
            const filters = {
                status: statusFilter ? statusFilter.value : '',
                page: this.currentPage || 1,
                pageSize: 10,
                forceRefresh: forceRefresh // 传递强制刷新标志
            };

            console.log('📋 订单加载参数:', filters);

            // 显示加载状态
            const container = document.getElementById('ordersContainer');
            if (container) {
                container.innerHTML = `<div class="loading-orders">正在加载订单数据...</div>`;
                console.log('📺 已显示订单加载状态');
            } else {
                console.warn('⚠️ 未找到ordersContainer元素');
            }

            const result = await window.realDataService.getOrders(filters);
            console.log('✅ 订单数据加载完成，订单数量:', result.orders?.length || 0);
            
            this.renderOrders(result.orders);
            this.updatePagination(result);
            console.log('✅ 订单渲染和分页更新完成');
        } catch (error) {
            console.error('❌ 加载订单失败:', error);
            this.showError('加载订单数据失败');

            // 🔧 错误时清除loading状态并显示错误信息
            const container = document.getElementById('ordersContainer');
            if (container) {
                container.innerHTML = `<div class="error-orders">加载订单失败，请<button onclick="window.app.loadOrders()" class="retry-btn">重试</button></div>`;
            }

            // 错误时也要更新分页信息，避免显示undefined
            this.updatePagination({
                page: this.currentPage || 1,
                totalPages: 1
            });
        } finally {
            this.isLoading = false;
        }
    }

    // 刷新订单列表
    async refreshOrders() {
        const refreshBtn = document.getElementById('refreshOrdersBtn');

        // 防止重复点击
        if (this.isLoading || (refreshBtn && refreshBtn.disabled)) {
            return;
        }

        try {
            // 设置按钮为加载状态
            if (refreshBtn) {
                refreshBtn.disabled = true;
                refreshBtn.classList.add('loading');
            }

            // 清除缓存以确保获取最新数据
            if (window.realDataService) {
                window.realDataService.clearCache();
            }

            // 重新加载订单，保持当前的筛选条件和分页状态（强制从WB API获取）
            await this.loadOrders(true);
        } catch (error) {
            console.error('刷新订单失败:', error);
            this.showError('刷新订单数据失败，请稍后重试');
        } finally {
            // 恢复按钮状态
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.classList.remove('loading');
            }
        }
    }

    // 渲染订单列表
    renderOrders(orders) {
        console.log('🎨 开始渲染订单，订单数量:', orders?.length || 0);
        
        const container = document.getElementById('ordersContainer');
        if (!container) {
            console.error('❌ 未找到ordersContainer元素，无法渲染订单');
            return;
        }

        if (orders.length === 0) {
            console.log('📝 没有订单数据，显示空状态');
            container.innerHTML = '<div class="no-orders">暂无订单数据</div>';
            return;
        }

        console.log('🎨 正在渲染', orders.length, '个订单到容器');
        container.innerHTML = orders.map(order => this.createOrderCard(order)).join('');
        console.log('✅ 订单渲染完成');

        // 注意：不在这里绑定事件，事件委托在初始化时已经设置
    }

    // 绑定订单操作按钮事件（使用事件委托，只绑定一次）
    bindOrderActionEvents() {
        const container = document.getElementById('ordersContainer');
        if (!container) return;

        // 移除可能存在的旧事件监听器
        container.removeEventListener('click', this.orderActionHandler);

        // 创建事件处理器并保存引用
        this.orderActionHandler = async (event) => {
            const button = event.target.closest('[data-action]');
            if (!button) return;

            const action = button.getAttribute('data-action');
            const orderKey = button.getAttribute('data-order-key');

            if (action === 'start-booking') {
                await this.startBooking(orderKey);
            } else if (action === 'cancel-booking') {
                await this.cancelBooking(orderKey);
            }
        };

        // 绑定事件监听器
        container.addEventListener('click', this.orderActionHandler);
    }

    // 创建订单卡片
    createOrderCard(order) {
        const statusClass = order.bookingStatus;
        const statusText = window.realDataService.getStatusText(order.bookingStatus);
        const monitoringInfo = this.getMonitoringInfo(order);

        // 获取原始API数据用于显示详细信息
        const apiData = order._apiData || {};
        const isNotDraft = apiData.statusId !== -1; // 非草稿状态

        // 统一显示供应单号逻辑
        const supplyNumber = this.getSupplyNumber(order);

        return `
            <div class="order-card ${statusClass}" data-order="${order.orderNumber || order.supplyId}">
                <div class="order-header">
                    <div class="order-number">
                        供应单: ${supplyNumber}
                    </div>
                    <div class="order-status ${statusClass}">
                        ${statusText}
                    </div>
                </div>
                ${monitoringInfo ? `
                <div class="monitoring-status">
                    ${monitoringInfo}
                </div>
                ` : ''}
                <div class="order-details">
                    <!-- 基础信息 -->
                    <div class="basic-info">
                        <div class="detail-item">
                            <span class="label">配送类型</span>
                            <span class="value">${order.deliveryType}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">创建日期</span>
                            <span class="value">${order.creationDate}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">仓库</span>
                            <span class="value">${order.warehouse}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">商品数量</span>
                            <span class="value">${order.itemsQuantity}</span>
                        </div>
                    </div>

                    <!-- 扩展信息（仅非草稿状态显示） -->
                    ${isNotDraft ? this.renderNonDraftFields(apiData) : ''}
                </div>
                <div class="order-actions">
                    ${this.getOrderActions(order)}
                </div>
            </div>
        `;
    }

    // 获取统一的供应单号显示（始终显示原始的preorderId）
    getSupplyNumber(order) {
        // 优先级：originalPreorderId > preorderId > orderNumber
        // 注意：不使用supplyId作为供应单号，supplyId是送货单号
        if (order.originalPreorderId) {
            return order.originalPreorderId;
        }
        if (order.preorderId) {
            return order.preorderId;
        }
        if (order.orderNumber && !order.actualSupplyId) {
            // 只有在没有supplyId的情况下才使用orderNumber
            return order.orderNumber;
        }
        return '-';
    }

    // 渲染非草稿状态订单的额外字段
    renderNonDraftFields(apiData) {
        // 根据状态显示不同的详细信息
        const isScheduled = apiData.statusId === 1; // 已预约状态（statusId 1）

        const coefficient = apiData.paidAcceptanceCoefficient === 0 ? '免费' :
                           apiData.paidAcceptanceCoefficient ? `系数 ${apiData.paidAcceptanceCoefficient}` : '-';
        const supplyDate = apiData.supplyDate ?
                          new Date(apiData.supplyDate).toLocaleDateString('zh-CN', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit'
                          }) : '-';
        const acceptanceCost = apiData.acceptanceCost !== undefined ? `${apiData.acceptanceCost} 卢布` : '-';

        // 为供货系数添加颜色样式
        const coefficientClass = this.getCoefficientClass(apiData.paidAcceptanceCoefficient);

        // 为验收成本添加颜色样式
        const costClass = this.getCostClass(apiData.acceptanceCost);

        // 根据状态显示不同的字段
        let additionalFields = '';
        if (isScheduled && apiData.supplyId) {
            // 已预约状态显示送货单号
            additionalFields = `
                <div class="detail-item">
                    <span class="label">送货单号</span>
                    <span class="value">${apiData.supplyId}</span>
                </div>
            `;
        }

        return `
            <div class="extended-info">
                ${additionalFields}
                <div class="detail-item">
                    <span class="label">供货系数</span>
                    <span class="value ${coefficientClass}">${coefficient}</span>
                </div>
                <div class="detail-item">
                    <span class="label">预定日期</span>
                    <span class="value">${supplyDate}</span>
                </div>
                <div class="detail-item">
                    <span class="label">验收成本</span>
                    <span class="value ${costClass}">${acceptanceCost}</span>
                </div>
            </div>
        `;
    }

    // 获取供货系数的样式类
    getCoefficientClass(coefficient) {
        if (coefficient === 0) return 'coefficient-free';
        if (coefficient === undefined || coefficient === null) return '';
        if (coefficient <= 5) return 'coefficient-good';
        if (coefficient <= 10) return 'coefficient-ok';
        if (coefficient <= 15) return 'coefficient-warning';
        return 'coefficient-danger';
    }

    // 获取验收成本的样式类
    getCostClass(cost) {
        if (cost === undefined || cost === null || cost === 0) return 'cost-free';
        if (cost > 0) return 'cost-paid';
        return '';
    }

    // 获取监控信息显示
    getMonitoringInfo(order) {
        if (order.bookingStatus !== 'booking') {
            return null; // 只有预约中的订单才显示监控信息
        }

        // 这里可以从后台获取实际的监控状态
        return `
            <div class="monitoring-info">
                <div class="monitoring-indicator">
                    <i class="fas fa-search fa-spin"></i>
                    <span>正在监控仓位费用变化...</span>
                </div>
                <div class="monitoring-details">
                    <small>系统将自动搜索符合条件的时段并预约</small>
                </div>
            </div>
        `;
    }

    // 获取订单操作按钮
    getOrderActions(order) {
        const orderKey = order.orderNumber || order.supplyId;
        if (order.bookingStatus === 'not_planned') {
            return `<button class="btn btn-primary" data-action="start-booking" data-order-key="${orderKey}">开始预约</button>`;
        } else if (order.bookingStatus === 'booking') {
            return `<button class="btn btn-secondary" data-action="cancel-booking" data-order-key="${orderKey}" title="取消本地监控和云端任务">取消预约</button>`;
        } else {
            return `<span class="booking-completed">预约已完成</span>`;
        }
    }
    // 开始预约
    async startBooking(orderKey) {
        if (window.bookingWizard) {
            // 使用新的ID查找逻辑，支持ID映射
            const order = window.realDataService.getOrderByAnyId(orderKey);

            let type = 'supply'; // 默认类型
            if (order) {
                // 根据订单实际数据判断类型
                if (order.preorderId && !order.supplyId) {
                    type = 'order'; // 预订单
                } else if (order.supplyId) {
                    type = 'supply'; // 供应单
                } else if (order.orderNumber) {
                    type = 'order'; // 订单号
                }
            } else {
                // 如果没找到订单，使用原有的格式判断逻辑
                const isOrderNumber = /^\d{8,10}$/.test(orderKey) && parseInt(orderKey) > 10000000;
                type = isOrderNumber ? 'order' : 'supply';
            }

            await window.bookingWizard.startBooking(orderKey, type);
        }
    }

    // 取消预约（同时处理本地监控和云端任务）
    async cancelBooking(orderKey) {
        // 防止重复操作
        if (this.processingActions && this.processingActions.has(`cancel-${orderKey}`)) {
            return;
        }

        if (this.processingActions) {
            this.processingActions.add(`cancel-${orderKey}`);
        }

        this.showConfirmDialog('确定要取消预约吗？这将停止本地监控并取消云端任务。', async () => {
            try {
                this.showNotification('正在取消预约...', 'info');

                // 先找到对应的订单和preorderId（用于云端任务取消）
                const order = window.realDataService.getOrderByAnyId(orderKey);
                let preorderId = null;
                
                if (order) {
                    preorderId = order.originalPreorderId || order.preorderId;
                    if (!preorderId && order.orderNumber) {
                        preorderId = order.orderNumber;
                    }
                }

                // 1. 取消本地监控（现有逻辑）
                let localCancelSuccess = false;
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    const response = await new Promise((resolve) => {
                        chrome.runtime.sendMessage({
                            action: 'cancelBooking',
                            orderId: orderKey
                        }, resolve);
                    });

                    localCancelSuccess = response && response.success;
                    if (localCancelSuccess) {
                        console.log('✅ 本地监控已取消');
                    } else {
                        console.warn('⚠️ 本地监控取消失败:', response?.message);
                    }
                } else {
                    // 非扩展环境，直接调用本地服务
                    await window.realDataService.cancelBooking(orderKey);
                    localCancelSuccess = true;
                    console.log('✅ 本地服务预约已取消');
                }

                // 2. 取消云端任务（如果有WBAssist认证且能找到preorderId）
                let cloudCancelSuccess = false;
                if (window.wbAssistApiService && window.wbAssistApiService.isAuthenticated && preorderId) {
                    try {
                        // 获取任务列表，找到对应的活跃任务
                        const tasks = await window.wbAssistApiService.getTasks();
                        const activeTask = tasks.tasks.find(task => 
                            task.wb_preorder_id === String(preorderId) && 
                            ['pending', 'running'].includes(task.status)
                        );

                        if (activeTask) {
                            console.log(`🔄 找到活跃的云端任务: ${activeTask.id}`);
                            
                            // 调用失败上报API来取消云端任务
                            await window.wbAssistApiService.reportTaskFailure(
                                activeTask.id,
                                'cancelled',
                                {
                                    error_code: 'USER_CANCELLED_LOCAL',
                                    error_message: '用户从插件界面取消预约',
                                    cancelled_by: 'user_local_interface',
                                    cancelled_reason: '用户主动取消预约任务'
                                }
                            );
                            
                            cloudCancelSuccess = true;
                            console.log('✅ 云端任务已取消');
                        } else {
                            console.log('ℹ️ 未找到活跃的云端任务');
                            cloudCancelSuccess = true; // 没有任务也算成功
                        }
                    } catch (cloudError) {
                        console.error('❌ 云端任务取消失败:', cloudError);
                        // 云端取消失败不影响整体操作，只记录日志
                    }
                } else {
                    console.log('ℹ️ 未登录WBAssist或无法确定preorderId，跳过云端任务取消');
                    cloudCancelSuccess = true; // 没有云端认证也算成功
                }

                // 3. 根据取消结果显示通知
                if (localCancelSuccess) {
                    if (cloudCancelSuccess) {
                        this.showNotification('预约已完全取消（本地监控和云端任务）', 'success');
                    } else {
                        this.showNotification('本地预约已取消，云端任务取消可能失败', 'warning');
                    }
                } else {
                    this.showNotification('取消预约失败', 'error');
                }

                // 4. 刷新订单列表
                await this.loadOrders();

            } catch (error) {
                console.error('取消预约失败:', error);
                this.showNotification(`取消预约失败: ${error.message}`, 'error');
            }

            // 清除处理标记
            if (this.processingActions) {
                this.processingActions.delete(`cancel-${orderKey}`);
            }
        }, () => {
            // 取消时也要清除处理标记
            if (this.processingActions) {
                this.processingActions.delete(`cancel-${orderKey}`);
            }
        });
    }

    // 处理预约状态变化
    async handleBookingStatusChanged(detail) {
        await this.loadOrders();

        if (detail.status === 'completed') {
            this.showNotification('预约成功！', 'success');
        } else if (detail.status === 'failed') {
            this.showNotification('预约失败，请重试', 'error');
        }
    }

    // 处理监控状态变化（来自后台脚本）
    async handleMonitoringStatusChanged(detail) {
        const { orderId, status, optimalSlot, bookingResult, error, newSupplyId } = detail;

        // 如果预约成功并且有新的supplyId，需要处理ID转换
        if ((status === 'accepted' || status === 'scheduled') && newSupplyId && newSupplyId !== orderId) {
            // 处理从preorderId到supplyId的转换
            await window.realDataService.handleOrderIdTransition(orderId, newSupplyId);
        }

        // 刷新订单列表以显示最新状态
        await this.loadOrders();

        // 根据不同状态显示相应的通知
        switch (status) {
            case 'found':
                if (optimalSlot) {
                    const date = new Date(optimalSlot.date).toLocaleDateString();
                    const coefficient = optimalSlot.coefficient === 0 ? '免费' : `系数${optimalSlot.coefficient}`;
                    this.showNotification(`订单 ${orderId} 找到符合条件的时段：${date} (${coefficient})，正在自动预约...`, 'info');
                }
                break;

            case 'scheduled':
            case 'completed':
                if (bookingResult && bookingResult.success) {
                    // 获取订单详细信息用于丰富通知
                    const orderDetails = this.getOrderDetails(orderId);
                    
                    // 使用与上面 'found' 状态相同的日期格式化逻辑
                    const bookedDate = optimalSlot ? 
                        new Date(optimalSlot.date).toLocaleDateString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit', 
                            day: '2-digit'
                        }).replace(/\//g, '/') : '未知日期';

                    if (orderDetails) {
                        // 使用丰富的通知格式
                        const richNotification = this.formatRichBookingSuccessNotification(orderDetails, bookedDate);
                        this.showRichNotification(richNotification, 'success');
                    } else {
                        // 回退到简单格式
                        this.showNotification(`订单 ${orderId} 预约成功！预约日期：${bookedDate}`, 'success');
                    }
                } else {
                    this.showNotification(`订单 ${orderId} 自动预约失败，请手动重试`, 'error');
                }
                break;

            case 'failed':
                let errorMsg = error || '监控过程中发生未知错误';
                
                // 过滤Chrome扩展技术错误，显示用户友好的消息
                if (typeof errorMsg === 'string') {
                    if (errorMsg.includes('Frame with ID') && errorMsg.includes('was removed')) {
                        errorMsg = '预约过程中标签页被关闭，预约已停止';
                    } else if (errorMsg.includes('Extension context invalidated') || 
                               errorMsg.includes('Receiving end does not exist')) {
                        errorMsg = '连接中断，请重试预约';
                    }
                }
                
                this.showNotification(`订单 ${orderId} 监控失败：${errorMsg}`, 'error');
                break;

            default:
                break;
        }
    }

    // 处理分页
    async handlePrevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            await this.loadOrders();
        }
    }

    async handleNextPage() {
        this.currentPage++;
        await this.loadOrders();
    }

    // 更新分页信息
    updatePagination(result) {
        const pageInfo = document.getElementById('pageInfo');
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');

        if (pageInfo) {
            pageInfo.textContent = `第 ${result.page} 页，共 ${result.totalPages} 页`;
        }

        if (prevBtn) {
            prevBtn.disabled = result.page <= 1;
        }

        if (nextBtn) {
            nextBtn.disabled = result.page >= result.totalPages;
        }

        this.currentPage = result.page;
    }

    // 获取订单详细信息
    getOrderDetails(orderId) {
        // 从缓存的订单列表中查找订单详情
        if (window.realDataService && window.realDataService.cachedOrders) {
            return window.realDataService.cachedOrders.find(order =>
                order.supplyId == orderId ||
                order.preorderId == orderId ||
                order.actualSupplyId == orderId
            );
        }
        return null;
    }

    // 格式化丰富的预约成功通知
    formatRichBookingSuccessNotification(orderDetails, bookedDate) {
        // 从原始API数据中获取字段，如果没有则使用转换后的字段
        const apiData = orderDetails._apiData || orderDetails;

        const accountName = apiData.supplierAssignName || orderDetails.supplierAssignName || 'ИП Стельмах А. Л.';
        const preorderId = apiData.preorderId || orderDetails.preorderId || '未知';
        const supplyId = apiData.supplyId || orderDetails.actualSupplyId || orderDetails.supplyId || '未知';
        const coefficient = (apiData.paidAcceptanceCoefficient !== undefined ? apiData.paidAcceptanceCoefficient : orderDetails.acceptanceCoefficient) === 0 ? '免费' : `系数 ${apiData.paidAcceptanceCoefficient || orderDetails.acceptanceCoefficient}`;
        const acceptanceCost = apiData.acceptanceCost || orderDetails.acceptanceCost || 0;
        const deliveryType = apiData.boxTypeName || orderDetails.deliveryType || '未知';
        const itemsQuantity = apiData.detailsQuantity || orderDetails.itemsQuantity || 0;
        const warehouse = apiData.warehouseName || orderDetails.warehouse || '未知仓库';

        return `恭喜，您的订单完成预约啦！！
账户：${accountName}
供应单号：${preorderId}
送货单号：${supplyId}
系数：${coefficient}
验收成本：${acceptanceCost} 卢布
配送类型：${deliveryType}
商品数量：${itemsQuantity}
仓库：${warehouse}
预定日期：${bookedDate}`;
    }

    // 显示丰富格式的通知
    showRichNotification(message, type = 'info') {
        // 使用更长的显示时间来展示丰富内容
        this.showToast(message, type, 8000);
    }

    // 显示通知
    showNotification(message, type = 'info') {
        this.showToast(message, type);
    }

    // 显示错误
    showError(message) {
        console.error(message);
        this.showToast(message, 'error');
    }

    // 显示Toast通知
    showToast(message, type = 'info', duration = 3000) {
        // 创建toast容器（如果不存在）
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(toastContainer);
        }

        // 创建toast元素
        const toast = document.createElement('div');
        toast.style.cssText = `
            background: ${type === 'error' ? '#f44336' : type === 'success' ? '#4caf50' : '#2196f3'};
            color: white;
            padding: 16px 20px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            pointer-events: auto;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 400px;
            word-wrap: break-word;
            white-space: pre-line;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        `;
        toast.textContent = message;

        // 添加到容器
        toastContainer.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    // 显示确认对话框
    showConfirmDialog(message, onConfirm, onCancel = null) {
        // 生成唯一ID避免冲突
        const dialogId = 'dialog-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);

        // 创建模态框背景
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        // 创建对话框
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        `;

        dialog.innerHTML = `
            <div style="margin-bottom: 20px; font-size: 16px; color: #333;">${message}</div>
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button class="cancel-btn-${dialogId}" style="
                    padding: 8px 16px;
                    border: 1px solid #ddd;
                    background: white;
                    border-radius: 4px;
                    cursor: pointer;
                    color: #666;
                ">取消</button>
                <button class="confirm-btn-${dialogId}" style="
                    padding: 8px 16px;
                    border: none;
                    background: #2196f3;
                    color: white;
                    border-radius: 4px;
                    cursor: pointer;
                ">确定</button>
            </div>
        `;

        overlay.appendChild(dialog);
        document.body.appendChild(overlay);

        // 显示动画
        setTimeout(() => {
            overlay.style.opacity = '1';
            dialog.style.transform = 'scale(1)';
        }, 10);

        // 绑定事件
        const confirmBtn = dialog.querySelector(`.confirm-btn-${dialogId}`);
        const cancelBtn = dialog.querySelector(`.cancel-btn-${dialogId}`);

        let isProcessing = false; // 防止重复处理

        const closeDialog = () => {
            if (isProcessing) return;
            isProcessing = true;

            overlay.style.opacity = '0';
            dialog.style.transform = 'scale(0.9)';

            // 清理ESC事件监听器
            document.removeEventListener('keydown', handleEsc);

            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        };

        const confirmHandler = () => {
            if (isProcessing) return;
            closeDialog();
            if (onConfirm) onConfirm();
        };

        const cancelHandler = () => {
            if (isProcessing) return;
            closeDialog();
            if (onCancel) onCancel();
        };

        confirmBtn.addEventListener('click', confirmHandler);
        cancelBtn.addEventListener('click', cancelHandler);

        // 点击背景关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                cancelHandler();
            }
        });

        // ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                cancelHandler();
            }
        };
        document.addEventListener('keydown', handleEsc);
    }

    // 执行认证检查
    async performAuthCheck() {
        try {
            this.showAuthStatus('正在验证...', 'info');

            if (typeof chrome === 'undefined' || !chrome.runtime) {
                this.showAuthStatus('需要在扩展环境中运行', 'error');
                return;
            }

            // 1. 首先检查WBAssist后端认证状态
            await this.checkWBAssistAuth();

            // 2. 然后检查WB认证
            const result = await this.sendMessageToBackground('checkAuth');

            if (result.success && result.authenticated) {
                this.showAuthStatus('已连接', 'success');
                const authData = await this.sendMessageToBackground('getAuthData');
                if (authData.success) {
                    this.handleAuthSuccess(authData.data);
                }
            } else {
                this.showAuthStatus('连接失败', 'error');
                this.handleAuthFailure(result);
            }

        } catch (error) {
            this.showAuthStatus('连接失败', 'error');
        }
    }

    // 检查WBAssist后端认证状态
    async checkWBAssistAuth() {
        // 如果已经在检查认证状态，避免重复调用
        if (this.isCheckingAuth) {
            console.log('⚠️ 认证检查已在进行中，跳过重复调用');
            return false;
        }
        
        this.isCheckingAuth = true;
        
        try {
            // 初始化WBAssist API服务
            this.updateLoaderStep('step-auth', 'active');
            const initialized = await window.wbAssistApiService.initializeFromWebsite();
            
            if (initialized) {
                // 验证认证状态
                const authStatus = await window.wbAssistApiService.checkAuthStatus();
                
                if (authStatus.authenticated) {
                    console.log('WBAssist认证成功:', authStatus.user);
                    this.showWBAssistAuthStatus(true, authStatus.user);
                    
                    // 确保在登录成功后重新初始化应用数据，显示完整的初始化流程
                    if (!this.isInitialized) {
                        console.log('🔄 登录成功，开始完整初始化流程...');
                        try {
                            // 重置加载器步骤状态
                            this.resetLoaderSteps();
                            // 确保显示初始化弹窗和步骤
                            this.showPageLoader();
                            await this.initializeApp();
                            console.log('✅ 应用数据初始化完成');
                        } catch (error) {
                            console.error('❌ 应用数据初始化失败:', error);
                            this.showError('数据加载失败，请刷新页面重试');
                            this.hidePageLoader();
                            await this.showMainInterface(); // 即使失败也显示界面
                        }
                    } else {
                        // 这个分支理论上不应该执行，因为重新登录时已经重置了isInitialized
                        console.warn('⚠️ 意外进入已初始化分支，强制重新初始化');
                        this.isInitialized = false;
                        this.resetLoaderSteps();
                        this.showPageLoader();
                        try {
                            await this.initializeApp();
                            console.log('✅ 强制重新初始化完成');
                        } catch (error) {
                            console.error('❌ 强制重新初始化失败:', error);
                            this.showError('数据加载失败，请刷新页面重试');
                            this.hidePageLoader();
                            await this.showMainInterface();
                        }
                    }
                } else {
                    // 检查是否是JWT token过期
                    if (authStatus.expired) {
                        console.log('🔐 JWT token已过期，需要重新登录');
                        // 清理本地状态并显示登录界面
                        this.isInitialized = false;
                        this.resetLoaderSteps();
                        this.showWBAssistAuthStatus(false);
                        this.showLoginPrompt(true); // 传递true表示token过期
                        this.updateLoaderStep('step-auth', 'error');
                        this.hidePageLoader();
                        return;
                    }
                    
                    console.log('WBAssist认证失败:', authStatus.message);
                    this.showWBAssistAuthStatus(false);
                    this.showLoginPrompt();
                }
            } else {
                console.log('未找到WBAssist认证信息');
                this.showWBAssistAuthStatus(false);
                this.hidePageLoader();
                this.showLoginPrompt();
            }
        } catch (error) {
            console.error('WBAssist认证检查失败:', error);
            this.showWBAssistAuthStatus(false);
            this.updateLoaderStep('step-auth', 'error');
            this.hidePageLoader();
            this.showLoginPrompt();
        } finally {
            this.isCheckingAuth = false;
        }
    }

    // 🔧 refreshStoreAndOrderData函数已集成到认证流程中，避免重复代码

    // 显示WBAssist认证状态
    showWBAssistAuthStatus(authenticated, user = null) {
        // 更新用户信息显示
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            if (authenticated && user) {
                // 显示已登录状态
                const userDisplay = userInfo.querySelector('.user-display') || document.createElement('div');
                userDisplay.className = 'user-display';
                userDisplay.innerHTML = `
                    <div class="user-avatar" data-action="goto-dashboard" title="前往个人中心">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-email">${user.email}</span>
                        <span class="auth-status authenticated">已登录</span>
                    </div>
                    <button class="logout-btn" data-action="logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                `;
                
                // 如果不存在则添加
                if (!userInfo.querySelector('.user-display')) {
                    userInfo.appendChild(userDisplay);
                }
            } else {
                // 清除用户显示
                const userDisplay = userInfo.querySelector('.user-display');
                if (userDisplay) {
                    userDisplay.remove();
                }
            }
        }
    }

    // 显示登录提示界面
    showLoginPrompt(isTokenExpired = false) {
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            const title = isTokenExpired ? '登录已过期，请重新登录' : '请先登录WBAssist账户';
            const message = isTokenExpired ? '令牌过期，请重新登录WBAssist账户' : '您需要登录WBAssist账户才能使用自动预约功能';
            const icon = isTokenExpired ? 'fas fa-clock' : 'fas fa-lock';
            
            mainContent.innerHTML = `
                <div class="login-prompt">
                    <div class="login-prompt-content">
                        <div class="login-icon">
                            <i class="${icon}"></i>
                        </div>
                        <h2>${title}</h2>
                        <p>${message}</p>
                        <div class="login-actions">
                            <button class="btn btn-primary" data-action="login">
                                <i class="fas fa-sign-in-alt"></i>
                                ${isTokenExpired ? '重新登录' : '立即登录'}
                            </button>
                            <button class="btn btn-secondary" data-action="register">
                                <i class="fas fa-user-plus"></i>
                                注册账户
                            </button>
                        </div>
                        <div class="login-features">
                            <h3>登录后可以享受：</h3>
                            <ul>
                                <li><i class="fas fa-check"></i> 免费获得每个店铺1次预约机会</li>
                                <li><i class="fas fa-check"></i> 多店铺管理</li>
                                <li><i class="fas fa-crown"></i> 升级套餐获得更多预约次数</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // 显示主界面
    async showMainInterface() {
        console.log('🔄 显示主界面 - 认证成功');
        
        // 移除登录提示（如果存在）
        const loginPrompt = document.querySelector('.login-prompt');
        if (loginPrompt) {
            loginPrompt.remove();
            console.log('✅ 登录提示已移除');
        }
        
        // 显示原始的主界面内容
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            // 如果main-content为空或只有登录提示，重新加载原始HTML结构
            const hasOriginalContent = mainContent.querySelector('.store-panel') || 
                                     mainContent.querySelector('.orders-section');
            
            if (!hasOriginalContent) {
                // 重新生成主界面HTML（从原始模板复制）
                this.restoreMainInterfaceHTML();
                
                // 🔧 等待DOM完全渲染，确保新元素可用
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // 确保内容可见
            mainContent.style.display = 'block';
            console.log('✅ 主界面内容已显示');
            
            // 🔧 HTML结构已准备好，重新绑定事件监听器
            console.log('🔄 HTML结构已准备好，重新绑定事件监听器');
            this.rebindEventListeners();
            
            // 现在初始化店铺选择器
            console.log('🔄 开始初始化店铺选择器');
            this.updateLoaderStep('step-permissions', 'active');
            await this.initStoreSelector();
            this.updateLoaderStep('step-permissions', 'completed');
            
            // 🔧 检查是否有待更新的店铺信息
            if (this.pendingStoreUpdate) {
                console.log('🔄 现在更新待处理的店铺信息:', this.pendingStoreUpdate.displayName);
                const storeToUpdate = this.pendingStoreUpdate;
                this.pendingStoreUpdate = null; // 清除待更新标记
                await this.updateStoreDisplay(storeToUpdate);
                console.log('✅ 待处理的店铺信息已更新完成');
            }
        }
    }
    
    // 重新绑定事件监听器（用于HTML结构重新生成后）
    rebindEventListeners() {
        console.log('🔄 重新绑定所有事件监听器');
        
        // 店铺选择变化
        const storeSelect = document.getElementById('storeSelect');
        if (storeSelect && !storeSelect.hasAttribute('data-event-bound')) {
            storeSelect.addEventListener('change', () => this.handleStoreChange());
            storeSelect.setAttribute('data-event-bound', 'true');
            console.log('✅ 店铺选择器事件已重新绑定');
        }

        // 状态筛选变化
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter && !statusFilter.hasAttribute('data-event-bound')) {
            statusFilter.addEventListener('change', () => this.handleStatusFilter());
            statusFilter.setAttribute('data-event-bound', 'true');
            console.log('✅ 状态筛选器事件已重新绑定');
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshOrdersBtn');
        if (refreshBtn && !refreshBtn.hasAttribute('data-event-bound')) {
            refreshBtn.addEventListener('click', () => this.refreshOrders());
            refreshBtn.setAttribute('data-event-bound', 'true');
            console.log('✅ 刷新按钮事件已重新绑定');
        }

        // 卡密管理按钮
        const licenseBtn = document.querySelector('[data-action="open-license-bind-modal"]');
        if (licenseBtn && !licenseBtn.hasAttribute('data-event-bound')) {
            licenseBtn.setAttribute('data-event-bound', 'true');
            console.log('✅ 卡密管理按钮事件已重新绑定（使用全局监听器）');
        }

        // 【修复】重新绑定订单操作事件（解决重新登录后预约功能失效问题）
        this.bindOrderActionEvents();
        console.log('✅ 订单操作事件已重新绑定');

        // 重新绑定分页控件事件
        this.bindPaginationEvents();
        console.log('✅ 分页控件事件已重新绑定');
    }
    
    // 绑定分页事件
    bindPaginationEvents() {
        const prevPage = document.getElementById('prevPage');
        const nextPage = document.getElementById('nextPage');
        if (prevPage && !prevPage.hasAttribute('data-event-bound')) {
            prevPage.addEventListener('click', () => this.handlePrevPage());
            prevPage.setAttribute('data-event-bound', 'true');
        }
        if (nextPage && !nextPage.hasAttribute('data-event-bound')) {
            nextPage.addEventListener('click', () => this.handleNextPage());
            nextPage.setAttribute('data-event-bound', 'true');
        }
    }
    
    // 恢复主界面HTML结构
    restoreMainInterfaceHTML() {
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.innerHTML = `
                <!-- 店铺信息面板 -->
                <section class="store-panel">
                    <div class="store-info-card">
                        <div class="store-header">
                            <h2>店铺信息</h2>
                            <div class="store-header-actions">
                                <div class="store-selector">
                                    <div class="custom-select-wrapper">
                                        <select id="storeSelect" class="store-dropdown">
                                            <option value="">选择店铺...</option>
                                        </select>
                                        <div class="select-arrow">
                                            <i class="fas fa-chevron-down"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="license-actions">
                                    <button class="btn btn-sm btn-primary" data-action="open-license-bind-modal" title="绑定或管理店铺卡密">
                                        <i class="fas fa-key"></i>
                                        卡密管理
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="store-details" id="storeDetails">
                            <!-- 表头行 -->
                            <div class="store-details-header">
                                <div class="store-header-item">店铺名称</div>
                                <div class="store-header-item">ИНН</div>
                                <div class="store-header-item">店铺等级</div>
                                <div class="store-header-item">剩余天数</div>
                                <div class="store-header-item">剩余预约次数</div>
                            </div>
                            <!-- 数据行 -->
                            <div class="store-details-data">
                                <div class="store-data-item" id="storeName">-</div>
                                <div class="store-data-item" id="storeInn">-</div>
                                <div class="store-data-item">
                                    <div class="plan-badge-with-icon" id="storePlanBadge">
                                        <i class="plan-icon" id="storePlanIcon"></i>
                                        <span id="storePlan" class="plan-text">-</span>
                                    </div>
                                </div>
                                <div class="store-data-item">
                                    <span id="storeExpiration" class="days-remaining">-</span>
                                </div>
                                <div class="store-data-item" id="remainingCredits">-</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 订单列表和预约面板 -->
                <section class="orders-section">
                    <div class="section-header">
                        <h2>供货计划订单</h2>
                        <div class="header-actions">
                            <button id="refreshOrdersBtn" class="btn-refresh" title="刷新订单列表">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <div class="filters">
                                <select id="statusFilter" class="filter-select">
                                    <option value="all">全部状态</option>
                                    <option value="PENDING">等待中</option>
                                    <option value="BOOKED">已预约</option>
                                    <option value="DELIVERED">已发货</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 订单列表 -->
                    <div class="orders-container" id="ordersContainer">
                        <!-- 订单卡片将通过JavaScript动态生成 -->
                    </div>
                    
                    <!-- 分页控件 -->
                    <div class="pagination" id="pagination">
                        <button class="pagination-btn" id="prevPage" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="pagination-info" id="pageInfo">第 1 页，共 1 页</span>
                        <button class="pagination-btn" id="nextPage" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </section>
            `;
            console.log('🔧 主界面HTML结构已恢复');
        }
    }

    // 打开登录页面
    async openLoginPage() {
        // 🔧 检查是否刚刚退出登录，如果是则强制重新认证
        const forceReauth = this.justLoggedOut || false;
        if (forceReauth) {
            console.log('🔄 检测到刚退出登录，启用强制重新认证模式');
            this.justLoggedOut = false; // 重置标志
        }
        
        await window.wbAssistApiService.openLoginPage(forceReauth);
    }

    // 打开注册页面（弹窗模式）
    async openRegisterPage() {
        const registerUrl = `${window.wbAssistApiService.backendUrl}/signup?redirect=${encodeURIComponent('extension://plugin-auth')}`;
        
        // 存储弹窗引用用于后续关闭
        window.wbAssistApiService.registerPopup = window.open(
            registerUrl, 
            'wbassist-register',
            'width=480,height=700,scrollbars=yes,resizable=yes,left=' + 
            (screen.width / 2 - 240) + ',top=' + (screen.height / 2 - 350)
        );
        
        // 监听弹窗关闭事件
        if (window.wbAssistApiService.registerPopup) {
            const checkClosed = setInterval(() => {
                if (window.wbAssistApiService.registerPopup.closed) {
                    clearInterval(checkClosed);
                    console.log('🔒 注册弹窗已关闭');
                }
            }, 1000);
        }
    }

    // 打开个人中心页面（新标签页）
    async openDashboardPage() {
        const dashboardUrl = `${window.wbAssistApiService.backendUrl}/dashboard`;
        
        console.log('🏠 打开个人中心页面:', dashboardUrl);
        
        // 在新标签页中打开dashboard页面
        window.open(dashboardUrl, '_blank');
    }

    // 处理登出
    async handleLogout() {
        console.log('🚪 开始处理用户退出...');
        
        // 🔧 设置退出标志，下次登录时强制重新认证
        this.justLoggedOut = true;
        
        // 🔧 彻底清理认证状态
        if (window.wbAssistApiService) {
            await window.wbAssistApiService.clearAuth();
        }
        
        // 🔧 清理应用状态
        this.currentStore = null;
        this.isCheckingAuth = false;
        this.isInitialized = false;
        this.resetLoaderSteps();
        
        // 🔧 更新header用户认证状态显示（清除用户信息）
        this.showWBAssistAuthStatus(false);
        
        // 🔧 清理数据缓存
        if (window.realDataService) {
            window.realDataService.clearCache();
        }
        
        // 🔧 重置界面状态
        this.showLoginPrompt();
        
        // 🔔 用户提示：基于本地清理+强制重新认证的策略
        this.showNotification(
            '已退出登录', 
            'success', 
            5000
        );
        
        console.log('✅ 用户退出处理完成，下次登录将重新认证');
    }

    // 向后台脚本发送消息
    async sendMessageToBackground(action, data = {}) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action, ...data }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    // 显示认证状态
    showAuthStatus(message, type = 'info') {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');

        if (statusIndicator && statusText) {
            statusText.textContent = message;

            // 移除所有状态类
            statusIndicator.classList.remove('connecting', 'connected', 'error');

            // 根据类型设置状态
            switch (type) {
                case 'info':
                    statusIndicator.classList.add('connecting');
                    break;
                case 'success':
                    statusIndicator.classList.add('connected');
                    break;
                case 'error':
                    statusIndicator.classList.add('error');
                    // 如果是错误状态，显示错误弹窗
                    this.showConnectionErrorModal();
                    break;
                case 'warning':
                    statusIndicator.classList.add('connecting');
                    break;
                default:
                    statusIndicator.classList.add('connecting');
            }
        }
    }

    // 显示连接错误弹窗
    showConnectionErrorModal() {
        const modal = document.getElementById('connectionErrorModal');
        if (modal) {
            modal.classList.add('show');

            // 绑定关闭事件
            const closeBtn = document.getElementById('closeConnectionErrorModal');
            const closeErrorBtn = document.getElementById('closeErrorModal');
            const retryBtn = document.getElementById('retryConnection');

            const closeModal = () => {
                modal.classList.remove('show');
            };

            if (closeBtn) {
                closeBtn.onclick = closeModal;
            }

            if (closeErrorBtn) {
                closeErrorBtn.onclick = closeModal;
            }

            if (retryBtn) {
                retryBtn.onclick = () => {
                    closeModal();
                    // 重试连接
                    this.performAuthCheck();
                };
            }

            // 点击背景关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            };
        }
    }

    // 处理认证成功
    async handleAuthSuccess(authData) {
        this.authData = authData;

        if (authData.wbSuppliersList && authData.wbSuppliersList.suppliers) {
            // 重新初始化真实数据服务以获取最新的供应商数据
            await window.realDataService.initialize();

            // 只在初始化完成后才重新初始化店铺选择器，无需重复加载订单
            if (this.isInitialized) {
                console.log('🔧 认证成功后更新店铺选择器，跳过重复订单加载');
                this.initStoreSelector();
                // 移除重复的订单加载，initializeApp中已经处理
            }
        }
    }

    // 处理认证失败
    handleAuthFailure(_result) {
        this.authData = null;
    }

    // ==================== 数据一致性检查和修复 ====================

    // 静默检查和修复数据一致性（后台自动运行）
    async checkDataConsistency() {
        try {
            if (!window.bookingStorage || !window.bookingStorage.isAvailable()) {
                return;
            }

            // 获取所有ID映射
            const allMappings = window.bookingStorage.getAllIdMappings();
            let repairedCount = 0;

            for (const mapping of allMappings) {
                try {
                    const { preorderId, supplyId } = mapping;

                    // 检查映射的订单是否还存在
                    const orderByPreorder = window.realDataService.getOrderByAnyId(preorderId);
                    const orderBySupply = window.realDataService.getOrderByAnyId(supplyId);

                    if (orderByPreorder && orderBySupply && orderByPreorder !== orderBySupply) {
                        // 存在不一致，静默修复
                        await window.realDataService.handleIdMapping(preorderId, supplyId);
                        repairedCount++;
                    }
                } catch (error) {
                    // 静默处理错误，不影响用户体验
                    console.warn(`ID映射检查失败: ${mapping.preorderId}->${mapping.supplyId}`, error);
                }
            }

            // 静默清理过期的映射
            await window.realDataService.cleanupExpiredIdMappings();

            if (repairedCount > 0) {
                console.log(`已静默修复 ${repairedCount} 个数据一致性问题`);
            }
        } catch (error) {
            // 静默处理错误
            console.warn('数据一致性检查失败:', error);
        }
    }

    // 更新店铺选择器（如果需要）
    updateStoreSelector(suppliers) {
        const storeSelect = document.getElementById('storeSelect');
        if (!storeSelect || !suppliers || suppliers.length === 0) {
            return;
        }

        // 清空现有选项
        storeSelect.innerHTML = '<option value="">选择店铺</option>';

        // 添加供应商选项
        suppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.id || supplier.supplierId;
            option.textContent = supplier.name || supplier.supplierName || `供应商 ${supplier.id}`;
            storeSelect.appendChild(option);
        });

        // 默认选择第一个供应商
        if (suppliers.length > 0) {
            storeSelect.value = suppliers[0].id;
            this.handleStoreChange();
        }
    }

    // 刷新供应商数据（在初始化过程中自动调用）
    async refreshSuppliersData() {
        try {
            // 重新初始化真实数据服务以获取最新数据
            await window.realDataService.initialize();

            // 只在初始化完成后才重新初始化店铺选择器
            if (this.isInitialized) {
                this.initStoreSelector();
            }
            return true;
        } catch (error) {
            console.error('刷新供应商数据失败:', error);
            return false;
        }
    }

    // 设置云端任务取消监听器
    setupCloudCancelListener() {
        // 定期检查是否有待处理的云端取消请求
        setInterval(async () => {
            await this.processPendingCloudCancels();
        }, 2000); // 每2秒检查一次
    }

    // 处理待处理的云端任务取消请求
    async processPendingCloudCancels() {
        if (!window.wbAssistApiService || !window.wbAssistApiService.isAuthenticated) {
            return; // 未登录时跳过
        }

        try {
            // 获取所有存储的数据
            const storage = await chrome.storage.local.get(null);
            
            // 找到所有待处理的取消请求
            const pendingCancels = {};
            for (const [key, value] of Object.entries(storage)) {
                if (key.startsWith('pending_cloud_cancel_') && value.cancelRequested) {
                    const orderId = key.replace('pending_cloud_cancel_', '');
                    pendingCancels[orderId] = value;
                }
            }

            // 处理每个待处理的取消请求
            for (const [orderId, cancelData] of Object.entries(pendingCancels)) {
                console.log(`🔄 处理待处理的云端取消请求: ${orderId}`);
                
                try {
                    // 尝试找到并取消云端任务
                    await this.cancelCloudTaskByOrderId(orderId, cancelData);
                    
                    // 取消成功，清除待处理标记
                    await chrome.storage.local.remove([`pending_cloud_cancel_${orderId}`]);
                    console.log(`✅ 云端任务取消完成: ${orderId}`);
                    
                } catch (error) {
                    console.error(`❌ 处理云端取消失败 ${orderId}:`, error);
                    
                    // 如果请求时间超过5分钟，清除过期的请求
                    const requestTime = new Date(cancelData.requestTime);
                    const now = new Date();
                    if (now - requestTime > 5 * 60 * 1000) {
                        await chrome.storage.local.remove([`pending_cloud_cancel_${orderId}`]);
                        console.log(`🗑️ 已清除过期的取消请求: ${orderId}`);
                    }
                }
            }
        } catch (error) {
            console.error('处理待处理云端取消请求失败:', error);
        }
    }

    // 根据orderId取消云端任务
    async cancelCloudTaskByOrderId(orderId, cancelData) {
        console.log(`🔍 开始取消云端任务，orderId: ${orderId}`, cancelData);
        
        // 如果有直接的taskId，优先使用
        if (cancelData.taskId) {
            console.log(`✅ 使用直接的taskId进行取消: ${cancelData.taskId}`);
            try {
                await window.wbAssistApiService.reportTaskFailure(
                    cancelData.taskId,
                    'cancelled',
                    {
                        error_code: 'USER_CANCELLED_BACKGROUND',
                        error_message: '用户从后台取消预约',
                        cancelled_by: 'user_background',
                        cancelled_reason: '后台监控取消触发的云端任务取消'
                    }
                );
                console.log(`✅ 云端任务取消成功: ${cancelData.taskId}`);
                return;
            } catch (error) {
                console.error(`❌ 使用taskId取消失败: ${error.message}`);
                // 继续尝试其他方法
            }
        }

        // 先尝试直接使用orderId作为preorderId查找任务
        console.log(`🔍 尝试直接使用orderId作为preorderId查找任务: ${orderId}`);
        try {
            const tasks = await window.wbAssistApiService.getTasks();
            let activeTask = tasks.tasks.find(task => 
                task.wb_preorder_id === String(orderId) && 
                ['pending', 'running'].includes(task.status)
            );

            if (activeTask) {
                console.log(`✅ 找到活跃任务，直接取消: ${activeTask.id}`);
                await window.wbAssistApiService.reportTaskFailure(
                    activeTask.id,
                    'cancelled',
                    {
                        error_code: 'USER_CANCELLED_BACKGROUND',
                        error_message: '用户从后台取消预约',
                        cancelled_by: 'user_background',
                        cancelled_reason: '后台监控取消触发的云端任务取消'
                    }
                );
                console.log(`✅ 云端任务取消成功: ${activeTask.id}`);
                return;
            }

            // 如果直接查找失败，尝试通过订单查找preorderId
            console.log(`🔍 直接查找失败，尝试通过订单数据查找preorderId`);
            const order = window.realDataService.getOrderByAnyId(orderId);
            let preorderId = null;
            
            if (order) {
                preorderId = order.originalPreorderId || order.preorderId || order.orderNumber;
                console.log(`📋 从订单数据获取preorderId: ${preorderId}`, {
                    originalPreorderId: order.originalPreorderId,
                    preorderId: order.preorderId,
                    orderNumber: order.orderNumber
                });
            } else {
                console.log(`⚠️ 未在缓存中找到订单: ${orderId}`);
            }

            if (preorderId && preorderId !== orderId) {
                console.log(`🔍 使用解析的preorderId查找任务: ${preorderId}`);
                activeTask = tasks.tasks.find(task => 
                    task.wb_preorder_id === String(preorderId) && 
                    ['pending', 'running'].includes(task.status)
                );

                if (activeTask) {
                    console.log(`✅ 找到活跃任务: ${activeTask.id}`);
                    await window.wbAssistApiService.reportTaskFailure(
                        activeTask.id,
                        'cancelled',
                        {
                            error_code: 'USER_CANCELLED_BACKGROUND',
                            error_message: '用户从后台取消预约',
                            cancelled_by: 'user_background',
                            cancelled_reason: '后台监控取消触发的云端任务取消'
                        }
                    );
                    console.log(`✅ 云端任务取消成功: ${activeTask.id}`);
                    return;
                }
            }

            // 最后尝试：在所有任务中搜索可能匹配的
            console.log(`🔍 最后尝试：在所有任务中搜索可能匹配的订单`);
            const allTasks = tasks.tasks.filter(task => ['pending', 'running'].includes(task.status));
            console.log(`📊 当前活跃任务数量: ${allTasks.length}`, allTasks.map(t => ({ id: t.id, wb_preorder_id: t.wb_preorder_id })));
            
            console.log(`ℹ️ 未找到订单 ${orderId} 的活跃云端任务`);
            
        } catch (error) {
            console.error(`❌ 云端任务查找失败: ${error.message}`);
            // 不抛出错误，因为可能本来就没有云端任务
        }
    }


}

// 等待DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new WildberriesBookingApp();
    
    // WBAssist消息监听器已在构造函数中自动设置
    console.log('🔧 WBAssist认证消息监听器已在服务初始化时设置');
});

// 监听页面可见性变化（用户切换标签页时）
// 注意：不在此处触发初始化，只在用户主动操作时初始化
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.app && window.app.isInitialized) {
        // 页面重新可见时，只进行轻量级的状态检查，不重新初始化
    }
});

// 监听来自后台脚本的消息
if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener(async (request, _sender, sendResponse) => {
        if (request.action === 'authResult' && window.app) {
            if (request.result.success && request.result.authenticated) {
                window.app.showAuthStatus('已连接', 'success');
                // 认证成功后自动刷新供应商数据（只在点击插件图标时触发）
                if (window.app.isInitialized && !window.app.isLoading) {
                    await window.app.refreshSuppliersData();
                }
            } else {
                window.app.showAuthStatus('连接失败', 'error');
            }
        }
        sendResponse({ received: true });
    });
}

// 全局事件监听器 - 处理data-action属性的点击事件
document.addEventListener('click', async (event) => {
    const target = event.target.closest('[data-action]');
    if (!target) return;
    
    const action = target.getAttribute('data-action');
    console.log('🖱️ 点击事件触发:', {
        action,
        target: target,
        hasLicenseManager: !!window.licenseManager,
        hasApp: !!window.app
    });
    
    event.preventDefault();
    
    // 处理登录相关的操作
    if (window.app) {
        switch (action) {
            case 'login':
                await window.app.openLoginPage();
                break;
            case 'register':
                await window.app.openRegisterPage();
                break;
            case 'logout':
                await window.app.handleLogout();
                break;
            case 'goto-dashboard':
                await window.app.openDashboardPage();
                break;
        }
    }
    
    // 处理卡密管理相关的操作
    if (window.licenseManager) {
        switch (action) {
            case 'open-license-bind-modal':
                console.log('🔑 打开卡密绑定模态框');
                window.licenseManager.openLicenseBindModal();
                break;
            case 'close-license-bind-modal':
                console.log('❌ 关闭卡密绑定模态框');
                window.licenseManager.closeLicenseBindModal();
                break;
            case 'bind-license':
                console.log('🔗 执行卡密绑定');
                await window.licenseManager.bindLicense();
                break;
            case 'close-store-capabilities-modal':
                console.log('❌ 关闭店铺权限模态框');
                window.licenseManager.closeStoreCapabilitiesModal();
                break;
        }
    } else {
        console.warn('⚠️ window.licenseManager 不存在，action:', action);
    }
});

