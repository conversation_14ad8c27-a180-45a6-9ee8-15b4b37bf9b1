'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import Avatar from '@/public/images/yihui-avatar.png'
import { createClient } from '@/utils/supabase/client'

export default function SignUp() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [agreedToTerms, setAgreedToTerms] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!agreedToTerms) {
      setError('请同意服务条款和隐私政策')
      return
    }

    if (password.length < 6) {
      setError('密码长度不能少于6个字符')
      return
    }

    setLoading(true)

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        throw error
      }

      // 检查是否需要邮箱验证
      if (data?.user?.identities?.length === 0) {
        // 已注册但未确认邮箱
        setError('该邮箱已注册，请检查邮箱或直接登录')
        return
      }

      // 注册成功
      if (data.user) {
        // 重定向到成功页面或登录页面
        router.push('/signup-success')
      }
    } catch (error: any) {
      console.error('注册失败:', error)
      setError(error.message || '注册失败，请稍后再试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      {/* Page header */}
      <div className="max-w-3xl mx-auto text-center pb-12">
        <div className="relative inline-flex mb-4">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
        </div>
        <h1 className="h2 font-cabinet-grotesk">加入WBAssist智能预约平台</h1>
        <p className="text-xl text-gray-600 mt-4">
          开启您的Wildberries智能预约之旅
        </p>
      </div>
      {/* Form */}
      <div className="max-w-sm mx-auto">
        <form onSubmit={handleSignUp}>
          {error && (
            <div className="bg-red-50 text-red-600 p-3 rounded mb-4 text-sm">
              {error}
            </div>
          )}
          <div className="flex flex-wrap mb-4">
            <div className="w-full">
              <label className="block text-gray-500 text-sm font-medium mb-1" htmlFor="email">
                邮箱
              </label>
              <input
                id="email"
                type="email"
                className="form-input w-full text-gray-800"
                placeholder="请输入您的邮箱地址"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
          </div>
          <div className="flex flex-wrap mb-4">
            <div className="w-full">
              <label className="block text-gray-500 text-sm font-medium mb-1" htmlFor="password">
                密码
              </label>
              <input
                id="password"
                type="password"
                className="form-input w-full text-gray-800"
                placeholder="请设置登录密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              <p className="text-xs text-gray-500 mt-1">密码至少需要6个字符</p>
            </div>
          </div>
          <div className="flex flex-wrap items-center justify-between mt-6">
            <Link
              className="font-medium text-sm sm:text-base text-blue-600 decoration-blue-600 decoration-2 underline-offset-2 hover:underline"
              href="/signin"
            >
              返回登录
            </Link>
            <div className="ml-2">
              <button 
                type="submit"
                className="btn-sm text-white bg-blue-600 hover:bg-blue-700 shadow-sm"
                disabled={loading}
              >
                {loading ? '注册中...' : '注册WBAssist账号'}
              </button>
            </div>
          </div>
          <div className="mt-5">
            <label className="flex items-start">
              <input 
                type="checkbox" 
                className="form-checkbox mt-0.5" 
                checked={agreedToTerms}
                onChange={(e) => setAgreedToTerms(e.target.checked)}
                required
              />
              <span className="text-sm text-gray-500 ml-3">
                我同意{' '}
                <a className="underline hover:decoration-blue-600 underline-offset-2 hover:underline" href="#0">
                  服务条款
                </a>{' '}
                和{' '}
                <a className="underline hover:decoration-blue-600 underline-offset-2 hover:underline" href="#0">
                  隐私政策
                </a>
                。
              </span>
            </label>
          </div>
        </form>
      </div>    
    </>
  )
}
