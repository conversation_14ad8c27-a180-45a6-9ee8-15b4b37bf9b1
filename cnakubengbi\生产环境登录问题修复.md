# 生产环境登录问题修复

## 🐛 问题描述

生产环境下插件弹出登录弹窗后没有收到网站的认证成功消息，切换回开发环境又可以正常工作。

## 🔍 问题根因

插件的 `manifest.json` 文件中的权限配置使用了**错误的域名**：

- ❌ 旧域名：`wbassist.vercel.app`  
- ✅ 正确域名：`wb-assist-web.vercel.app`

由于权限配置错误，插件无法在生产环境网站中注入 content script，导致：
1. 插件与网站无法建立消息通信
2. 网站的认证成功消息无法传递到插件
3. 登录流程中断

## ✅ 修复方案

### 1. 修复 manifest.json 权限配置

已修复以下配置：

```json
{
  "host_permissions": [
    "https://seller.wildberries.ru/*",
    "https://*.wildberries.ru/*", 
    "https://*.seller-auth.wildberries.ru/*",
    "https://*.supabase.co/*",
    "http://localhost:3000/*",
    "https://localhost:3000/*",
    "https://wb-assist-web.vercel.app/*"  // 👈 已更新为正确域名
  ],
  
  "content_scripts": [
    {
      "matches": [
        "http://localhost:3000/*",
        "https://wb-assist-web.vercel.app/*"  // 👈 已更新为正确域名
      ],
      "js": ["content-script.js"],
      "run_at": "document_start"
    }
  ]
}
```

### 2. 登录消息流程

正常的登录消息传递流程：

```
网站 → postMessage → Content Script → Chrome Runtime → Background Script → Storage
```

1. **网站发送认证成功消息**
   ```javascript
   window.postMessage({
     type: 'WBASSIST_AUTH_SUCCESS',
     payload: { token, user, supabase_url }
   }, '*');
   ```

2. **Content Script 接收并转发**
   ```javascript
   chrome.runtime.sendMessage({
     action: 'auth_success',
     data: { access_token: token, user, supabase_url }
   });
   ```

3. **Background Script 保存认证信息**
   ```javascript
   chrome.storage.local.set({ wbassist_auth: authData });
   ```

## 🚀 部署步骤

1. **重新加载插件**
   - 打开 Chrome 扩展管理页面 (chrome://extensions/)
   - 找到 WBAssist 插件
   - 点击"重新加载"按钮

2. **验证修复**
   - 打开生产环境网站：https://wb-assist-web.vercel.app
   - 点击插件图标进行登录测试
   - 检查浏览器控制台是否有相关消息输出

## 🔧 验证方法

### 在浏览器控制台中验证：

```javascript
// 1. 检查 content script 是否正常注入
console.log('Content Script 状态:', window.chrome?.runtime?.id);

// 2. 检查当前 API 配置
console.log('当前API地址:', window.API_CONFIG?.getApiUrl());

// 3. 模拟发送认证消息测试
window.postMessage({
  type: 'WBASSIST_AUTH_SUCCESS', 
  payload: { 
    token: 'test_token',
    user: { id: 'test' }
  }
}, '*');
```

## 📋 检查清单

- ✅ manifest.json 权限配置已更新
- ✅ content_scripts matches 已更新  
- ✅ API_CONFIG 环境配置正确
- ✅ 消息传递机制正常
- ⏳ 重新加载插件
- ⏳ 生产环境测试验证

## 🚨 重要提示

- **必须重新加载插件**才能使 manifest.json 的更改生效
- **开发环境和生产环境**现在都应该能正常工作
- 如果仍有问题，检查浏览器控制台的错误信息

## 🎯 预防措施

为避免将来出现类似问题：

1. **统一配置管理**: 已在 `constants.js` 中创建统一的环境配置
2. **自动化检查**: 建议在构建流程中添加域名一致性检查
3. **测试流程**: 每次更换域名后必须测试所有环境的插件功能

---

**现在生产环境的登录功能应该可以正常工作了！** 🎉 