// 前端交互常量定义
// 只包含订单展示和预约交互需要的常量

// 应用常量
const APP_CONSTANTS = {
    NAME: 'WBAssist 自动预约系统',
    VERSION: '1.0.0'
};

// 预约状态常量
const BOOKING_STATUS = {
    NOT_PLANNED: 'not_planned',
    BOOKING: 'booking',
    ACCEPTED: 'accepted',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
};

// 用户计划常量
const USER_PLANS = {
    FREE: {
        name: 'Free',
        displayName: '免费版',
        maxCreditsPerMonth: 1,
        maxConcurrentBookings: 1
    },
    PRO: {
        name: 'Pro',
        displayName: '专业版',
        maxCreditsPerMonth: 3,
        maxConcurrentBookings: 3
    },
    ULTIMATE: {
        name: 'Ultimate',
        displayName: '旗舰版',
        maxCreditsPerMonth: -1, // 无限
        maxConcurrentBookings: 10
    }
};

// 时间常量
const TIME_CONSTANTS = {
    // 预约流程超时
    BOOKING_PROCESS_TIMEOUT: 60000,

    // 重试设置
    MAX_RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000
};

// API 配置常量
const API_CONFIG = {
    // 🔧 环境配置 - 只需要修改这里就能切换环境
    DEVELOPMENT: {
        BASE_URL: 'http://localhost:3000',
        WS_URL: 'ws://localhost:3000'
    },
    PRODUCTION: {
        BASE_URL: 'https://wb-assist-web.vercel.app',
        WS_URL: 'wss://wb-assist-web.vercel.app'
    },
    
    // 当前环境设置 - 改这里来切换环境
    CURRENT_ENV: 'PRODUCTION', // 改为 'DEVELOPMENT' 或 'PRODUCTION'
    
    // 获取当前API基础URL
    getBaseUrl() {
        return this[this.CURRENT_ENV].BASE_URL;
    },
    
    // 获取当前WebSocket URL
    getWsUrl() {
        return this[this.CURRENT_ENV].WS_URL;
    },
    
    // 自动检测环境（备用方案）
    autoDetectEnv() {
        try {
            // 检查是否在网页环境中
            if (typeof window !== 'undefined' && window.location) {
                if (window.location.hostname === 'localhost' || 
                    window.location.href.includes('localhost:3000')) {
                    return 'DEVELOPMENT';
                }
            }
        } catch (e) {
            // 忽略错误，使用默认设置
        }
        return this.CURRENT_ENV;
    },
    
    // 智能获取API地址
    getApiUrl() {
        const env = this.autoDetectEnv();
        return this[env].BASE_URL;
    }
};

// 将常量添加到全局对象
window.APP_CONSTANTS = APP_CONSTANTS;
window.BOOKING_STATUS = BOOKING_STATUS;
window.USER_PLANS = USER_PLANS;
window.TIME_CONSTANTS = TIME_CONSTANTS;
window.API_CONFIG = API_CONFIG;
