// API服务类 - 处理与WB后端的真实API交互
class WBApiService {
    constructor() {
        this.baseUrl = 'https://seller.wildberries.ru';
        this.authData = null;
        this.isInitialized = false;
    }

    // 初始化API服务
    async initialize() {
        try {
            // 从后台脚本获取认证数据
            const authData = await this.getAuthDataFromBackground();
            if (authData && authData.success) {
                this.authData = authData.data;
                this.isInitialized = true;
                return true;
            }
            return false;
        } catch (error) {
            console.error('API服务初始化失败:', error);
            return false;
        }
    }

    // 从后台脚本获取认证数据
    async getAuthDataFromBackground() {
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            throw new Error('需要在扩展环境中运行');
        }

        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getAuthData' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    // 获取供应商列表
    async getSuppliers() {
        if (!this.isInitialized) {
            const initialized = await this.initialize();
            if (!initialized) {
                throw new Error('API服务未初始化');
            }
        }

        if (!this.authData || !this.authData.wbSuppliersList) {
            throw new Error('没有可用的供应商数据');
        }

        return this.authData.wbSuppliersList.suppliers || [];
    }

    // 刷新供应商数据
    async refreshSuppliers() {
        try {
            // 重新获取认证数据
            const authData = await this.getAuthDataFromBackground();
            if (authData && authData.success) {
                this.authData = authData.data;
                return this.authData.wbSuppliersList?.suppliers || [];
            }
            throw new Error('获取供应商数据失败');
        } catch (error) {
            console.error('刷新供应商数据失败:', error);
            throw error;
        }
    }

    // 获取当前选中的店铺ID（按照2.md的成功实现）
    async getCurrentSupplierId() {
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            throw new Error('需要在扩展环境中运行');
        }

        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getCurrentSupplierId' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    resolve(response.supplierId);
                } else {
                    reject(new Error(response.message || '获取店铺ID失败'));
                }
            });
        });
    }

    // 切换店铺（按照2.md的成功实现）
    async switchToSupplier(supplierId) {
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            throw new Error('需要在扩展环境中运行');
        }

        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'switchSupplier',
                supplierId: supplierId
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response && response.success) {
                    resolve();
                } else {
                    reject(new Error(response?.message || '切换店铺失败'));
                }
            });
        });
    }

    // 从WB网站获取当前选中的店铺ID
    async getCurrentSupplierIdFromWB() {
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            throw new Error('需要在扩展环境中运行');
        }

        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getCurrentSupplierId' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    resolve(response.supplierId);
                } else {
                    reject(new Error(response.message || '获取店铺ID失败'));
                }
            });
        });
    }

    // 获取供应计划列表（按照2.md成功项目的实现方式）
    async getSupplyList(filters = {}) {
        if (!this.isInitialized || !this.authData) {
            throw new Error('API服务未初始化');
        }

        try {
            // 获取当前店铺ID并构建完整的Cookie字符串（按照2.md的方式）
            const currentSupplierId = await this.getCurrentSupplierId();
            let fullCookieString = this.authData.wbxValidKey;

            if (currentSupplierId) {
                fullCookieString = `${this.authData.wbxValidKey}; x-supplier-id-external=${currentSupplierId}; x-supplier-id=${currentSupplierId}`;
            }

            const requestData = {
                method: "listSupplies",
                params: {
                    pageNumber: filters.page || 1,
                    pageSize: filters.pageSize || 20,
                    sortBy: "createDate",
                    sortDirection: "desc",
                    statusId: filters.statusId || -2  // WB后台使用-2获取所有状态的供应计划
                },
                jsonrpc: "2.0",
                id: `json-rpc_supplies_${Date.now()}`
            };

            const response = await fetch('https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/listSupplies', {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'authorizev3': this.authData.Authorizev3,
                    'content-type': 'application/json',
                    'cookie': fullCookieString,
                    'origin': 'https://seller.wildberries.ru',
                    'referer': 'https://seller.wildberries.ru/',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const result = await response.json();

            if (result.error) {
                throw new Error(`API错误: ${result.error.message || result.error}`);
            }

            return result.result || { totalCount: 0, data: [] };
        } catch (error) {
            console.error('获取供应计划失败:', error);
            throw error;
        }
    }

    // 获取接收费用
    async getAcceptanceCosts(preorderID, dateFrom, dateTo, storeInfo = null) {
        if (!this.isInitialized || !this.authData) {
            throw new Error('API服务未初始化');
        }

        try {
            const requestData = {
                params: {
                    dateFrom: dateFrom,
                    dateTo: dateTo,
                    preorderID: preorderID
                },
                jsonrpc: '2.0',
                id: 'json-rpc_' + Date.now()
            };

            // 构建店铺特定的Cookie字符串
            let cookieString = this.authData.wbxValidKey;
            if (storeInfo && storeInfo.supplierId) {
                cookieString += `; x-supplier-id=${storeInfo.supplierId}`;
                if (storeInfo.supplierIdExternal) {
                    cookieString += `; x-supplier-id-external=${storeInfo.supplierIdExternal}`;
                }
            }

            const headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'authorizev3': this.authData.Authorizev3,
                'content-type': 'application/json',
                'origin': this.baseUrl,
                'priority': 'u=1, i',
                'referer': `${this.baseUrl}/`,
                'root-version': 'v1.50.2',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'user-agent': navigator.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                'Cookie': cookieString
            };

            // 如果有店铺信息，添加店铺特定的请求头
            if (storeInfo && storeInfo.supplierId) {
                headers['x-supplier-id'] = storeInfo.supplierId;
                if (storeInfo.supplierIdExternal) {
                    headers['x-supplier-id-external'] = storeInfo.supplierIdExternal;
                }
            }

            const response = await fetch(`${this.baseUrl}/ns/sm-supply/supply-manager/api/v1/supply/getAcceptanceCosts`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestData),
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status}`);
            }

            const data = await response.json();
            return data.result || { costs: [] };
        } catch (error) {
            console.error('获取接收费用失败:', error);
            throw error;
        }
    }

    // 检查API服务是否可用
    isAvailable() {
        return this.isInitialized && this.authData && this.authData.Authorizev3;
    }

    // 获取当前认证状态
    getAuthStatus() {
        return {
            isAuthenticated: this.isAvailable(),
            hasSuppliers: this.authData?.wbSuppliersList?.suppliers?.length > 0,
            suppliersCount: this.authData?.wbSuppliersList?.suppliers?.length || 0
        };
    }
}

// 创建全局API服务实例
window.wbApiService = new WBApiService();
