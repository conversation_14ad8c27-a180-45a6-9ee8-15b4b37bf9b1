import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

// 获取用户卡密信息
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // 验证用户认证
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '用户未认证' },
        { status: 401 }
      )
    }

    // 获取用户的激活卡密
    const { data: userCards, error: cardsError } = await supabase
      .from('user_cards')
      .select(`
        id,
        code,
        plan,
        billing_cycle,
        monthly_credits,
        features,
        status,
        activated_at,
        expires_at,
        created_at
      `)
      .eq('bound_user_id', user.id)
      .in('status', ['activated', 'expired'])
      .order('activated_at', { ascending: false })

    if (cardsError) {
      console.error('获取用户卡密失败:', cardsError)
      return NextResponse.json(
        { error: '获取卡密信息失败' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      cards: userCards || [],
      user: {
        id: user.id,
        email: user.email,
        name: user.user_metadata?.full_name || user.email
      }
    })

  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 激活卡密
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // 验证用户认证
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '用户未认证' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { cardCode } = body

    if (!cardCode) {
      return NextResponse.json(
        { error: '卡密代码不能为空' },
        { status: 400 }
      )
    }

    // 查找卡密
    const { data: card, error: findError } = await supabase
      .from('user_cards')
      .select('*')
      .eq('code', cardCode)
      .single()

    if (findError || !card) {
      return NextResponse.json(
        { error: '卡密不存在或已失效' },
        { status: 404 }
      )
    }

    // 检查卡密状态
    if (card.status !== 'generated' && card.status !== 'sold') {
      return NextResponse.json(
        { error: '卡密已被使用或已过期' },
        { status: 400 }
      )
    }

    // 检查是否已绑定其他用户
    if (card.bound_user_id && card.bound_user_id !== user.id) {
      return NextResponse.json(
        { error: '卡密已被其他用户绑定' },
        { status: 400 }
      )
    }

    // 计算过期时间
    const now = new Date()
    let expiresAt: Date

    switch (card.billing_cycle) {
      case 'monthly':
        expiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30天
        break
      case 'quarterly':
        expiresAt = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000) // 90天
        break
      case 'yearly':
        expiresAt = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000) // 365天
        break
      default:
        expiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 默认30天
    }

    // 激活卡密
    const { data: updatedCard, error: updateError } = await supabase
      .from('user_cards')
      .update({
        status: 'activated',
        bound_user_id: user.id,
        activated_at: now.toISOString(),
        expires_at: expiresAt.toISOString(),
        updated_at: now.toISOString()
      })
      .eq('id', card.id)
      .select()
      .single()

    if (updateError) {
      console.error('激活卡密失败:', updateError)
      return NextResponse.json(
        { error: '激活卡密失败' },
        { status: 500 }
      )
    }

    // 记录激活日志
    await supabase
      .from('booking_logs')
      .insert({
        user_id: user.id,
        action_type: 'card_activated',
        status: 'success',
        message: `卡密 ${cardCode} 激活成功`,
        created_at: now.toISOString()
      })

    return NextResponse.json({
      success: true,
      message: '卡密激活成功',
      card: {
        id: updatedCard.id,
        code: updatedCard.code,
        plan: updatedCard.plan,
        billing_cycle: updatedCard.billing_cycle,
        monthly_credits: updatedCard.monthly_credits,
        features: updatedCard.features,
        status: updatedCard.status,
        activated_at: updatedCard.activated_at,
        expires_at: updatedCard.expires_at
      }
    })

  } catch (error) {
    console.error('激活卡密API错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
