# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is **WBAssist 自动预约系统** - a Chrome extension for automating Wildberries warehouse booking and supply management. The extension operates in Chinese/Russian markets and handles the complex workflow of booking warehouse slots for supply deliveries.

**Core Functionality:**
- Authentication with Wildberries seller portal using cookies and tokens
- Multi-store supplier management and switching
- Automated warehouse slot booking with monitoring
- Real-time order status tracking with persistent storage
- ID mapping between pre-orders and supply orders during lifecycle transitions

## Architecture Overview

### Extension Structure
- **Manifest V3 Extension**: Uses service worker for background operations
- **Single Page Application**: Main UI in `index.html` with modular JavaScript components
- **Background Service Worker**: Handles authentication, API calls, and automated booking
- **Chrome Storage API**: Persistent state management across sessions

### Core Components

#### 1. Authentication & API Layer (`background.js`, `js/api-service.js`)
- **WB API Integration**: Communicates with seller.wildberries.ru endpoints
- **Multi-store Support**: Manages multiple supplier accounts with cookie-based switching  
- **Token Management**: Handles `authorizev3` tokens and `wbx-validation-key` cookies

#### 2. Data Management (`js/real-data-service.js`, `js/booking-storage.js`)
- **Real Data Service**: Converts WB API data to frontend format, manages caching
- **Booking Storage**: Persistent state management using Chrome storage API
- **ID Mapping System**: Tracks order lifecycle (preorderId → supplyId transitions)

#### 3. UI Layer (`js/main.js`, `index.html`)
- **Order Management**: Paginated order lists with real-time status updates
- **Booking Wizard**: User interface for configuring automated booking
- **Status Monitoring**: Real-time feedback for booking progress

#### 4. Automated Booking System (`background.js`)
- **Background Monitoring**: Polls WB API for optimal booking slots
- **Automatic Execution**: Opens WB pages in background tabs to perform booking
- **Smart Retry**: Handles failures with exponential backoff

## Key Development Commands

### Testing
```bash
# Load extension in Chrome Developer Mode
# Navigate to chrome://extensions/
# Enable "Developer mode" and click "Load unpacked"
# Select this directory
```

### Debugging
```bash
# Background script console
# Go to chrome://extensions/ → WBAssist → "Inspect views: service worker"

# Content script console  
# Open extension popup/main page and use browser DevTools
```

## Architecture Patterns

### Service Layer Pattern
Each major component is implemented as a service class:
- `WBApiService`: API communication
- `RealDataService`: Data transformation and caching
- `BookingStorageService`: Persistent storage
- `WildberriesBookingApp`: Main application controller

### Event-Driven Communication
- Chrome runtime messaging between background and content scripts
- Custom events for UI updates (`bookingStatusChanged`, `monitoringStatusChanged`)
- Storage change listeners for multi-tab synchronization

### State Management
- **Persistent**: Chrome storage API for booking states, configurations, ID mappings
- **Cached**: In-memory caching in `RealDataService` with TTL
- **Real-time**: Event-driven updates across all components

### ID Lifecycle Management
The system handles complex order ID transitions:
1. **Pre-order Stage**: Orders have `preorderId`, no `supplyId`
2. **Booking Process**: User initiates booking for `preorderId`
3. **Supply Stage**: After booking, order gets `supplyId`, `preorderId` becomes null
4. **ID Mapping**: System maintains `preorderId → supplyId` mapping for continuity

## Key Configuration

### Wildberries API Endpoints
- Supply List: `https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/listSupplies`
- Acceptance Costs: `https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/getAcceptanceCosts`
- Supplier Portal: `https://seller.wildberries.ru/ns/suppliers/suppliers-portal-core/suppliers`

### Authentication Requirements
- `authorizev3`: Bearer token from localStorage
- `wbx-validation-key`: Validation cookie
- `x-supplier-id`: Current supplier context
- Origin/Referer headers matching Wildberries domain

### Storage Keys
- `wb_booking_states`: Order booking status and metadata
- `wb_booking_configs`: User booking preferences per order
- `wb_id_mappings`: preorderId to supplyId mappings

## Development Guidelines

### Error Handling
- Always wrap WB API calls in try-catch with meaningful error messages
- Use fallback mechanisms when Chrome APIs are unavailable
- Implement retry logic for network operations

### State Consistency  
- Update both persistent storage and cached data simultaneously
- Clear caches after state changes to ensure fresh data
- Handle multi-tab synchronization via storage change events

### Background Operations
- Use `active: false` when creating tabs for automated operations
- Implement proper cleanup (close tabs, clear intervals) after completion
- Handle race conditions in concurrent booking operations

### API Rate Limiting
- Use random intervals for monitoring (30s-5min) to avoid detection
- Implement exponential backoff for failed requests
- Respect WB's API usage patterns by mimicking browser behavior

## Localization Notes
The extension operates in Chinese/Russian markets:
- UI text is primarily in Chinese (中文)
- WB API responses are in Russian
- Error messages should be in Chinese for user-facing components
- Console logs can be in English for debugging

## Security Considerations
- Never log sensitive cookies or tokens
- Use secure origins for all WB API communications
- Validate all user inputs before API calls
- Implement proper CORS handling for cross-origin requests

## Critical Bug Fixes & Lessons Learned

### Date Range Filtering Bug (Fixed 2025-08-24)
**Issue**: Automated booking system was not respecting user-selected dates with buffer days, always selecting the first available date instead of dates within the user's preferred range.

**Root Cause**: In `background.js`, the `filterCostsByUserConditions()` function had incorrect date range logic:
```javascript
// WRONG - Only checked ONE date (selected date + buffer days)
const targetDate = new Date(selectedDate);
targetDate.setDate(targetDate.getDate() + bufferDays);
return isSameDay(costDate, targetDate);
```

**Correct Solution**: Generate a proper date range from selected date to selected date + buffer days:
```javascript  
// CORRECT - Check RANGE of dates (selected date through selected date + buffer days)
for (let i = 0; i <= bufferDays; i++) {
    const targetDate = new Date(baseDate);
    targetDate.setDate(baseDate.getDate() + i);
    if (isSameDay(costDate, targetDate)) {
        return true;
    }
}
```

**Example**: 
- User selects: 2025-08-27, buffer days: 1
- **Wrong behavior**: Only checked 2025-08-28 
- **Correct behavior**: Checks 2025-08-27 AND 2025-08-28

**Location**: `background.js` lines ~1425-1460 in `filterCostsByUserConditions()` function

**Testing Method**: Added comprehensive debug logging to trace:
- WB API slot availability queries (every 5-30s intervals)  
- Date filtering logic with detailed step-by-step analysis
- Final selection results showing matched dates and coefficients

**Prevention**: Always implement date range logic by iterating through each day in the range, never assume single-date calculations for range operations.