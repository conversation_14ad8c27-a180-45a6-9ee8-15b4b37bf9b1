import { NextResponse } from "next/server";

// 产品数据定义
interface ProductFeature {
  id: string;
  text: string;
}

interface Product {
  id: string;
  name: string;
  title: string; // 产品标题
  description: string;
  price: string;
  priceLabel: string; // 价格标签
  originalPrice?: string; // 原价（用于显示折扣）
  badge?: string; // 徽章（如"节省 20%"）
  isSubscription: boolean;
  subscriptionPeriod?: string;
  features: ProductFeature[]; // 产品特性列表
  popular?: boolean; // 是否推荐
  
  // License相关配置（与架构蓝本保持一致）
  license_type: string; // "pro" | "ultimate"（移除basic，因为免费版不通过支付）
  duration_days: number; // 卡密有效期天数
  monthly_quota: number; // 月度预约次数限制
  is_free: boolean; // 是否免费
  auto_activate: boolean; // 是否自动激活
}

interface FreeTierInfo {
  description: string;
  quota: number;
  how_to_get: string;
}

interface ProductsResponse {
  products: Record<string, Product>;
  free_tier_info: FreeTierInfo;
}

// WBAssist付费产品数据库（与架构蓝本PRODUCT_LICENSE_MAPPING保持一致）
const products: Record<string, Product> = {
  "basic-onetime": {
    id: "basic-onetime",
    name: "WBAssist免费版",
    title: "免费版",
    description: "每个店铺免费获得1次预约机会，体验基础功能。",
    price: "0",
    priceLabel: "",
    isSubscription: false,
    popular: false,
    license_type: "basic",
    duration_days: 365, // 足够长的有效期
    monthly_quota: 1,
    is_free: true,
    auto_activate: true,
    features: [
      { id: "basic-1", text: "1次免费预约机会" },
      { id: "basic-2", text: "基础预约功能" },
      { id: "basic-3", text: "店铺管理" },
      { id: "basic-4", text: "预约历史查看" },
      { id: "basic-5", text: "社区支持" },
    ],
  },
  "pro-monthly": {
    id: "pro-monthly",
    name: "WBAssist专业版月卡",
    title: "专业版",
    description: "解锁更多预约次数和高级功能，提升预约成功率。",
    price: "0.01",
    priceLabel: "/月",
    isSubscription: true,
    subscriptionPeriod: "monthly",
    popular: true,
    license_type: "pro",
    duration_days: 30,
    monthly_quota: 3,
    is_free: false,
    auto_activate: false,
    features: [
      { id: "pro-1", text: "每月3次预约额度" },
      { id: "pro-2", text: "智能缓冲天数设置" },
      { id: "pro-3", text: "自定义系数限制" },
      { id: "pro-4", text: "多时段并发预约" },
      { id: "pro-5", text: "飞书/邮件通知" },
      { id: "pro-6", text: "优先客服支持" },
    ],
  },
  "pro-yearly": {
    id: "pro-yearly",
    name: "WBAssist专业版年卡",
    title: "专业版年付",
    description: "年付享受更大优惠，解锁所有专业功能。",
    price: "0.03",
    priceLabel: "/年",
    originalPrice: "0.12",
    badge: "节省 20%",
    isSubscription: true,
    subscriptionPeriod: "yearly",
    popular: false,
    license_type: "pro",
    duration_days: 365,
    monthly_quota: 3,
    is_free: false,
    auto_activate: false,
    features: [
      { id: "pro-1", text: "每月3次预约额度" },
      { id: "pro-2", text: "智能缓冲天数设置" },
      { id: "pro-3", text: "自定义系数限制" },
      { id: "pro-4", text: "多时段并发预约" },
      { id: "pro-5", text: "飞书/邮件通知" },
      { id: "pro-6", text: "优先客服支持" },
      { id: "pro-7", text: "年付专享优惠20%" },
    ],
  },
  "ultimate-monthly": {
    id: "ultimate-monthly",
    name: "WBAssist旗舰版月卡",
    title: "旗舰版",
    description: "无限预约次数，享受最高级别的服务和支持。",
    price: "0.02",
    priceLabel: "/月",
    isSubscription: true,
    subscriptionPeriod: "monthly",
    popular: false,
    license_type: "ultimate",
    duration_days: 30,
    monthly_quota: 999, // 999表示实际无限制
    is_free: false,
    auto_activate: false,
    features: [
      { id: "ultimate-1", text: "无限预约次数" },
      { id: "ultimate-2", text: "最高优先级队列" },
      { id: "ultimate-3", text: "批量预约功能" },
      { id: "ultimate-4", text: "高级筛选器" },
      { id: "ultimate-5", text: "自动重试机制" },
      { id: "ultimate-6", text: "专属客服经理" },
      { id: "ultimate-7", text: "API接口访问" },
      { id: "ultimate-8", text: "定制化功能开发" },
    ],
  },
  "ultimate-yearly": {
    id: "ultimate-yearly",
    name: "WBAssist旗舰版年卡",
    title: "旗舰版年付",
    description: "年付享受更大优惠，无限预约次数，享受最高级别的服务和支持。",
    price: "0.04",
    priceLabel: "/年",
    originalPrice: "0.24",
    badge: "节省 25%",
    isSubscription: true,
    subscriptionPeriod: "yearly",
    popular: false,
    license_type: "ultimate",
    duration_days: 365,
    monthly_quota: 999, // 999表示实际无限制
    is_free: false,
    auto_activate: false,
    features: [
      { id: "ultimate-1", text: "无限预约次数" },
      { id: "ultimate-2", text: "最高优先级队列" },
      { id: "ultimate-3", text: "批量预约功能" },
      { id: "ultimate-4", text: "高级筛选器" },
      { id: "ultimate-5", text: "自动重试机制" },
      { id: "ultimate-6", text: "专属客服经理" },
      { id: "ultimate-7", text: "API接口访问" },
      { id: "ultimate-8", text: "定制化功能开发" },
      { id: "ultimate-9", text: "年付专享优惠25%" },
    ],
  },
};

// 免费版权益信息（不是产品，是系统默认权益）
const free_tier_info: FreeTierInfo = {
  description: "新用户每个店铺默认获得1次免费预约机会",
  quota: 1,
  how_to_get: "通过插件同步店铺即可自动激活免费配额"
};

// GET请求处理函数 - 获取所有付费产品和免费版说明
export async function GET() {
  const response: ProductsResponse = {
    products,
    free_tier_info
  };
  
  return NextResponse.json(response);
}
