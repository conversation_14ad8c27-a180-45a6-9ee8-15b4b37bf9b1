// 预约状态持久化存储服务
// 使用Chrome扩展storage API管理预约状态

class BookingStorageService {
    constructor() {
        this.storageKey = 'wb_booking_states';
        this.configKey = 'wb_booking_configs';
        this.mappingKey = 'wb_id_mappings'; // 新增：ID映射存储键
        this.isInitialized = false;
        this.bookingStates = new Map(); // 内存缓存
        this.bookingConfigs = new Map(); // 预约配置缓存
        this.idMappings = new Map(); // 新增：preorderId -> supplyId 映射
        this.listeners = new Set(); // 状态变化监听器
    }

    // 初始化存储服务
    async initialize() {
        try {
            if (typeof chrome === 'undefined' || !chrome.storage) {
                console.warn('Chrome storage API不可用，使用内存存储');
                this.isInitialized = true;
                return true;
            }

            // 从存储中恢复状态
            await this.loadFromStorage();
            
            // 监听存储变化（多标签页同步）
            chrome.storage.onChanged.addListener((changes, namespace) => {
                if (namespace === 'local') {
                    if (changes[this.storageKey]) {
                        this.handleStorageChange(changes[this.storageKey]);
                    }
                }
            });

            this.isInitialized = true;
            return true;
        } catch (error) {
            console.error('预约状态存储服务初始化失败:', error);
            this.isInitialized = true; // 即使失败也标记为初始化，使用内存存储
            return false;
        }
    }

    // 从存储中加载数据
    async loadFromStorage() {
        try {
            const result = await chrome.storage.local.get([this.storageKey, this.configKey, this.mappingKey]);

            // 恢复预约状态
            if (result[this.storageKey]) {
                const states = result[this.storageKey];
                this.bookingStates.clear();
                Object.entries(states).forEach(([key, value]) => {
                    this.bookingStates.set(key, value);
                });

            }

            // 恢复预约配置
            if (result[this.configKey]) {
                const configs = result[this.configKey];
                this.bookingConfigs.clear();
                Object.entries(configs).forEach(([key, value]) => {
                    this.bookingConfigs.set(key, value);
                });

            }

            // 恢复ID映射
            if (result[this.mappingKey]) {
                const mappings = result[this.mappingKey];
                this.idMappings.clear();
                Object.entries(mappings).forEach(([key, value]) => {
                    this.idMappings.set(key, value);
                });

            }
        } catch (error) {
            console.error('从存储加载数据失败:', error);
        }
    }

    // 保存到存储
    async saveToStorage() {
        try {
            if (typeof chrome === 'undefined' || !chrome.storage) {
                return; // 无存储API，只使用内存
            }

            const states = Object.fromEntries(this.bookingStates);
            const configs = Object.fromEntries(this.bookingConfigs);
            const mappings = Object.fromEntries(this.idMappings);

            await chrome.storage.local.set({
                [this.storageKey]: states,
                [this.configKey]: configs,
                [this.mappingKey]: mappings
            });


        } catch (error) {
            console.error('保存到存储失败:', error);
        }
    }

    // 处理存储变化（多标签页同步）
    handleStorageChange(change) {
        if (change.newValue) {
            const newStates = change.newValue;
            
            // 检查哪些状态发生了变化
            const changedKeys = [];
            Object.entries(newStates).forEach(([key, value]) => {
                const currentValue = this.bookingStates.get(key);
                if (!currentValue || JSON.stringify(currentValue) !== JSON.stringify(value)) {
                    this.bookingStates.set(key, value);
                    changedKeys.push(key);
                }
            });

            // 通知监听器
            if (changedKeys.length > 0) {
                this.notifyListeners(changedKeys);
            }
        }
    }

    // 设置预约状态
    async setBookingState(orderId, state, additionalData = {}) {
        const key = String(orderId);
        const timestamp = new Date().toISOString();
        
        const stateData = {
            orderId: orderId,
            status: state,
            timestamp: timestamp,
            lastUpdated: timestamp,
            ...additionalData
        };

        // 如果是更新现有状态，保留一些历史信息
        const existingState = this.bookingStates.get(key);
        if (existingState) {
            stateData.startTime = existingState.startTime || timestamp;
            stateData.history = existingState.history || [];
            stateData.history.push({
                status: existingState.status,
                timestamp: existingState.lastUpdated || existingState.timestamp
            });
        } else {
            stateData.startTime = timestamp;
            stateData.history = [];
        }

        this.bookingStates.set(key, stateData);
        await this.saveToStorage();
        

        this.notifyListeners([key]);
        
        return stateData;
    }

    // 获取预约状态
    getBookingState(orderId) {
        const key = String(orderId);
        return this.bookingStates.get(key) || null;
    }

    // 获取所有预约状态
    getAllBookingStates() {
        return Object.fromEntries(this.bookingStates);
    }

    // 删除预约状态
    async removeBookingState(orderId) {
        const key = String(orderId);
        const existed = this.bookingStates.has(key);
        
        this.bookingStates.delete(key);
        this.bookingConfigs.delete(key);
        
        if (existed) {
            await this.saveToStorage();

            this.notifyListeners([key]);
        }
        
        return existed;
    }

    // 设置预约配置
    async setBookingConfig(orderId, config) {
        const key = String(orderId);
        this.bookingConfigs.set(key, {
            ...config,
            orderId: orderId,
            timestamp: new Date().toISOString()
        });
        
        await this.saveToStorage();

        
        return this.bookingConfigs.get(key);
    }

    // 获取预约配置
    getBookingConfig(orderId) {
        const key = String(orderId);
        return this.bookingConfigs.get(key) || null;
    }

    // 获取活跃的预约（状态为booking的订单）
    getActiveBookings() {
        const activeBookings = [];
        this.bookingStates.forEach((state, key) => {
            if (state.status === 'booking') {
                activeBookings.push({
                    orderId: key,
                    ...state,
                    config: this.bookingConfigs.get(key)
                });
            }
        });
        return activeBookings;
    }

    // 清理过期状态（可选，用于清理旧数据）
    async cleanupExpiredStates(maxAge = 30 * 24 * 60 * 60 * 1000) { // 默认30天
        const now = Date.now();
        const keysToRemove = [];
        
        this.bookingStates.forEach((state, key) => {
            const stateTime = new Date(state.timestamp).getTime();
            if (now - stateTime > maxAge && state.status !== 'booking') {
                keysToRemove.push(key);
            }
        });

        if (keysToRemove.length > 0) {
            keysToRemove.forEach(key => {
                this.bookingStates.delete(key);
                this.bookingConfigs.delete(key);
            });
            
            await this.saveToStorage();

        }
        
        return keysToRemove.length;
    }

    // 添加状态变化监听器
    addListener(callback) {
        this.listeners.add(callback);
        return () => this.listeners.delete(callback); // 返回取消监听的函数
    }

    // 通知所有监听器
    notifyListeners(changedKeys) {
        this.listeners.forEach(callback => {
            try {
                callback(changedKeys);
            } catch (error) {
                console.error('状态变化监听器执行失败:', error);
            }
        });
    }

    // 检查服务是否可用
    isAvailable() {
        return this.isInitialized;
    }

    // 获取统计信息
    getStats() {
        const stats = {
            totalStates: this.bookingStates.size,
            totalConfigs: this.bookingConfigs.size,
            activeBookings: 0,
            completedBookings: 0,
            failedBookings: 0
        };

        this.bookingStates.forEach(state => {
            switch (state.status) {
                case 'booking':
                    stats.activeBookings++;
                    break;
                case 'accepted':
                    stats.completedBookings++;
                    break;
                case 'failed':
                    stats.failedBookings++;
                    break;
            }
        });

        return stats;
    }

    // ==================== ID映射管理方法 ====================

    // 保存preorderId到supplyId的映射
    async saveIdMapping(preorderId, supplyId, metadata = {}) {
        if (!preorderId || !supplyId) {
            throw new Error('preorderId和supplyId都不能为空');
        }

        const mappingData = {
            preorderId: preorderId.toString(),
            supplyId: supplyId.toString(),
            createdAt: new Date().toISOString(),
            ...metadata
        };

        this.idMappings.set(preorderId.toString(), mappingData);
        await this.saveToStorage();

        // 通知监听器
        this.notifyListeners(['id_mappings']);

        return mappingData;
    }

    // 根据preorderId获取supplyId
    getSupplyIdByPreorderId(preorderId) {
        if (!preorderId) return null;

        const mapping = this.idMappings.get(preorderId.toString());
        return mapping ? mapping.supplyId : null;
    }

    // 根据supplyId获取preorderId
    getPreorderIdBySupplyId(supplyId) {
        if (!supplyId) return null;

        for (const [preorderId, mapping] of this.idMappings) {
            if (mapping.supplyId === supplyId.toString()) {
                return mapping.preorderId;
            }
        }
        return null;
    }

    // 获取完整的映射信息
    getIdMapping(preorderId) {
        if (!preorderId) return null;
        return this.idMappings.get(preorderId.toString()) || null;
    }

    // 检查是否存在映射
    hasIdMapping(preorderId) {
        return this.idMappings.has(preorderId.toString());
    }

    // 删除映射
    async removeIdMapping(preorderId) {
        if (!preorderId) return false;

        const removed = this.idMappings.delete(preorderId.toString());
        if (removed) {
            await this.saveToStorage();
            this.notifyListeners(['id_mappings']);
        }
        return removed;
    }

    // 获取所有映射
    getAllIdMappings() {
        return Array.from(this.idMappings.entries()).map(([preorderId, mapping]) => ({
            preorderId,
            ...mapping
        }));
    }

    // 清理过期的映射（可选，用于清理旧数据）
    async cleanupExpiredMappings(maxAge = 90 * 24 * 60 * 60 * 1000) { // 默认90天
        const now = Date.now();
        const keysToRemove = [];

        this.idMappings.forEach((mapping, preorderId) => {
            const mappingTime = new Date(mapping.createdAt).getTime();
            if (now - mappingTime > maxAge) {
                keysToRemove.push(preorderId);
            }
        });

        if (keysToRemove.length > 0) {
            keysToRemove.forEach(key => {
                this.idMappings.delete(key);
            });

            await this.saveToStorage();
            this.notifyListeners(['id_mappings']);
        }

        return keysToRemove.length;
    }
}

// 创建全局预约存储服务实例
window.bookingStorage = new BookingStorageService();
