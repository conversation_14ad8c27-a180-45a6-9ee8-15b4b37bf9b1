import {
  UserPlusIcon,
  CogIcon,
  PlayIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";

// WBAssist工作流程数据
const workflowSteps = [
  {
    id: 1,
    icon: UserPlusIcon,
    title: "注册账号",
    description: "快速注册WBAssist账号，选择适合的套餐方案",
    details: [
      "邮箱注册，1分钟完成",
      "选择免费或付费套餐",
      "获得专属用户面板"
    ],
    color: "text-blue-500",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  {
    id: 2,
    icon: CogIcon,
    title: "配置插件",
    description: "安装Chrome插件，绑定您的Wildberries账号",
    details: [
      "一键安装Chrome插件",
      "安全绑定WB账号",
      "设置预约偏好"
    ],
    color: "text-green-500",
    bgColor: "bg-green-50",
    borderColor: "border-green-200"
  },
  {
    id: 3,
    icon: PlayIcon,
    title: "启动监控",
    description: "系统开始24/7智能监控，自动寻找最佳预约时机",
    details: [
      "实时监控仓位状态",
      "智能分析最佳时机",
      "自动执行预约操作"
    ],
    color: "text-purple-500",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  },
  {
    id: 4,
    icon: CheckCircleIcon,
    title: "预约成功",
    description: "收到预约成功通知，查看详细的统计报告",
    details: [
      "即时成功通知",
      "详细预约记录",
      "数据统计分析"
    ],
    color: "text-orange-500",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200"
  },
];

export default function Carousel() {
  return (
    <section className="bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="py-12 md:py-20">
          {/* Section header */}
          <div className="max-w-3xl mx-auto text-center pb-12 md:pb-16">
            <h2 className="h2 font-cabinet-grotesk text-gray-900">
              简单四步，开启智能预约
            </h2>
            <p className="text-xl text-gray-600 mt-4">
              从注册到成功预约，WBAssist让整个流程变得简单高效
            </p>
          </div>

          {/* Workflow Steps */}
          <div className="relative">
            {/* Connection Line */}
            <div className="hidden lg:block absolute top-24 left-0 right-0 h-0.5 bg-gray-200" aria-hidden="true">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-green-500 via-purple-500 to-orange-500 opacity-30"></div>
            </div>

            {/* Steps Grid */}
            <div className="grid gap-8 lg:grid-cols-4 lg:gap-12">
              {workflowSteps.map((step, index) => {
                const IconComponent = step.icon;
                return (
                  <div
                    key={step.id}
                    className="relative text-center"
                    data-aos="fade-up"
                    data-aos-delay={index * 100}
                  >
                    {/* Step Number */}
                    <div className="relative inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg border-4 border-gray-100 mb-6 lg:mb-8">
                      <span className="text-2xl font-bold text-gray-400">
                        {step.id}
                      </span>
                      {/* Icon Overlay */}
                      <div className={`absolute inset-0 flex items-center justify-center w-16 h-16 ${step.bgColor} rounded-full border-4 ${step.borderColor} opacity-0 hover:opacity-100 transition-opacity duration-300`}>
                        <IconComponent className={`w-8 h-8 ${step.color}`} />
                      </div>
                    </div>

                    {/* Content */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-bold text-gray-900">
                        {step.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {step.description}
                      </p>

                      {/* Details List */}
                      <ul className="text-sm text-gray-500 space-y-2">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-center justify-center lg:justify-start">
                            <svg className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Arrow for desktop */}
                    {index < workflowSteps.length - 1 && (
                      <div className="hidden lg:block absolute top-24 -right-6 transform -translate-y-1/2">
                        <svg className="w-6 h-6 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
