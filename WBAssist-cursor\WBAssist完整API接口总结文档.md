# WBAssist 完整API接口总结文档

## 📋 文档概述

本文档详细总结了WBAssist自动预约系统中的所有API接口，包括：
1. **Chrome插件API接口** - 插件内部使用的API服务类
2. **网站端API接口** - Web应用的Next.js API路由
3. **Supabase Edge Functions** - 服务端业务逻辑处理

---

## 🔧 Chrome插件API接口 (cnakubengbi/js/wbassist-api-service.js)

### 基础配置

#### 类构造器
```javascript
class WBAssistApiService {
    constructor()
    getBackendUrl()           // 获取网站端URL（本地/生产环境自适应）
    getSupabaseUrl()          // 获取Supabase URL配置
}
```

#### 环境配置
- **本地开发**：`http://localhost:3000`
- **生产环境**：`https://wbassist.vercel.app`
- **Supabase**：`https://cdnpddlbecxqnpkfkvid.supabase.co`

### 认证管理接口

#### 1. 初始化与认证
```javascript
async initializeFromWebsite()                    // 初始化API服务，从存储或background获取认证
async getAuthFromBackgroundWithRetry()           // 带重试的认证获取
async getAuthFromBackground()                    // 从background script获取认证信息
async getAuthFromStorage()                       // 从Chrome存储获取认证信息
async saveAuthToStorage(authData)               // 保存认证信息到Chrome存储
async clearAuth()                                // 清理认证状态（本地+Cookie）
```

#### 2. 登录流程
```javascript
async openLoginPage(forceReauth = false)        // 打开登录页面，支持强制重新认证
async checkAuthStatus()                          // 检查认证状态
async handleAuthSuccess(authData)               // 处理认证成功回调
```

### 业务接口 (调用Supabase Edge Functions)

#### 3. 店铺管理
```javascript
async getStoreCapabilities(wbSupplierId)        // 获取店铺权限和配额状态
// 调用: GET ${supabaseUrl}/functions/v1/capabilities?wb_supplier_id=${wbSupplierId}

async syncStores(authToken, wbxValidKey)        // 同步店铺到后端系统
// 调用: POST ${supabaseUrl}/functions/v1/stores-register
```

#### 4. 许可证管理
```javascript
async bindLicense(licenseKey, wbSupplierId)     // 绑定卡密到店铺
// 调用: POST ${supabaseUrl}/functions/v1/license-upgrade
```

#### 5. 任务管理
```javascript
async createTask(wbSupplierId, wbPreorderId, taskConfig)  // 创建预约任务
// 调用: POST ${supabaseUrl}/functions/v1/tasks-create

async reportTaskSuccess(taskId, wbSupplyId, bookingSuccessAt, bookingDetails)  // 上报成功
// 调用: POST ${supabaseUrl}/functions/v1/tasks-report-success

async reportTaskFailure(taskId, status, errorInfo)  // 上报失败
// 调用: POST ${supabaseUrl}/functions/v1/tasks-report-failure

async getTasks(storeId, status, page, limit)    // 获取任务列表
// 调用: GET ${supabaseUrl}/functions/v1/tasks-list
```

#### 6. 配额统计
```javascript
async getCreditsUsage(storeId, monthPeriod)     // 获取配额使用情况
// 调用: GET ${supabaseUrl}/functions/v1/credits-usage
```

---

## 🌐 网站端API接口 (WBAssist-web/app/api/)

### 插件专用接口

#### 1. 认证验证
```http
GET /api/extension/auth
```
- **功能**：验证插件用户认证状态
- **认证**：Bearer Token (JWT)
- **响应**：
```json
{
  "authenticated": true,
  "user": {
    "id": "user_uuid",
    "email": "<EMAIL>"
  }
}
```

```http
POST /api/extension/auth
```
- **功能**：Token验证
- **请求体**：`{ "token": "jwt_token" }`

#### 2. 卡密管理
```http
GET /api/extension/cards
```
- **功能**：获取用户卡密信息
- **认证**：Session Cookie
- **响应**：用户激活的卡密列表

```http
POST /api/extension/cards
```
- **功能**：卡密操作（激活/绑定等）

### 用户管理接口

#### 3. 许可证管理
```http
GET /api/me/licenses
```
- **功能**：获取用户购买的所有卡密
- **认证**：Session Cookie
- **响应**：
```json
{
  "success": true,
  "licenses": [
    {
      "order_id": "order_123",
      "product_name": "WBAssist Pro版",
      "license_key": "WBPRO-2024-XXXX-XXXX",
      "license_type": "pro",
      "duration_days": 30,
      "monthly_quota": 100,
      "binding_id": "binding_uuid",
      "store_name": "店铺名称",
      "activated_at": "2024-01-01T00:00:00Z",
      "expires_at": "2024-01-31T00:00:00Z",
      "status": "active"
    }
  ]
}
```

#### 4. 订单管理
```http
GET /api/me/orders?page=1&limit=20&status=success
```
- **功能**：获取用户订单历史
- **认证**：Session Cookie
- **参数**：
  - `page`: 页码 (默认1)
  - `limit`: 每页数量 (默认20)
  - `status`: 订单状态过滤
- **响应**：
```json
{
  "orders": [
    {
      "id": "transaction_id",
      "out_trade_no": "order_123",
      "product_name": "WBAssist Pro版",
      "amount": "99.00",
      "status": "success",
      "payment_method": "zpay",
      "created_at": "2024-01-01T00:00:00Z",
      "license": {
        "id": "license_uuid",
        "status": "active",
        "expires_at": "2024-01-31T00:00:00Z",
        "bound_to_store": "店铺名称"
      }
    }
  ],
  "pagination": {
    "total": 5,
    "page": 1,
    "limit": 20,
    "total_pages": 1
  }
}
```

### 产品与支付接口

#### 5. 产品信息
```http
GET /api/products
```
- **功能**：获取所有可购买的产品
- **认证**：无需认证
- **响应**：
```json
{
  "products": {
    "pro-monthly": {
      "id": "pro-monthly",
      "name": "WBAssist Pro版",
      "title": "专业版",
      "price": "99",
      "license_type": "pro",
      "duration_days": 30,
      "monthly_quota": 100,
      "features": [
        { "id": "1", "text": "每月100次预约额度" },
        { "id": "2", "text": "智能监控与自动预约" }
      ]
    }
  },
  "free_tier_info": {
    "description": "每个店铺免费获得1次预约机会",
    "quota": 1,
    "how_to_get": "注册后自动获得"
  }
}
```

#### 6. 支付相关
```http
GET /api/payment/license/{orderId}
```
- **功能**：获取支付成功后的卡密信息
- **认证**：Session Cookie
- **参数**：`orderId` - 订单号
- **响应**：
```json
{
  "success": true,
  "order": {
    "out_trade_no": "order_123",
    "product_name": "WBAssist Pro版",
    "amount": "99.00",
    "status": "success",
    "payment_method": "zpay"
  },
  "license": {
    "license_key": "WBPRO-2024-XXXX-XXXX",
    "license_type": "pro",
    "duration_days": 30,
    "monthly_quota": 100
  },
  "bind_instructions": {
    "steps": [
      "复制上面的卡密",
      "打开WBAssist插件",
      "点击绑定卡密按钮",
      "粘贴卡密并选择店铺"
    ],
    "bind_url": "chrome-extension://插件ID/index.html"
  }
}
```

### 支付处理接口

#### 7. 支付URL生成
```http
POST /api/checkout/providers/zpay/url
```
- **功能**：生成支付链接
- **认证**：Session Cookie

#### 8. 支付回调
```http
POST /api/checkout/providers/zpay/webhook
```
- **功能**：处理支付平台回调
- **认证**：Webhook签名验证

---

## ⚡ Supabase Edge Functions API

> **调用基础URL**: `https://cdnpddlbecxqnpkfkvid.supabase.co/functions/v1/`
> 
> **认证方式**: Bearer Token (JWT)

### 认证与店铺管理

#### 1. 插件认证验证
```http
POST /functions/v1/extension-auth
```
- **功能**：验证插件用户身份
- **认证**：Bearer Token
- **响应**：用户认证状态和基本信息

#### 2. 店铺同步注册
```http
POST /functions/v1/stores-register
```
- **功能**：同步WB店铺信息到系统
- **认证**：Bearer Token
- **请求体**：
```json
{
  "wb_auth_token": "wb_access_token",
  "wbx_validation_key": "validation_key",
  "stores_data": [...]
}
```
- **响应**：
```json
{
  "success": true,
  "existing_stores": [...],  // 已存在的店铺（含共享权限信息）
  "new_stores": [...],       // 新创建的店铺
  "summary": {
    "total_existing": 2,
    "total_new": 1,
    "shared_access_count": 1
  }
}
```

#### 3. 店铺权限查询
```http
GET /functions/v1/capabilities?wb_supplier_id={storeId}
```
- **功能**：获取店铺权限和配额状态
- **认证**：Bearer Token
- **响应**：
```json
{
  "success": true,
  "store_info": {
    "wb_supplier_id": "12345",
    "name": "店铺名称"
  },
  "license_info": {
    "license_type": "pro",
    "monthly_quota": 100,
    "current_month_used": 25,
    "remaining_quota": 75,
    "expires_at": "2024-01-31T00:00:00Z"
  },
  "sharing_info": {
    "is_shared_access": true,
    "sharing_type": "global_permission",
    "original_owner_id": "other_user_uuid",
    "original_owner_email": "<EMAIL>",
    "shared_since": "2024-01-15T00:00:00Z"
  }
}
```

### 许可证管理

#### 4. 卡密绑定升级
```http
POST /functions/v1/license-upgrade
```
- **功能**：绑定卡密到指定店铺
- **认证**：Bearer Token
- **请求体**：
```json
{
  "license_key": "WBPRO-2024-XXXX-XXXX",
  "wb_supplier_id": "12345"
}
```
- **响应**：绑定结果和权限信息

### 任务管理

#### 5. 创建预约任务
```http
POST /functions/v1/tasks-create
```
- **功能**：创建基于preorderId的预约任务
- **认证**：Bearer Token
- **请求体**：
```json
{
  "wb_supplier_id": "12345",
  "wb_preorder_id": "预订单号",
  "task_config": {
    "selected_dates": ["2024-01-15", "2024-01-16"],
    "max_coefficient": 20,
    "buffer_days": 3,
    "time_preferences": ["morning", "afternoon"]
  }
}
```
- **响应**：
```json
{
  "success": true,
  "task_id": "task_uuid",
  "message": "预约任务创建成功",
  "quota_deducted": false,
  "remaining_quota": 75
}
```

#### 6. 上报预约成功
```http
POST /functions/v1/tasks-report-success
```
- **功能**：预约成功时上报结果并扣减配额
- **认证**：Bearer Token
- **请求体**：
```json
{
  "task_id": "task_uuid",
  "wb_supply_id": "送货单号",
  "booking_success_at": "2024-01-15T10:30:00Z",
  "booking_details": {
    "delivery_date": "2024-01-20",
    "coefficient": 5.2,
    "cost": 1000.50
  }
}
```
- **响应**：
```json
{
  "success": true,
  "message": "预约成功已记录",
  "quota_deducted": true,
  "remaining_quota": 74,
  "id_mapping": {
    "wb_preorder_id": "预订单号",
    "wb_supply_id": "送货单号"
  }
}
```

#### 7. 上报预约失败
```http
POST /functions/v1/tasks-report-failure
```
- **功能**：预约失败时上报，不扣减配额
- **认证**：Bearer Token
- **请求体**：
```json
{
  "task_id": "task_uuid",
  "status": "failed",  // "failed" | "cancelled" | "timeout"
  "error_info": {
    "error_code": "COEFFICIENT_TOO_HIGH",
    "error_message": "系数超过用户设定的最大值",
    "failed_at": "2024-01-15T10:30:00Z"
  }
}
```

#### 8. 任务列表查询
```http
GET /functions/v1/tasks-list?store_id={storeId}&status={status}&page={page}&limit={limit}
```
- **功能**：获取用户的任务历史
- **认证**：Bearer Token
- **参数**：
  - `store_id`: 店铺ID过滤（可选）
  - `status`: 任务状态过滤（可选）
  - `page`: 页码
  - `limit`: 每页数量

### 配额统计

#### 9. 配额使用查询
```http
GET /functions/v1/credits-usage?store_id={storeId}&month_period={month}
```
- **功能**：获取配额使用统计
- **认证**：Bearer Token
- **参数**：
  - `store_id`: 店铺ID过滤（可选）
  - `month_period`: 月份过滤（可选，格式：2024-01）
- **响应**：
```json
{
  "success": true,
  "usage_summary": {
    "current_month": {
      "month": "2024-01",
      "total_quota": 100,
      "used_quota": 25,
      "remaining_quota": 75,
      "success_count": 20,
      "failure_count": 5
    },
    "stores_breakdown": [
      {
        "wb_supplier_id": "12345",
        "store_name": "店铺A",
        "used_quota": 15,
        "success_count": 12,
        "last_used": "2024-01-15T00:00:00Z"
      }
    ]
  },
  "recent_usage": [
    {
      "id": "usage_uuid",
      "wb_supplier_id": "12345",
      "wb_preorder_id": "预订单号",
      "wb_supply_id": "送货单号",
      "used_at": "2024-01-15T10:30:00Z",
      "operation_type": "booking_success"
    }
  ]
}
```

---

## 🔒 认证与安全

### 认证方式

#### 插件端认证
- **方式**：JWT Bearer Token
- **获取**：通过网站端登录后获得
- **传递**：`Authorization: Bearer {token}` header
- **存储**：Chrome extension storage

#### 网站端认证
- **方式**：Supabase Auth Session Cookie
- **管理**：自动处理session生命周期
- **验证**：通过`createServerSupabaseClient()`

### 权限验证层级

#### Edge Functions验证顺序
1. **身份验证**：JWT token有效性
2. **店铺权限**：验证操作店铺的访问权限
3. **卡密状态**：检查绑定卡密的有效性
4. **配额验证**：检查月度剩余配额
5. **业务逻辑**：具体功能的参数验证

#### 全局权限共享机制 (v4.0)
- **核心原理**：任何认证用户都可以使用已有店铺的license和配额
- **权限查询**：优先查找当前用户绑定，其次查找全局共享
- **配额共享**：基于`wb_supplier_id`的全局配额池
- **所有权保留**：原始绑定关系保持不变

### 错误响应码

| 状态码 | 含义 | 场景 |
|--------|------|------|
| 401 | 未认证 | JWT token缺失或无效 |
| 403 | 权限不足 | 操作非授权店铺 |
| 402 | 付费需求 | 卡密过期或配额不足 |
| 404 | 资源不存在 | 店铺、任务不存在 |
| 409 | 冲突状态 | preorderId重复 |
| 422 | 参数无效 | 请求参数格式错误 |
| 429 | 频率限制 | 请求过于频繁 |

---

## 📊 数据流向图

### 用户认证流程
```
插件点击登录 → 打开网站登录页 → Supabase Auth → 返回JWT → 插件保存Token
```

### 任务创建与执行流程
```
插件UI操作 → Edge Functions验证 → 创建任务 → 后台监控 → WB自动预约 → 上报结果 → 配额扣减
```

### 全局权限共享流程
```
用户A店铺绑定license → 用户B访问同店铺 → Edge Functions检查全局权限 → 返回共享权限状态
```

---

## 🎯 最佳实践

### API调用建议

#### 1. 错误处理
```javascript
try {
  const response = await fetch(apiUrl, options);
  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.error || 'API调用失败');
  }
  
  return data;
} catch (error) {
  console.error('API调用错误:', error);
  // 根据错误类型进行相应处理
}
```

#### 2. 认证Token管理
```javascript
// 检查token有效性
if (!this.authToken || this.isTokenExpired()) {
  await this.refreshAuth();
}

// 自动重试机制
if (response.status === 401) {
  await this.clearAuth();
  await this.initializeFromWebsite();
}
```

#### 3. 分页查询
```javascript
// 任务列表分页
const tasks = await this.getTasks(
  storeId,      // 店铺过滤
  'completed',  // 状态过滤
  1,           // 页码
  20           // 每页数量
);
```

### 性能优化

#### 1. 缓存策略
- **认证信息**：Chrome storage缓存
- **店铺列表**：内存缓存30分钟
- **配额信息**：实时查询（防止不一致）

#### 2. 并发控制
- **任务创建**：同一preorderId防重复
- **配额扣减**：原子操作保证一致性
- **监控轮询**：随机间隔避免同步

#### 3. 容错设计
- **网络重试**：指数退避策略
- **降级处理**：本地状态优先
- **监控恢复**：页面刷新后自动恢复

---

## 📝 更新历史

### v4.0 全局权限共享
- 新增全局权限共享机制
- 优化店铺权限查询逻辑
- 更新认证和配额验证流程

### v3.5 支付系统集成
- 完善支付API接口
- 新增卡密自动生成
- 优化订单管理功能

### v3.0 任务管理系统
- 实现preorderId→supplyId映射
- 新增任务生命周期管理
- 完善配额统计功能

---

## 🔗 相关文档

- [系统架构蓝本](./系统架构蓝本.md)
- [Chrome插件v4.0全局权限共享适配总结](./Chrome插件v4.0全局权限共享适配总结.md)
- [店铺权限全局共享重构技术文档](./店铺权限全局共享重构技术文档.md)

---

**文档版本**: v4.0.1
**最后更新**: 2024年1月 