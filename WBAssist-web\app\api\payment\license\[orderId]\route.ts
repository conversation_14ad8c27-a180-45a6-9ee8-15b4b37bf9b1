import { NextRequest, NextResponse } from "next/server";
import { createServerAdminClient, createServerSupabaseClient } from "@/utils/supabase/server";

interface OrderInfo {
  out_trade_no: string;
  product_name: string;
  amount: string;
  status: string;
  payment_method: string;
}

interface LicenseInfo {
  license_key: string;
  license_type: string;
  duration_days: number;
  monthly_quota: number;
  expires_at?: string;
}

interface BindInstructions {
  steps: string[];
  bind_url: string;
}

interface PaymentLicenseResponse {
  success: boolean;
  order: OrderInfo;
  license: LicenseInfo;
  bind_instructions: BindInstructions;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // 🔐 获取已验证的用户信息（安全方式）- 防止篡改支付信息
    const supabase = createServerSupabaseClient();
    const {
      data: { user },
      error: authError
    } = await supabase.auth.getUser(); // 向服务器验证，防止用户篡改获取别人的卡密

    // Check if the user is authenticated
    if (authError || !user) {
      return NextResponse.json(
        { error: "Unauthorized. Please login to proceed." },
        { status: 401 }
      );
    }

    const userId = user.id; // 经过验证的安全userId
    const { orderId } = params;

    if (!orderId) {
      return NextResponse.json(
        { error: "Missing orderId parameter" },
        { status: 400 }
      );
    }

    // Get the admin client for database operations
    const adminClient = createServerAdminClient();

    // Find the transaction and verify ownership
    const { data: transaction, error: transactionError } = await adminClient
      .from("zpay_transactions")
      .select("*")
      .eq("out_trade_no", orderId)
      .eq("user_id", userId)
      .single();

    if (transactionError || !transaction) {
      let errorMessage = "Order not found";
      if (transactionError && transactionError.code !== "PGRST116") {
        errorMessage = "Access denied to this order";
      }
      
      return NextResponse.json(
        { error: errorMessage },
        { status: 404 }
      );
    }

    // Check payment status
    if (transaction.status !== "success") {
      let message = "Order not paid successfully";
      if (transaction.status === "pending") {
        message = "Payment is still processing, please try again later";
      } else if (transaction.status === "failed") {
        message = "Payment failed";
      } else if (transaction.status === "cancelled") {
        message = "Order was cancelled";
      }
      
      return NextResponse.json(
        { error: message },
        { status: 400 }
      );
    }

    // Check if license was generated
    const metadata = transaction.metadata || {};
    const licenseKey = metadata.generated_license_key;
    
    if (!licenseKey) {
      return NextResponse.json(
        { error: "License not generated yet, please contact support" },
        { status: 500 }
      );
    }

    // Build response
    const orderInfo: OrderInfo = {
      out_trade_no: transaction.out_trade_no,
      product_name: metadata.product_name || "Unknown Product",
      amount: transaction.amount.toString(),
      status: transaction.status,
      payment_method: transaction.payment_method
    };

    const licenseInfo: LicenseInfo = {
      license_key: licenseKey,
      license_type: metadata.license_type || "pro",
      duration_days: metadata.license_duration_days || 30,
      monthly_quota: metadata.monthly_quota || 3
    };

    const bindInstructions: BindInstructions = {
      steps: [
        "复制上方卡密",
        "打开WBAssist插件", 
        "选择要绑定的店铺",
        "粘贴卡密并点击绑定",
        "绑定成功后即可开始使用"
      ],
      bind_url: "/dashboard/cards"
    };

    const response: PaymentLicenseResponse = {
      success: true,
      order: orderInfo,
      license: licenseInfo,
      bind_instructions: bindInstructions
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error("Payment license error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 