---
type: "always_apply"
---

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

WBAssist is a Wildberries reservation automation system built with Next.js 14 (App Router), TypeScript, and Supabase. The application helps manage supply planning and improve reservation success rates for Wildberries sellers.

## Development Commands

```bash
# Development server
pnpm dev          # Preferred - project uses pnpm
npm run dev       # Alternative
yarn dev          # Alternative

# Production build
pnpm build
npm run build

# Production server
pnpm start
npm run start

# Linting
pnpm lint
npm run lint
```

## Architecture

### App Router Structure
- `/app` - Next.js 14 App Router pages and layouts
- `/app/(auth)` - Authentication pages (signin, signup, reset-password)
- `/app/(default)` - Default layout group with main landing page
- `/app/dashboard` - User dashboard and card management
- `/app/api` - API routes for checkout, extension auth, products

### Key Components
- `/components` - Shared React components
  - `ExtensionBridge.tsx` - Browser extension communication bridge
  - `CourseNotice.tsx` - Course notification system
  - `/dashboard` - Dashboard-specific components
  - `/ui` - Base UI components (header, footer)

### Database & Authentication
- **Supabase** integration for auth and database
- Server-side auth: `utils/supabase/server.ts` 
- Client-side auth: `utils/supabase/client.ts`
- Admin client available with service role key

#### Supabase MCP Integration
- **Project ID**: `cdnpddlbecxqnpkfkvid`
- **Project Name**: WBAssist
- **Region**: ap-southeast-1
- **Status**: ACTIVE_HEALTHY
- **Database**: PostgreSQL 17.4.1

**Usage**: When you need to view, create, or manage database tables, you can directly connect to the Supabase project using the MCP integration. Use the project ID `cdnpddlbecxqnpkfkvid` for all database operations.

**Key Tables**:
- `zpay_transactions` - Payment transactions with ZPay integration
- Row Level Security (RLS) enabled on all tables

### Styling
- **Tailwind CSS** with custom configuration
- Custom fonts: Inter (Google) and Cabinet Grotesk (local)
- Custom CSS in `/app/css/style.css` and `/app/css/additional-styles/`

### Payment Integration
- ZPay payment provider integration
- API routes: `/api/checkout/providers/zpay/`
- Database migrations in `/migrations/`

### Browser Extension
- Extension authentication: `/app/api/extension/auth/`
- Card management API: `/app/api/extension/cards/`
- Bridge component for web-extension communication

## Important Notes

- Project uses Chinese language (lang="zh-CN")
- Custom font loading with local Cabinet Grotesk fonts
- Supabase SSR implementation for proper server-side rendering
- Middleware configured for authentication routing