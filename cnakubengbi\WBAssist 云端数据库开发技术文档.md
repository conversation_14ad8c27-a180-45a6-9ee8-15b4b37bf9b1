# WBAssist 云端数据库开发技术文档

## 目录
1. [可直接复用的核心组件](#1-可直接复用的核心组件)
2. [数据模型设计](#2-数据模型设计)
3. [API接口设计参考](#3-api接口设计参考)
4. [数据同步策略](#4-数据同步策略)
5. [迁移路径](#5-迁移路径)

---

## 1. 可直接复用的核心组件

### 1.1 核心业务逻辑类

#### **BookingStorageService 核心方法**（当前实际实现）
```javascript
// 位置: js/booking-storage.js
// 可直接移植的核心业务逻辑 - 已完整实现版本

class BookingStorageService {
    constructor() {
        this.storageKey = 'wb_booking_states';
        this.configKey = 'wb_booking_configs';
        this.mappingKey = 'wb_id_mappings'; // ID映射存储键
        this.bookingStates = new Map();
        this.bookingConfigs = new Map();
        this.idMappings = new Map(); // preorderId -> supplyId 映射
        this.listeners = new Set(); // 状态变化监听器
    }

    // ID映射管理 - 核心业务逻辑可直接复用
    async saveIdMapping(preorderId, supplyId, metadata = {}) {
        if (!preorderId || !supplyId) {
            throw new Error('preorderId和supplyId都不能为空');
        }

        const mappingData = {
            preorderId: preorderId.toString(),
            supplyId: supplyId.toString(),
            createdAt: new Date().toISOString(),
            ...metadata
        };

        this.idMappings.set(preorderId.toString(), mappingData);
        await this.saveToStorage();
        this.notifyListeners(['id_mappings']);

        return mappingData;
    }
    
    // 预约状态管理 - 状态机逻辑可直接复用
    async setBookingState(orderId, state, additionalData = {}) {
        const key = String(orderId);
        const timestamp = new Date().toISOString();
        
        const stateData = {
            orderId: orderId,
            status: state,
            timestamp: timestamp,
            lastUpdated: timestamp,
            ...additionalData
        };

        // 保留历史状态信息
        const existingState = this.bookingStates.get(key);
        if (existingState) {
            stateData.startTime = existingState.startTime || timestamp;
            stateData.history = existingState.history || [];
            stateData.history.push({
                status: existingState.status,
                timestamp: existingState.lastUpdated || existingState.timestamp
            });
        } else {
            stateData.startTime = timestamp;
            stateData.history = [];
        }

        this.bookingStates.set(key, stateData);
        await this.saveToStorage();
        this.notifyListeners([key]);
        
        return stateData;
    }

    // 双向ID查询 - 核心查找算法
    getSupplyIdByPreorderId(preorderId) {
        if (!preorderId) return null;
        const mapping = this.idMappings.get(preorderId.toString());
        return mapping ? mapping.supplyId : null;
    }

    getPreorderIdBySupplyId(supplyId) {
        if (!supplyId) return null;
        for (const [preorderId, mapping] of this.idMappings) {
            if (mapping.supplyId === supplyId.toString()) {
                return mapping.preorderId;
            }
        }
        return null;
    }

    // 多标签页状态同步机制
    handleStorageChange(change) {
        if (change.newValue) {
            const newStates = change.newValue;
            const changedKeys = [];
            Object.entries(newStates).forEach(([key, value]) => {
                const currentValue = this.bookingStates.get(key);
                if (!currentValue || JSON.stringify(currentValue) !== JSON.stringify(value)) {
                    this.bookingStates.set(key, value);
                    changedKeys.push(key);
                }
            });

            if (changedKeys.length > 0) {
                this.notifyListeners(changedKeys);
            }
        }
    }

    // 获取活跃预约（状态监控功能）
    getActiveBookings() {
        const activeBookings = [];
        this.bookingStates.forEach((state, key) => {
            if (state.status === 'booking' || state.status === 'monitoring') {
                activeBookings.push({
                    orderId: key,
                    ...state,
                    config: this.bookingConfigs.get(key)
                });
            }
        });
        return activeBookings;
    }

    // 数据清理功能
    async cleanupExpiredStates(maxAge = 30 * 24 * 60 * 60 * 1000) {
        const now = Date.now();
        const keysToRemove = [];
        
        this.bookingStates.forEach((state, key) => {
            const stateTime = new Date(state.timestamp).getTime();
            if (now - stateTime > maxAge && state.status !== 'booking' && state.status !== 'monitoring') {
                keysToRemove.push(key);
            }
        });

        if (keysToRemove.length > 0) {
            keysToRemove.forEach(key => {
                this.bookingStates.delete(key);
                this.bookingConfigs.delete(key);
            });
            await this.saveToStorage();
        }
        
        return keysToRemove.length;
    }
}
```

#### **RealDataService 数据处理逻辑**（当前实际实现）
```javascript
// 位置: js/real-data-service.js
// 订单数据处理和ID转换逻辑 - 已完整实现版本

class RealDataService {
    constructor() {
        this.currentStoreId = null;
        this.stores = [];
        this.cachedOrders = [];
        this.lastOrdersUpdate = null;
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    }

    // WB API状态映射 - 核心业务逻辑
    mapWbStatusToBookingStatus(statusId, statusName, preorderId, supplyId) {
        // 订单生命周期状态判断：
        // 初始状态：preorderId有值，supplyId为null，statusId为-1 → 需要预约
        // 预约成功：preorderId为null，supplyId有值，statusId为1或7 → 预约完成
        
        const hasPreorderId = preorderId && preorderId !== null;
        const hasSupplyId = supplyId && supplyId !== null;
        
        if (hasPreorderId && !hasSupplyId && statusId === -1) {
            return 'not_planned';
        } else if (!hasPreorderId && hasSupplyId && (statusId === 1 || statusId === 7)) {
            return 'scheduled';
        } else {
            switch (statusId) {
                case -1: return 'not_planned';
                case 1:
                case 7: return 'scheduled';
                default: return 'not_planned';
            }
        }
    }

    // API数据转换为前端格式 - 核心数据处理算法
    convertApiDataToOrders(apiData) {
        // 后台异步处理ID转换，不阻塞主流程
        this.processOrderIdTransitions(apiData);

        // 过滤仓库预约相关订单（statusId为-1或1）
        const filteredData = apiData.filter(item => {
            return item.statusId === -1 || item.statusId === 1;
        });

        return filteredData.map(item => {
            // 处理preorderId和supplyId的互斥关系
            const currentPreorderId = item.preorderId || null;
            const currentSupplyId = item.supplyId || null;

            // 获取原始preorderId（从存储恢复）
            let originalPreorderId = currentPreorderId;
            if (!currentPreorderId && currentSupplyId) {
                originalPreorderId = this.getOriginalPreorderIdFromStorage(currentSupplyId);
            }

            // 主要ID用于状态查询（优先supplyId，其次preorderId）
            const primaryId = currentSupplyId || currentPreorderId;

            // WB系统状态映射
            const wbStatus = this.mapWbStatusToBookingStatus(
                item.statusId, item.statusName, currentPreorderId, currentSupplyId
            );

            // 持久化状态优先级处理
            let persistedStatus = null;
            if (currentSupplyId) {
                persistedStatus = this.getOrderBookingStatus(currentSupplyId);
            }
            if (persistedStatus === null && (currentPreorderId || originalPreorderId)) {
                persistedStatus = this.getOrderBookingStatus(currentPreorderId || originalPreorderId);
            }

            // 失败状态重置逻辑
            let finalStatus;
            if (persistedStatus === 'failed') {
                this.clearOrderBookingStatus(currentSupplyId || currentPreorderId || originalPreorderId);
                finalStatus = wbStatus;
            } else {
                finalStatus = persistedStatus !== null ? persistedStatus : wbStatus;
            }

            return {
                // 基础信息 - 使用主要ID作为订单标识
                orderNumber: primaryId,
                supplyId: primaryId,
                deliveryType: item.boxTypeName || "未知配送类型",
                creationDate: item.createDate ? new Date(item.createDate).toLocaleDateString() : "未知",
                warehouse: item.warehouseName || "未知仓库",
                transitWarehouse: item.transitWarehouseName || null,
                itemsQuantity: item.detailsQuantity || 0,

                // 预约状态（优先持久化状态）
                bookingStatus: finalStatus,

                // 计划信息
                planDate: item.supplyDate ? new Date(item.supplyDate).toLocaleDateString() : null,
                acceptanceCoefficient: item.paidAcceptanceCoefficient || null,
                acceptanceCost: item.acceptanceCost || 0,

                // WB系统状态信息
                wbStatusId: item.statusId,
                wbStatusName: item.statusName,

                // 订单ID追踪
                originalPreorderId: originalPreorderId,
                preorderId: currentPreorderId,
                actualSupplyId: currentSupplyId,

                // 订单生命周期状态
                lifecycleStage: currentSupplyId ? 'confirmed' : 'draft',

                // 原始API数据
                _apiData: item
            };
        });
    }

    // 智能订单查找 - 多重查找策略算法
    getOrderByAnyId(id) {
        if (!id) return null;

        const idStr = id.toString();

        // 直接查找
        let order = this.cachedOrders.find(o =>
            (o.orderNumber && o.orderNumber.toString() === idStr) ||
            (o.supplyId && o.supplyId.toString() === idStr) ||
            (o.preorderId && o.preorderId.toString() === idStr)
        );

        // 通过ID映射查找
        if (!order && window.bookingStorage && window.bookingStorage.isAvailable()) {
            // preorderId -> supplyId 映射查找
            const supplyId = window.bookingStorage.getSupplyIdByPreorderId(idStr);
            if (supplyId) {
                order = this.cachedOrders.find(o =>
                    o.supplyId && o.supplyId.toString() === supplyId
                );
            }

            // supplyId -> preorderId 映射查找
            if (!order) {
                const preorderId = window.bookingStorage.getPreorderIdBySupplyId(idStr);
                if (preorderId) {
                    order = this.cachedOrders.find(o =>
                        o.preorderId && o.preorderId.toString() === preorderId
                    );
                }
            }
        }

        return order;
    }

    // 订单ID转换处理 - 生命周期管理
    async handleOrderIdTransition(preorderId, supplyId) {
        if (!preorderId || !supplyId) return;

        console.log(`处理订单ID转换: ${preorderId} -> ${supplyId}`);

        if (window.bookingStorage && window.bookingStorage.isAvailable()) {
            const preorderStatus = await window.bookingStorage.getBookingState(preorderId);
            if (preorderStatus) {
                // 状态转移到新的supplyId
                await window.bookingStorage.setBookingState(supplyId, preorderStatus.status, {
                    ...preorderStatus.data,
                    originalPreorderId: preorderId,
                    transitionTime: new Date().toISOString()
                });

                // 保留preorderId记录为已转换状态
                await window.bookingStorage.setBookingState(preorderId, 'transitioned', {
                    newSupplyId: supplyId,
                    originalPreorderId: preorderId,
                    transitionTime: new Date().toISOString()
                });

                console.log(`ID转换完成: ${preorderId} -> ${supplyId}`);
            }
        }
    }

    // 订单状态验证 - 与WB系统同步检查
    async validateOrderStatuses(newOrders) {
        if (!window.bookingStorage || !window.bookingStorage.isAvailable()) return;

        const activeBookings = window.bookingStorage.getActiveBookings();
        
        for (const booking of activeBookings) {
            const bookingOrderId = booking.orderId;
            
            const wbOrder = newOrders.find(order => 
                order.orderNumber == bookingOrderId || 
                order.supplyId == bookingOrderId ||
                order.preorderId == bookingOrderId
            );
            
            if (wbOrder) {
                const localStatus = booking.status;
                
                // 预约完成检测逻辑
                const isBookingCompleted = (
                    localStatus === 'booking' &&
                    (!wbOrder.preorderId || wbOrder.preorderId === null) &&
                    wbOrder.supplyId && 
                    wbOrder.supplyId.toString().trim() !== '' &&
                    wbOrder.statusId !== -1
                );
                
                if (isBookingCompleted) {
                    console.log(`检测到订单 ${bookingOrderId} 预约已完成`);
                    
                    // 更新状态为scheduled
                    await window.bookingStorage.setBookingState(bookingOrderId, 'scheduled', {
                        detectedAt: new Date().toISOString(),
                        supplyId: wbOrder.supplyId,
                        statusId: wbOrder.statusId,
                        correctedFromWB: true
                    });
                    
                    // 保存ID映射关系
                    if (booking.config && booking.config.originalPreorderId && wbOrder.supplyId) {
                        await window.bookingStorage.saveIdMapping(
                            booking.config.originalPreorderId, 
                            wbOrder.supplyId, {
                                detectedAt: new Date().toISOString(),
                                method: 'status_validation',
                                statusId: wbOrder.statusId
                            }
                        );
                    }
                }
            }
        }
    }

    // 获取完整ID历史
    getOrderIdHistory(id) {
        if (!id || !window.bookingStorage || !window.bookingStorage.isAvailable()) {
            return { currentId: id, mappings: [] };
        }

        const idStr = id.toString();
        const mappings = [];

        // 检查preorderId映射
        const supplyId = window.bookingStorage.getSupplyIdByPreorderId(idStr);
        if (supplyId) {
            mappings.push({
                type: 'preorder_to_supply',
                from: idStr,
                to: supplyId,
                mapping: window.bookingStorage.getIdMapping(idStr)
            });
        }

        // 检查supplyId映射
        const preorderId = window.bookingStorage.getPreorderIdBySupplyId(idStr);
        if (preorderId) {
            mappings.push({
                type: 'supply_from_preorder',
                from: preorderId,
                to: idStr,
                mapping: window.bookingStorage.getIdMapping(preorderId)
            });
        }

        return {
            currentId: idStr,
            mappings: mappings,
            hasMapping: mappings.length > 0
        };
    }
}
```

#### **自动预约系统核心算法**（当前实际实现）
```javascript
// 位置: background.js
// 自动监控和预约核心算法 - 已完整实现版本

// 智能时段筛选算法 - 核心业务逻辑
function filterCostsByUserConditions(costs, monitoring) {
    const { selectedDates, bufferDays, maxCoefficient } = monitoring;
    
    console.log(`🔍 开始筛选仓位，条件:`, {
        选择日期: selectedDates,
        缓冲天数: bufferDays,
        最大系数: maxCoefficient,
        原始数据量: costs.length
    });

    const filteredSlots = costs.filter((cost) => {
        // 1. 排除不可用日期 (coefficient: -1)
        if (cost.coefficient === -1) {
            return false;
        }

        // 2. 应用系数上限筛选
        if (cost.coefficient > maxCoefficient) {
            return false;
        }

        // 3. 检查是否在用户选择的目标日期范围内
        // **重要修复**: 支持缓冲天数范围，而不是单一日期
        const costDate = new Date(cost.date);
        const isInDateRange = selectedDates.some(selectedDate => {
            const baseDate = new Date(selectedDate);
            
            // 检查从选择日期到选择日期+缓冲天数的范围
            for (let i = 0; i <= bufferDays; i++) {
                const targetDate = new Date(baseDate);
                targetDate.setDate(baseDate.getDate() + i);
                
                if (isSameDay(costDate, targetDate)) {
                    console.log(`✅ 日期匹配: ${costDate.toLocaleDateString('zh-CN')} = ${targetDate.toLocaleDateString('zh-CN')}, 系数: ${cost.coefficient}`);
                    return true;
                }
            }
            return false;
        });
        
        return isInDateRange;
    });

    // 按优先级排序（免费优先，然后按系数从低到高）
    const sortedSlots = filteredSlots.sort((a, b) => {
        if (a.coefficient === 0 && b.coefficient !== 0) return -1;
        if (a.coefficient !== 0 && b.coefficient === 0) return 1;
        return a.coefficient - b.coefficient;
    });

    console.log(`🎯 筛选完成，结果: ${sortedSlots.length}个时段`, 
        sortedSlots.slice(0, 3).map(slot => ({
            日期: new Date(slot.date).toLocaleDateString('zh-CN'),
            系数: slot.coefficient
        })));

    return sortedSlots;
}

// 后台监控任务管理
const activeMonitoring = new Map(); // orderId -> monitoring config

async function startBookingMonitoring(orderId, config) {
    if (activeMonitoring.has(orderId)) {
        console.log(`订单 ${orderId} 已在监控中`);
        return { success: false, message: '该订单已在监控中' };
    }

    const monitoring = {
        orderId: orderId,
        selectedDates: config.selectedDates || [],
        bufferDays: config.bufferDays || 0,
        maxCoefficient: config.maxCoefficient || 20,
        startTime: new Date().toISOString(),
        checkCount: 0,
        maxChecks: config.maxChecks || 1000,
        status: 'monitoring'
    };

    activeMonitoring.set(orderId, monitoring);
    
    // 立即执行一次检查
    performMonitoringCheck(orderId);
    
    console.log(`🚀 开始监控订单 ${orderId}:`, monitoring);
    return { success: true, message: '监控已启动' };
}

// 监控检查执行 - 核心自动化逻辑
async function performMonitoringCheck(orderId) {
    const monitoring = activeMonitoring.get(orderId);
    if (!monitoring || monitoring.status !== 'monitoring') return;

    try {
        monitoring.checkCount++;
        console.log(`🔍 [${new Date().toLocaleString()}] 执行监控检查 ${monitoring.checkCount}/${monitoring.maxChecks} - 订单: ${orderId}`);

        // 获取认证数据和当前店铺ID
        const authResult = await getStorage(['Authorizev3', 'wbxValidKey']);
        const currentSupplierId = await getCurrentSupplierIdFromCookies();
        
        if (!authResult.Authorizev3 || !authResult.wbxValidKey) {
            throw new Error('认证信息缺失');
        }

        // 获取订单的仓位费用数据
        const costsData = await getAcceptanceCostsForMonitoring(
            orderId, 
            authResult.Authorizev3, 
            authResult.wbxValidKey, 
            currentSupplierId
        );

        if (!costsData || !costsData.costs || costsData.costs.length === 0) {
            console.log(`❌ 订单 ${orderId} 未获取到有效的费用数据`);
            scheduleNextMonitoringCheck(orderId, getRandomInterval());
            return;
        }

        // 使用用户筛选条件过滤时段
        const matchingSlots = filterCostsByUserConditions(costsData.costs, monitoring);
        
        if (matchingSlots.length > 0) {
            const optimalSlot = matchingSlots[0]; // 最优时段（免费或最低费用）
            
            console.log(`🎯 [${new Date().toLocaleString()}] 找到最优时段 - 订单: ${orderId}`, {
                日期: new Date(optimalSlot.date).toLocaleDateString('zh-CN'),
                系数: optimalSlot.coefficient,
                检查次数: monitoring.checkCount
            });

            // 更新监控状态
            monitoring.status = 'found';
            monitoring.optimalSlot = optimalSlot;

            // 停止监控并立即发起自动预约
            await stopBookingMonitoring(orderId);
            
            const userFilterCriteria = {
                selectedDates: monitoring.selectedDates || [],
                bufferDays: monitoring.bufferDays || 0,
                maxCoefficient: monitoring.maxCoefficient || 20
            };
            
            const bookingResult = await autoBookSupplyDelivery(orderId, optimalSlot.date, userFilterCriteria);

            // 通知前端结果
            const finalStatus = bookingResult.success ? 'scheduled' : 'failed';
            notifyFrontendMonitoringResult(orderId, finalStatus, {
                optimalSlot,
                bookingResult,
                completedAt: new Date().toISOString(),
                newSupplyId: bookingResult.data?.newSupplyId,
                bookedDate: optimalSlot.date,
                coefficient: optimalSlot.coefficient
            });
        } else {
            // 未找到匹配时段，安排下次检查
            console.log(`⏳ [${new Date().toLocaleString()}] 未找到匹配时段 - 订单: ${orderId}, 安排下次检查`);
            scheduleNextMonitoringCheck(orderId, getRandomInterval());
        }

    } catch (error) {
        console.error(`订单 ${orderId} 监控检查失败:`, error);

        // 如果是认证错误，停止监控
        if (error.message.includes('认证') || error.message.includes('401')) {
            await stopBookingMonitoring(orderId);
            notifyFrontendMonitoringResult(orderId, 'failed', { error: error.message });
        }
    }
}

// 获取随机监控间隔（避免被检测）
function getRandomInterval() {
    return Math.floor(Math.random() * (5 * 60 * 1000 - 30 * 1000) + 30 * 1000); // 30秒到5分钟
}

// 安排下次监控检查
function scheduleNextMonitoringCheck(orderId, interval) {
    setTimeout(() => {
        performMonitoringCheck(orderId);
    }, interval);
}
```

### 1.2 数据验证和转换工具

#### **数据验证函数**
```javascript
// 位置: shared/utils.js
// 可直接移植的验证逻辑

// 订单ID验证
function validateOrderId(id) {
    if (!id) return false;
    const idStr = id.toString();
    return /^\d{7,10}$/.test(idStr);
}

// 预约状态验证
function validateBookingStatus(status) {
    const validStates = ['not_planned', 'monitoring', 'booking', 'accepted', 'failed'];
    return validStates.includes(status);
}

// 日期范围验证
function validateDateRange(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();
    
    return start >= now && end > start && (end - start) <= 90 * 24 * 60 * 60 * 1000;
}
```

### 1.3 业务常量定义

#### **应用常量**（当前实际实现）
```javascript
// 从实际代码中提取的业务规则常量

const BOOKING_CONSTANTS = {
    // 预约状态常量（实际使用的状态值）
    STATES: {
        NOT_PLANNED: 'not_planned',
        MONITORING: 'monitoring', 
        BOOKING: 'booking',
        SCHEDULED: 'scheduled',      // 新增：已计划状态
        ACCEPTED: 'accepted',
        FAILED: 'failed',
        TRANSITIONED: 'transitioned' // 新增：ID转换状态
    },
    
    // 实际使用的Chrome存储键名
    STORAGE_KEYS: {
        BOOKING_STATES: 'wb_booking_states',
        BOOKING_CONFIGS: 'wb_booking_configs', 
        ID_MAPPINGS: 'wb_id_mappings',
        AUTH_TOKEN: 'Authorizev3',
        WBX_VALID_KEY: 'wbxValidKey',
        IS_HAS_COOKIES: 'isHasCookies',
        WB_SUPPLIERS_LIST: 'wbSuppliersList'
    },
    
    // 实际监控和预约时间配置
    TIMEOUTS: {
        BOOKING_PROCESS: 60000,           // 预约流程超时
        CACHE_TIMEOUT: 5 * 60 * 1000,     // 缓存超时：5分钟
        API_REQUEST: 30000,               // API请求超时
        MIN_MONITORING_INTERVAL: 30 * 1000,  // 最小监控间隔：30秒
        MAX_MONITORING_INTERVAL: 5 * 60 * 1000  // 最大监控间隔：5分钟
    },
    
    // 实际业务限制参数
    LIMITS: {
        MAX_MONITORING_DAYS: 60,          // 最大监控天数范围
        DEFAULT_MAX_COEFFICIENT: 20,      // 默认最大系数
        MAX_COEFFICIENT_LIMIT: 50,        // 系数上限
        MIN_BUFFER_DAYS: 0,
        MAX_BUFFER_DAYS: 7,
        DEFAULT_MAX_CHECKS: 1000,         // 默认最大检查次数
        DEFAULT_PAGE_SIZE: 20,            // 订单列表分页大小
        MAX_EXPIRED_STATE_AGE: 30 * 24 * 60 * 60 * 1000,  // 过期状态清理：30天
        MAX_EXPIRED_MAPPING_AGE: 90 * 24 * 60 * 60 * 1000  // 过期映射清理：90天
    },

    // WB API状态ID映射（实际WB系统使用的状态值）
    WB_STATUS_MAPPING: {
        DRAFT: -1,           // 草稿状态（未计划）
        SCHEDULED: 1,        // 已预约
        CONFIRMED: 7,        // 已确认
        UNAVAILABLE: -1      // 不可用（系数为-1的时段）
    },

    // WB API端点（实际使用的URL）
    API_ENDPOINTS: {
        SUPPLIERS: 'https://seller.wildberries.ru/ns/suppliers/suppliers-portal-core/suppliers',
        SUPPLY_LIST: 'https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/listSupplies',
        ACCEPTANCE_COSTS: 'https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/getAcceptanceCosts',
        SUPPLY_MANAGER_BASE: 'https://seller.wildberries.ru/supplies-management'
    }
};
```

---

## 2. 数据模型设计

### 2.1 核心数据表结构

#### **用户表 (users)**
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    wb_user_id VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255),
    plan_type ENUM('free', 'pro', 'ultimate') DEFAULT 'free',
    plan_expires_at TIMESTAMP NULL,
    credits_remaining INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_wb_user_id (wb_user_id),
    INDEX idx_plan_expires (plan_expires_at)
);
```

#### **店铺表 (stores)**
```sql
CREATE TABLE stores (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    supplier_id VARCHAR(50) NOT NULL,
    supplier_id_external VARCHAR(50),
    store_name VARCHAR(255) NOT NULL,
    inn VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_supplier (user_id, supplier_id),
    INDEX idx_supplier_id (supplier_id)
);
```

#### **订单表 (orders)**
```sql
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    store_id BIGINT NOT NULL,
    
    -- 订单标识
    order_number VARCHAR(50),
    preorder_id VARCHAR(50),
    supply_id VARCHAR(50),
    
    -- 订单信息
    delivery_type VARCHAR(100),
    warehouse VARCHAR(255),
    items_quantity INT DEFAULT 0,
    creation_date DATE,
    
    -- 状态信息
    order_status ENUM('preorder', 'supply', 'completed') DEFAULT 'preorder',
    booking_status ENUM('not_planned', 'monitoring', 'booking', 'scheduled', 'accepted', 'failed', 'transitioned') DEFAULT 'not_planned',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
    
    INDEX idx_user_store (user_id, store_id),
    INDEX idx_order_number (order_number),
    INDEX idx_preorder_id (preorder_id),
    INDEX idx_supply_id (supply_id),
    INDEX idx_booking_status (booking_status)
);
```

#### **ID映射表 (id_mappings)**
```sql
CREATE TABLE id_mappings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    
    -- 映射关系
    preorder_id VARCHAR(50) NOT NULL,
    supply_id VARCHAR(50) NOT NULL,
    
    -- 预约信息
    booked_date DATE,
    coefficient DECIMAL(5,2),
    booking_method ENUM('manual', 'auto_monitoring') DEFAULT 'auto_monitoring',
    
    -- 元数据
    metadata JSON,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    
    UNIQUE KEY uk_preorder_id (preorder_id),
    INDEX idx_supply_id (supply_id),
    INDEX idx_user_order (user_id, order_id)
);
```

#### **预约状态表 (booking_states)**
```sql
CREATE TABLE booking_states (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    
    -- 状态信息
    status ENUM('not_planned', 'monitoring', 'booking', 'scheduled', 'accepted', 'failed', 'transitioned') NOT NULL,
    previous_status ENUM('not_planned', 'monitoring', 'booking', 'scheduled', 'accepted', 'failed', 'transitioned'),
    
    -- 状态元数据
    metadata JSON,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    
    INDEX idx_user_order (user_id, order_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

#### **预约配置表 (booking_configs)**
```sql
CREATE TABLE booking_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    
    -- 预约配置
    selected_dates JSON NOT NULL, -- ["2023-10-13", "2023-10-14"]
    buffer_days INT DEFAULT 1,
    max_coefficient DECIMAL(5,2) DEFAULT 20.00,
    auto_accept BOOLEAN DEFAULT TRUE,
    
    -- 监控配置
    monitoring_enabled BOOLEAN DEFAULT FALSE,
    check_count INT DEFAULT 0,
    max_checks INT DEFAULT 1000,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    
    UNIQUE KEY uk_user_order (user_id, order_id)
);
```

#### **自动监控配置表 (monitoring_configs)**
```sql
CREATE TABLE monitoring_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    
    -- 监控配置
    selected_dates JSON NOT NULL, -- ["2023-10-13", "2023-10-14"]
    buffer_days INT DEFAULT 1,
    max_coefficient DECIMAL(5,2) DEFAULT 20.00,
    max_checks INT DEFAULT 1000,
    
    -- 监控状态
    is_active BOOLEAN DEFAULT FALSE,
    check_count INT DEFAULT 0,
    start_time TIMESTAMP NULL,
    status ENUM('waiting', 'monitoring', 'found', 'completed', 'failed') DEFAULT 'waiting',
    
    -- 结果信息
    optimal_slot JSON NULL, -- 最优时段信息
    completion_time TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    
    UNIQUE KEY uk_user_order (user_id, order_id),
    INDEX idx_active_monitoring (is_active, status),
    INDEX idx_check_count (check_count, max_checks)
);
```

### 2.2 数据关系图

```
users (1) -----> (N) stores
  |                   |
  |                   |
  +---> (N) orders <--+
           |
           +---> (1) id_mappings
           |
           +---> (N) booking_states
           |
           +---> (1) booking_configs
           |
           +---> (1) monitoring_configs
```

---

## 3. API接口设计参考

### 3.1 RESTful API 设计

#### **ID映射管理 API**
```javascript
// 基于 BookingStorageService.saveIdMapping() 设计

// POST /api/v1/id-mappings
// 保存ID映射关系
{
  "method": "POST",
  "endpoint": "/api/v1/id-mappings",
  "headers": {
    "Authorization": "Bearer {jwt_token}",
    "Content-Type": "application/json"
  },
  "body": {
    "preorderId": "12345678",
    "supplyId": "WB87654321",
    "metadata": {
      "bookedDate": "2023-10-13",
      "coefficient": 0,
      "bookingMethod": "auto_monitoring",
      "orderId": "12345678",
      "orderType": "order"
    }
  },
  "response": {
    "success": true,
    "data": {
      "id": 123,
      "preorderId": "12345678",
      "supplyId": "WB87654321",
      "createdAt": "2023-10-09T10:30:00.000Z"
    }
  }
}

// GET /api/v1/id-mappings/by-preorder/{preorderId}
// 根据预订单号获取供应单号
{
  "method": "GET",
  "endpoint": "/api/v1/id-mappings/by-preorder/12345678",
  "response": {
    "success": true,
    "data": {
      "supplyId": "WB87654321",
      "mapping": {
        "id": 123,
        "bookedDate": "2023-10-13",
        "coefficient": 0
      }
    }
  }
}
```

#### **预约状态管理 API**
```javascript
// 基于 BookingStorageService.setBookingState() 设计

// PUT /api/v1/orders/{orderId}/booking-status
// 更新预约状态
{
  "method": "PUT",
  "endpoint": "/api/v1/orders/12345678/booking-status",
  "body": {
    "status": "monitoring",
    "metadata": {
      "startedAt": "2023-10-09T10:30:00.000Z",
      "checkCount": 1,
      "optimalSlot": {
        "date": "2023-10-13",
        "coefficient": 0
      }
    }
  },
  "response": {
    "success": true,
    "data": {
      "orderId": "12345678",
      "status": "monitoring",
      "previousStatus": "not_planned",
      "updatedAt": "2023-10-09T10:30:00.000Z"
    }
  }
}
```

#### **订单查询 API**
```javascript
// 基于 RealDataService.getOrderByAnyId() 设计

// GET /api/v1/orders/search/{id}
// 智能订单查询（支持任意ID类型）
{
  "method": "GET",
  "endpoint": "/api/v1/orders/search/12345678",
  "response": {
    "success": true,
    "data": {
      "order": {
        "id": 456,
        "orderNumber": "12345678",
        "preorderId": null,
        "supplyId": "WB87654321",
        "deliveryType": "Короб",
        "warehouse": "Коледино",
        "bookingStatus": "accepted"
      },
      "idHistory": {
        "currentId": "12345678",
        "mappings": [
          {
            "type": "preorder_to_supply",
            "from": "12345678",
            "to": "WB87654321",
            "createdAt": "2023-10-09T10:30:00.000Z"
          }
        ]
      }
    }
  }
}
```

### 3.2 WebSocket 实时通信

#### **预约状态实时推送**
```javascript
// 基于现有的状态变化监听机制设计

// WebSocket 连接
const ws = new WebSocket('wss://api.wbassist.com/ws');

// 订阅订单状态变化
ws.send(JSON.stringify({
  action: 'subscribe',
  channel: 'booking_status',
  orderId: '12345678'
}));

// 接收状态变化推送
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.channel === 'booking_status') {
    // 处理状态变化
    handleBookingStatusChange(data.payload);
  }
};

// 推送数据格式
{
  "channel": "booking_status",
  "orderId": "12345678",
  "payload": {
    "status": "accepted",
    "previousStatus": "booking",
    "metadata": {
      "bookedDate": "2023-10-13",
      "coefficient": 0,
      "completedAt": "2023-10-09T10:35:00.000Z"
    },
    "timestamp": "2023-10-09T10:35:00.000Z"
  }
}
```

---

## 4. 数据同步策略

### 4.1 同步架构设计

#### **双向同步模式**
```javascript
// 插件端同步服务
class CloudSyncService {
    constructor() {
        this.syncQueue = [];
        this.lastSyncTime = null;
        this.syncInterval = 30000; // 30秒同步一次
    }
    
    // 增量同步策略
    async performIncrementalSync() {
        try {
            // 1. 获取本地变更
            const localChanges = await this.getLocalChanges();
            
            // 2. 推送到云端
            if (localChanges.length > 0) {
                await this.pushToCloud(localChanges);
            }
            
            // 3. 拉取云端变更
            const cloudChanges = await this.pullFromCloud();
            
            // 4. 应用到本地
            if (cloudChanges.length > 0) {
                await this.applyToLocal(cloudChanges);
            }
            
            this.lastSyncTime = new Date().toISOString();
        } catch (error) {
            console.error('同步失败:', error);
            // 加入重试队列
            this.scheduleRetry();
        }
    }
    
    // 冲突解决策略
    resolveConflict(localData, cloudData) {
        // 时间戳优先策略
        const localTime = new Date(localData.updatedAt);
        const cloudTime = new Date(cloudData.updatedAt);
        
        return cloudTime > localTime ? cloudData : localData;
    }
}
```

#### **同步数据格式**
```javascript
// 同步数据包格式
{
  "syncId": "uuid-v4",
  "timestamp": "2023-10-09T10:30:00.000Z",
  "changes": [
    {
      "type": "id_mapping",
      "action": "create", // create, update, delete
      "data": {
        "preorderId": "12345678",
        "supplyId": "WB87654321",
        "metadata": {...}
      },
      "localTimestamp": "2023-10-09T10:30:00.000Z"
    },
    {
      "type": "booking_state",
      "action": "update",
      "data": {
        "orderKey": "12345678",
        "status": "accepted",
        "metadata": {...}
      },
      "localTimestamp": "2023-10-09T10:31:00.000Z"
    }
  ]
}
```

### 4.2 离线支持策略

#### **离线队列管理**
```javascript
class OfflineQueueManager {
    constructor() {
        this.pendingOperations = [];
        this.maxQueueSize = 1000;
    }
    
    // 添加离线操作
    addOperation(operation) {
        if (this.pendingOperations.length >= this.maxQueueSize) {
            // 移除最旧的操作
            this.pendingOperations.shift();
        }
        
        this.pendingOperations.push({
            ...operation,
            timestamp: new Date().toISOString(),
            retryCount: 0
        });
    }
    
    // 网络恢复时批量同步
    async syncPendingOperations() {
        const operations = [...this.pendingOperations];
        this.pendingOperations = [];
        
        for (const operation of operations) {
            try {
                await this.executeOperation(operation);
            } catch (error) {
                // 重新加入队列，增加重试次数
                if (operation.retryCount < 3) {
                    operation.retryCount++;
                    this.pendingOperations.push(operation);
                }
            }
        }
    }
}
```

---

## 5. 迁移路径

### 5.1 迁移阶段规划

#### **阶段1：数据结构迁移**
```javascript
// 本地数据导出工具
class DataExporter {
    async exportAllData() {
        const exportData = {
            version: "1.0",
            exportTime: new Date().toISOString(),
            data: {
                idMappings: await this.exportIdMappings(),
                bookingStates: await this.exportBookingStates(),
                bookingConfigs: await this.exportBookingConfigs(),
                orders: await this.exportOrders()
            }
        };
        
        return exportData;
    }
    
    async exportIdMappings() {
        if (!window.bookingStorage?.isAvailable()) return [];
        
        return window.bookingStorage.getAllIdMappings().map(mapping => ({
            preorderId: mapping.preorderId,
            supplyId: mapping.supplyId,
            createdAt: mapping.createdAt,
            metadata: mapping.metadata || {}
        }));
    }
}

// 云端数据导入API
// POST /api/v1/migration/import
{
  "method": "POST",
  "endpoint": "/api/v1/migration/import",
  "body": {
    "version": "1.0",
    "data": {
      "idMappings": [...],
      "bookingStates": [...],
      "bookingConfigs": [...],
      "orders": [...]
    }
  }
}
```

#### **阶段2：混合模式运行**
```javascript
// 混合存储适配器
class HybridStorageAdapter {
    constructor() {
        this.localStorage = window.bookingStorage;
        this.cloudStorage = new CloudStorageClient();
        this.syncEnabled = false;
    }
    
    async saveIdMapping(preorderId, supplyId, metadata) {
        // 本地优先保存
        const localResult = await this.localStorage.saveIdMapping(
            preorderId, supplyId, metadata
        );
        
        // 异步同步到云端
        if (this.syncEnabled) {
            this.cloudStorage.saveIdMapping(preorderId, supplyId, metadata)
                .catch(error => {
                    console.warn('云端同步失败:', error);
                    // 加入重试队列
                });
        }
        
        return localResult;
    }
}
```

#### **阶段3：完全云端迁移**
```javascript
// 云端优先适配器
class CloudFirstAdapter {
    constructor() {
        this.cloudStorage = new CloudStorageClient();
        this.localCache = new LocalCacheManager();
    }
    
    async saveIdMapping(preorderId, supplyId, metadata) {
        try {
            // 云端优先保存
            const result = await this.cloudStorage.saveIdMapping(
                preorderId, supplyId, metadata
            );
            
            // 更新本地缓存
            await this.localCache.updateMapping(result);
            
            return result;
        } catch (error) {
            // 降级到本地存储
            console.warn('云端保存失败，降级到本地:', error);
            return await this.localCache.saveMapping(preorderId, supplyId, metadata);
        }
    }
}
```

### 5.2 数据验证和清理

#### **迁移前数据验证**
```javascript
class MigrationValidator {
    async validateData() {
        const issues = [];
        
        // 验证ID映射完整性
        const mappings = window.bookingStorage.getAllIdMappings();
        for (const mapping of mappings) {
            if (!mapping.preorderId || !mapping.supplyId) {
                issues.push(`无效的ID映射: ${JSON.stringify(mapping)}`);
            }
        }
        
        // 验证预约状态一致性
        const states = await this.getAllBookingStates();
        for (const state of states) {
            if (!this.isValidStatus(state.status)) {
                issues.push(`无效的预约状态: ${state.orderKey} - ${state.status}`);
            }
        }
        
        return {
            isValid: issues.length === 0,
            issues: issues
        };
    }
    
    async cleanupData() {
        // 清理过期数据
        await window.bookingStorage.cleanupExpiredMappings();
        await window.bookingStorage.cleanupExpiredStates();
        
        // 修复数据不一致
        await window.realDataService.checkDataConsistency();
    }
}
```

### 5.3 迁移监控和回滚

#### **迁移进度监控**
```javascript
class MigrationMonitor {
    constructor() {
        this.progress = {
            total: 0,
            completed: 0,
            failed: 0,
            errors: []
        };
    }
    
    async monitorMigration(migrationId) {
        const status = await this.cloudStorage.getMigrationStatus(migrationId);
        
        this.progress = {
            ...this.progress,
            ...status
        };
        
        // 发送进度更新
        this.notifyProgress();
        
        return status;
    }
    
    async rollbackIfNeeded() {
        if (this.progress.failed > this.progress.completed * 0.1) {
            console.warn('迁移失败率过高，执行回滚');
            await this.performRollback();
            return true;
        }
        return false;
    }
}
```

---

## 6. 关键技术问题与解决方案

### 6.1 重要Bug修复记录

#### **日期范围筛选Bug修复 (2025-08-24)**
**问题描述**: 自动预约系统不正确处理用户选择的日期范围和缓冲天数，总是选择第一个可用日期而不是用户指定范围内的日期。

**根本原因**: `filterCostsByUserConditions()` 函数中的日期范围逻辑错误：
```javascript
// 错误实现 - 只检查单一日期
const targetDate = new Date(selectedDate);
targetDate.setDate(targetDate.getDate() + bufferDays);
return isSameDay(costDate, targetDate);
```

**正确解决方案**: 
```javascript
// 正确实现 - 检查完整日期范围
const isInDateRange = selectedDates.some(selectedDate => {
    const baseDate = new Date(selectedDate);
    // 检查从选择日期到选择日期+缓冲天数的范围
    for (let i = 0; i <= bufferDays; i++) {
        const targetDate = new Date(baseDate);
        targetDate.setDate(baseDate.getDate() + i);
        if (isSameDay(costDate, targetDate)) {
            return true;
        }
    }
    return false;
});
```

**实际测试场景**:
- 用户选择: 2025-08-27, 缓冲天数: 1
- **错误行为**: 只检查 2025-08-28
- **正确行为**: 检查 2025-08-27 AND 2025-08-28

**位置**: background.js:1559-1617 `filterCostsByUserConditions()` 函数

### 6.2 技术架构特点

#### **订单ID生命周期管理**
项目的核心创新是订单ID的生命周期管理：
1. **草稿阶段**: `preorderId` 有值，`supplyId` 为null
2. **预约过程**: 用户配置自动预约监控
3. **成功转换**: `preorderId` 变null，`supplyId` 有值
4. **ID映射**: 系统维护 `preorderId → supplyId` 映射关系

#### **智能状态同步机制**
- **持久化状态优先**: 用户操作状态覆盖WB API状态
- **失败状态重置**: 预约失败后自动清除，恢复WB自然状态  
- **跨标签页同步**: Chrome storage change listener实现实时同步

#### **自适应监控算法**
- **智能时段筛选**: 支持多日期选择 + 缓冲天数范围
- **优先级排序**: 免费时段优先，然后按费用系数排序
- **随机间隔**: 30秒-5分钟随机监控间隔，避免被检测

### 6.3 云端迁移关键注意事项

#### **数据一致性保证**
1. **双向ID查询**: 必须同时支持 preorderId → supplyId 和 supplyId → preorderId 查询
2. **状态历史保留**: 保存所有状态变更历史，包括转换时间和原因
3. **并发控制**: 多设备同时操作时的状态冲突解决

#### **性能优化策略**
1. **缓存机制**: 5分钟订单数据缓存，减少API调用
2. **异步处理**: ID转换在后台异步处理，不阻塞主流程
3. **批量操作**: 状态验证和更新支持批量处理

#### **安全和稳定性**
1. **认证错误处理**: 401错误自动停止监控，避免无效请求
2. **指数退避**: 失败重试使用指数退避策略  
3. **数据清理**: 自动清理过期状态和映射数据

---

## 总结

本技术文档基于WBAssist项目的**实际生产代码**，提供了向云端数据库迁移的完整技术方案：

1. **核心组件复用**：基于实际实现的业务逻辑类、数据处理算法、自动化系统可直接移植
2. **数据模型设计**：完整支持ID映射、状态历史、监控配置的关系型数据库表结构
3. **API接口设计**：基于现有方法的RESTful API和WebSocket实时通信
4. **数据同步策略**：增量同步、冲突解决、离线支持的完整方案  
5. **迁移路径**：分阶段迁移，确保业务连续性和数据完整性
6. **关键Bug修复**：包含已验证的bug修复和最佳实践

**技术栈兼容性**: 所有核心算法和数据结构都设计为与Node.js后端、React/Vue前端、MySQL/PostgreSQL数据库完全兼容。

这个设计确保了云端数据库能够完全复现现有插件的功能，同时具备更好的扩展性、稳定性和多用户支持能力。
