// 真实数据服务类
// 使用WB API获取真实数据，替代Mock数据系统

class RealDataService {
    constructor() {
        this.currentStoreId = null;
        this.stores = [];
        this.cachedOrders = [];
        this.lastOrdersUpdate = null;
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
        this.currentPage = 1;
        this.pageSize = 20;
    }

    // 初始化数据服务
    async initialize() {
        try {
            // 确保API服务已初始化
            if (!window.wbApiService.isAvailable()) {
                const initialized = await window.wbApiService.initialize();
                if (!initialized) {
                    throw new Error('API服务初始化失败');
                }
            }

            // 获取店铺列表
            await this.loadStores();

            // 设置默认店铺
            if (this.stores.length > 0 && !this.currentStoreId) {
                this.currentStoreId = this.stores[0].id;
            }

            return true;
        } catch (error) {
            console.error('真实数据服务初始化失败:', error);
            return false;
        }
    }

    // 加载店铺列表
    async loadStores() {
        try {
            const suppliers = await window.wbApiService.getSuppliers();
            
            this.stores = suppliers.map(supplier => ({
                id: supplier.id || supplier.supplierId,
                displayName: supplier.name || supplier.displayName,
                inn: supplier.inn,
                subscriptionPlan: 'Pro', // 默认为Pro，实际应从API获取
                remainingCredits: '∞', // 无限制
                supplierId: supplier.id || supplier.supplierId,
                supplierIdExternal: supplier.externalId || supplier.supplierIdExternal,
                isActive: true
            }));

            return this.stores;
        } catch (error) {
            console.error('加载店铺列表失败:', error);
            // 如果API失败，返回空数组
            this.stores = [];
            return [];
        }
    }

    // 获取店铺列表
    getStores() {
        return this.stores;
    }

    // 设置当前店铺
    setCurrentStore(storeId) {
        const store = this.stores.find(s => s.id === storeId);
        if (store) {
            this.currentStoreId = storeId;

            // 清除缓存，强制重新加载订单
            this.clearCache();

            return store;
        }
        return null;
    }

    // 获取当前店铺
    getCurrentStore() {
        return this.stores.find(s => s.id === this.currentStoreId) || this.stores[0];
    }

    // 获取订单列表（真实API调用）
    async getOrders(filters = {}) {
        try {
            // 检查是否强制刷新
            const forceRefresh = filters.forceRefresh === true;
            
            // 检查缓存是否有效（但如果是强制刷新则跳过缓存）
            const now = Date.now();
            if (!forceRefresh &&
                this.cachedOrders.length > 0 &&
                this.lastOrdersUpdate &&
                (now - this.lastOrdersUpdate) < this.cacheTimeout &&
                filters.page === this.currentPage) {
                
                console.log('使用缓存的订单数据');
                return {
                    orders: this.cachedOrders,
                    totalCount: this.cachedOrders.length,
                    page: filters.page || 1,
                    totalPages: Math.ceil(this.cachedOrders.length / this.pageSize)
                };
            }

            // 确保有选中的店铺
            if (!this.currentStoreId) {
                throw new Error('未选择店铺');
            }

            let apiResult;

            // 切换到当前店铺
            await window.wbApiService.switchToSupplier(this.currentStoreId);

            // 调用真实API获取供应计划列表
            apiResult = await window.wbApiService.getSupplyList({
                page: filters.page || 1,
                pageSize: filters.pageSize || this.pageSize,
                statusId: this.mapStatusFilter(filters.status)
            });

            // 转换API数据格式为前端需要的格式
            const orders = this.convertApiDataToOrders(apiResult.data || []);
            
            // 🚀 只在强制刷新时验证订单状态，避免频繁验证导致闪烁
            if (forceRefresh) {
                await this.validateOrderStatuses(orders);
            }
            
            // 更新缓存
            this.cachedOrders = orders;
            this.lastOrdersUpdate = now;
            this.currentPage = filters.page || 1;
            
            console.log(`从WB API获取到 ${orders.length} 个订单，已更新缓存`);



            return {
                orders: orders,
                totalCount: apiResult.totalCount || orders.length,
                page: filters.page || 1,
                totalPages: Math.ceil((apiResult.totalCount || orders.length) / this.pageSize)
            };

        } catch (error) {
            console.error('获取订单失败:', error);
            
            // 如果API调用失败，返回空结果
            return {
                orders: [],
                totalCount: 0,
                currentPage: 1,
                totalPages: 1,
                error: error.message
            };
        }
    }

    // 将状态筛选器映射为API参数
    mapStatusFilter(status) {
        const statusMap = {
            'all': -2,           // 所有状态
            'not_planned': 0,    // 未计划
            'booking': 1,        // 预约中
            'accepted': 2,       // 已接受
            'failed': -1         // 失败
        };
        return statusMap[status] || -2;
    }

    // 将WB API的statusId映射为系统内部状态
    mapWbStatusToBookingStatus(statusId, statusName, preorderId, supplyId) {
        // 根据用户提供的正确状态判断规则：
        // 初始状态：preorderId有值，supplyId为null，statusId为-1 → 需要预约
        // 预约成功：preorderId为null，supplyId有值，statusId为1或7 → 预约完成
        
        // 首先检查订单的ID状态组合
        const hasPreorderId = preorderId && preorderId !== null;
        const hasSupplyId = supplyId && supplyId !== null;
        
        if (hasPreorderId && !hasSupplyId && statusId === -1) {
            // 初始状态：有preorderId，没有supplyId，statusId是-1
            return 'not_planned';
        } else if (!hasPreorderId && hasSupplyId && (statusId === 1 || statusId === 7)) {
            // 预约成功状态：没有preorderId，有supplyId，statusId是1或7
            return 'scheduled';
        } else {
            // 其他情况，根据statusId判断
            switch (statusId) {
                case -1:
                    return 'not_planned';
                case 1:
                case 7:
                    return 'scheduled';
                default:
                    return 'not_planned';
            }
        }
    }

    // 转换API数据为前端订单格式
    convertApiDataToOrders(apiData) {
        // 在后台异步处理ID转换，不阻塞主流程
        this.processOrderIdTransitions(apiData);

        // 过滤只显示与仓库预约相关的供应订单
        // 只保留 statusId 为 -1（未计划）和 1（已预约但未送仓）的订单
        const filteredData = apiData.filter(item => {
            return item.statusId === -1 || item.statusId === 1;
        });

        return filteredData.map(item => {
            // 处理preorderId和supplyId的互斥关系
            const currentPreorderId = item.preorderId || null;
            const currentSupplyId = item.supplyId || null;

            // 获取原始的preorderId（从持久化存储或当前API数据）
            let originalPreorderId = currentPreorderId;

            // 如果当前API数据中preorderId为null但有supplyId，尝试从存储中恢复原始preorderId
            if (!currentPreorderId && currentSupplyId) {
                originalPreorderId = this.getOriginalPreorderIdFromStorage(currentSupplyId);
            }

            // 获取主要ID用于状态查询（优先使用supplyId，其次是preorderId）
            const primaryId = currentSupplyId || currentPreorderId;

            // 从WB API状态映射为系统状态
            const wbStatus = this.mapWbStatusToBookingStatus(item.statusId, item.statusName, currentPreorderId, currentSupplyId);

            // 检查是否有持久化存储中的预约状态（用户主动发起的预约）
            // 需要同时检查preorderId和supplyId，因为订单可能从草稿状态转换为预约状态
            let persistedStatus = null;
            if (currentSupplyId) {
                persistedStatus = this.getOrderBookingStatus(currentSupplyId);
            }
            if (persistedStatus === null && (currentPreorderId || originalPreorderId)) {
                persistedStatus = this.getOrderBookingStatus(currentPreorderId || originalPreorderId);
            }

            // 如果持久化存储中有预约状态，优先使用持久化状态
            // 但如果持久化状态是 'failed'，应该清除它并回到WB API状态
            let finalStatus;
            if (persistedStatus === 'failed') {
                // 预约失败时，清除持久化状态，让订单回到基于WB API的自然状态
                this.clearOrderBookingStatus(currentSupplyId || currentPreorderId || originalPreorderId);
                finalStatus = wbStatus;
            } else {
                finalStatus = persistedStatus !== null ? persistedStatus : wbStatus;
            }

            const convertedOrder = {
                // 基础信息 - 使用主要ID作为订单标识，但保持原始preorderId
                orderNumber: primaryId, // 使用主要ID（supplyId或preorderId）作为统一标识
                supplyId: currentSupplyId, // 使用实际的supplyId
                deliveryType: item.boxTypeName || "未知配送类型",
                creationDate: item.createDate ? new Date(item.createDate).toLocaleDateString() : "未知",
                warehouse: item.warehouseName || "未知仓库",
                transitWarehouse: item.transitWarehouseName || null,
                itemsQuantity: item.detailsQuantity || 0,

                // 预约状态（优先使用持久化状态，其次使用WB API状态）
                bookingStatus: finalStatus,

                // 计划信息
                planDate: item.supplyDate ? new Date(item.supplyDate).toLocaleDateString() : null,
                acceptanceCoefficient: item.paidAcceptanceCoefficient || null,
                acceptanceCost: item.acceptanceCost || 0,

                // WB系统状态信息
                wbStatusId: item.statusId,
                wbStatusName: item.statusName,

                // 保存原始的preorderId和当前的supplyId用于追踪订单生命周期
                originalPreorderId: originalPreorderId, // 始终保存原始的preorderId
                preorderId: currentPreorderId, // 当前API返回的preorderId（可能为null）
                actualSupplyId: currentSupplyId, // 当前API返回的supplyId（可能为null）

                // 订单生命周期状态
                lifecycleStage: currentSupplyId ? 'confirmed' : 'draft', // confirmed: 已确认预约, draft: 草稿状态

                // 保存原始API数据用于丰富通知格式
                _apiData: item
            };

            return convertedOrder;
        });
    }

    // 从存储中获取原始的preorderId（当API中preorderId为null时使用）
    getOriginalPreorderIdFromStorage(supplyId) {
        if (!window.bookingStorage || !window.bookingStorage.isAvailable()) {
            return null;
        }

        try {
            // 检查存储中是否有该supplyId的原始preorderId信息
            const supplyState = window.bookingStorage.getBookingState(supplyId);
            if (supplyState && supplyState.originalPreorderId) {
                return supplyState.originalPreorderId;
            }

            // 检查ID映射表
            const allStates = window.bookingStorage.getAllBookingStates();
            for (const [key, state] of Object.entries(allStates)) {
                if (state.status === 'transitioned' && state.newSupplyId === supplyId) {
                    return key; // key就是原始的preorderId
                }
            }

            return null;
        } catch (error) {
            console.error('获取原始preorderId失败:', error);
            return null;
        }
    }

    // 获取订单的预约状态（从持久化存储中获取）
    getOrderBookingStatus(orderId) {
        if (!window.bookingStorage || !window.bookingStorage.isAvailable()) {
            return null; // 返回null表示没有持久化状态
        }

        const state = window.bookingStorage.getBookingState(orderId);
        return state ? state.status : null; // 返回null表示没有持久化状态
    }

    // 清除订单的预约状态（用于预约失败后重置）
    clearOrderBookingStatus(orderId) {
        if (!window.bookingStorage || !window.bookingStorage.isAvailable()) {
            return;
        }

        console.log(`🧹 清除订单 ${orderId} 的失败预约状态，恢复到WB API状态`);
        window.bookingStorage.removeBookingState(orderId);
    }

    // 获取单个订单
    getOrder(orderNumber, supplyId) {
        const searchId = orderNumber || supplyId;

        return this.cachedOrders.find(order => {
            // 通过orderNumber查找（对应preorderId）
            if (orderNumber && order.orderNumber === orderNumber) {
                return true;
            }

            // 通过supplyId查找（对应主要ID）
            if (supplyId && order.supplyId === supplyId) {
                return true;
            }

            // 兼容性查找：如果传入的ID匹配任何一个字段
            if (searchId && (order.orderNumber === searchId || order.supplyId === searchId)) {
                return true;
            }

            return false;
        });
    }

    // 获取状态文本
    getStatusText(status) {
        const statusTexts = {
            'not_planned': '未计划',
            'booking': '预约中',
            'scheduled': '已预约',
            'accepted': '已接受',
            'failed': '预约失败',
            'cancelled': '已取消'
        };
        return statusTexts[status] || '未知状态';
    }

    // 刷新数据
    async refreshData() {
        try {
            // 清除缓存
            this.cachedOrders = [];
            this.lastOrdersUpdate = null;
            
            // 重新加载店铺和订单
            await this.loadStores();
            
            return true;
        } catch (error) {
            console.error('刷新数据失败:', error);
            return false;
        }
    }

    // 取消预约
    async cancelBooking(orderKey) {

        if (window.bookingStorage && window.bookingStorage.isAvailable()) {
            await window.bookingStorage.setBookingState(orderKey, 'not_planned', {
                cancelledAt: new Date().toISOString(),
                reason: 'user_cancelled'
            });
        }

        // 在缓存中更新状态
        const order = this.cachedOrders.find(o =>
            (o.orderNumber && o.orderNumber.toString() === orderKey) ||
            (o.supplyId && o.supplyId.toString() === orderKey)
        );
        if (order) {
            order.bookingStatus = 'not_planned';
        }

        // 清除缓存以确保下次加载时获取最新状态
        this.clearCache();
    }

    // 扣减预约次数（Pro版本无限制）
    deductCredits(storeId, amount) {
        // Pro版本通常无限制
        // 实际实现应该调用API更新用户配额
    }

    // 更新订单状态
    async updateOrderStatus(orderNumber, supplyId, status, additionalData = {}) {
        const orderId = orderNumber || supplyId;

        // 如果有新的supplyId，需要处理ID转换
        if (additionalData.newSupplyId && additionalData.newSupplyId !== orderId) {
            await this.handleOrderIdTransition(orderId, additionalData.newSupplyId);
        }

        // 保存到持久化存储
        if (window.bookingStorage && window.bookingStorage.isAvailable()) {
            await window.bookingStorage.setBookingState(orderId, status, additionalData);

            // 如果有新的supplyId，也要为新ID保存状态
            if (additionalData.newSupplyId && additionalData.newSupplyId !== orderId) {
                await window.bookingStorage.setBookingState(additionalData.newSupplyId, status, {
                    ...additionalData,
                    originalOrderId: orderId,
                    transitionTime: new Date().toISOString()
                });
            }
        }

        // 在缓存中更新状态
        const order = this.getOrder(orderNumber, supplyId);
        if (order) {
            order.bookingStatus = status;
            Object.assign(order, additionalData);

            // 如果有新的supplyId，更新订单的ID信息，但保留原始preorderId
            if (additionalData.newSupplyId) {
                // 确保保存原始的preorderId
                if (!order.originalPreorderId && (order.preorderId || orderId)) {
                    order.originalPreorderId = order.preorderId || orderId;
                }

                order.actualSupplyId = additionalData.newSupplyId;
                order.supplyId = additionalData.newSupplyId;

                // 更新API数据中的supplyId，但不覆盖原始preorderId信息
                if (order._apiData) {
                    order._apiData.supplyId = additionalData.newSupplyId;
                    // 保存原始preorderId到API数据中
                    if (!order._apiData.originalPreorderId) {
                        order._apiData.originalPreorderId = order.originalPreorderId;
                    }
                }
            }
        }

        // 如果缓存中没有找到订单，更新所有缓存的订单
        if (!order && this.cachedOrders) {
            this.cachedOrders.forEach(cachedOrder => {
                if (cachedOrder.supplyId == orderId || cachedOrder.orderNumber == orderId) {
                    cachedOrder.bookingStatus = status;
                    Object.assign(cachedOrder, additionalData);

                    // 如果有新的supplyId，更新订单的ID信息，但保留原始preorderId
                    if (additionalData.newSupplyId) {
                        // 确保保存原始的preorderId
                        if (!cachedOrder.originalPreorderId && (cachedOrder.preorderId || orderId)) {
                            cachedOrder.originalPreorderId = cachedOrder.preorderId || orderId;
                        }

                        cachedOrder.actualSupplyId = additionalData.newSupplyId;
                        cachedOrder.supplyId = additionalData.newSupplyId;

                        // 更新API数据中的supplyId，但不覆盖原始preorderId信息
                        if (cachedOrder._apiData) {
                            cachedOrder._apiData.supplyId = additionalData.newSupplyId;
                            // 保存原始preorderId到API数据中
                            if (!cachedOrder._apiData.originalPreorderId) {
                                cachedOrder._apiData.originalPreorderId = cachedOrder.originalPreorderId;
                            }
                        }
                    }
                }
            });
        }

        // 清除缓存以确保下次加载时获取最新状态
        this.clearCache();

        return order;
    }

    // 开始预约流程
    async startBooking(orderNumber, supplyId, bookingConfig) {
        const orderId = orderNumber || supplyId;

        try {
            // 保存预约配置到持久化存储
            if (window.bookingStorage && window.bookingStorage.isAvailable()) {
                await window.bookingStorage.setBookingConfig(orderId, bookingConfig);
            }

            // 更新订单状态为预约中
            await this.updateOrderStatus(orderNumber, supplyId, 'booking', {
                startTime: new Date().toISOString(),
                config: bookingConfig
            });

            return {
                success: true,
                message: '预约已启动',
                orderId: orderId
            };
        } catch (error) {
            console.error('启动预约流程失败:', error);
            return {
                success: false,
                message: error.message || '启动预约失败'
            };
        }
    }



    // 后台处理所有订单的ID转换
    async processOrderIdTransitions(apiData) {
        // 异步处理，不阻塞主流程
        setTimeout(async () => {
            for (const item of apiData) {
                if (item.preorderId && item.supplyId) {
                    // 这种情况不应该发生，但如果发生了，记录日志
                    console.warn('订单同时包含preorderId和supplyId:', item);
                } else if (item.supplyId && !item.preorderId) {
                    // 检查是否有对应的preorderId需要转换
                    await this.checkAndHandleIdTransition(item.supplyId);
                }
            }
        }, 0);
    }

    // 检查并处理单个订单的ID转换
    async checkAndHandleIdTransition(supplyId) {
        if (!window.bookingStorage || !window.bookingStorage.isAvailable()) return;

        // 查找可能的preorderId转换
        // 这里可以实现更复杂的逻辑来匹配preorderId和supplyId
        // 目前简化处理
    }

    // 处理订单ID转换（从preorderId到supplyId的转换）
    async handleOrderIdTransition(preorderId, supplyId) {
        if (!preorderId || !supplyId) return;

        console.log(`处理订单ID转换: ${preorderId} -> ${supplyId}`);

        // 如果存储中有preorderId的预约状态，需要转移到supplyId
        if (window.bookingStorage && window.bookingStorage.isAvailable()) {
            const preorderStatus = await window.bookingStorage.getBookingState(preorderId);
            if (preorderStatus) {
                // 将preorderId的状态复制到supplyId，并保存原始preorderId
                await window.bookingStorage.setBookingState(supplyId, preorderStatus.status, {
                    ...preorderStatus.data,
                    originalPreorderId: preorderId,
                    transitionTime: new Date().toISOString()
                });

                // 保留preorderId的记录用于追踪，标记为已转换
                await window.bookingStorage.setBookingState(preorderId, 'transitioned', {
                    newSupplyId: supplyId,
                    originalPreorderId: preorderId,
                    transitionTime: new Date().toISOString()
                });

                console.log(`ID转换完成: ${preorderId} -> ${supplyId}`);
            }
        }
    }

    // 获取订单的所有相关ID（包括历史转换）
    async getOrderAllIds(orderId) {
        const ids = { preorderId: null, supplyId: null, primaryId: orderId };

        if (window.bookingStorage && window.bookingStorage.isAvailable()) {
            // 检查是否是转换后的supplyId
            const supplyStatus = await window.bookingStorage.getBookingState(orderId);
            if (supplyStatus && supplyStatus.data && supplyStatus.data.originalPreorderId) {
                ids.preorderId = supplyStatus.data.originalPreorderId;
                ids.supplyId = orderId;
            } else {
                // 检查是否是转换前的preorderId
                const preorderStatus = await window.bookingStorage.getBookingState(orderId);
                if (preorderStatus && preorderStatus.status === 'transitioned' && preorderStatus.data.newSupplyId) {
                    ids.preorderId = orderId;
                    ids.supplyId = preorderStatus.data.newSupplyId;
                    ids.primaryId = preorderStatus.data.newSupplyId;
                }
            }
        }

        return ids;
    }

    // 清除缓存
    clearCache() {
        this.cachedOrders = [];
        this.lastOrdersUpdate = null;
    }

    // 检查服务是否可用
    isAvailable() {
        return window.wbApiService && window.wbApiService.isAvailable();
    }

    // 获取计划文本
    getPlanText(plan) {
        const planTexts = {
            'Free': '免费版',
            'Pro': '专业版',
            'Enterprise': '企业版'
        };
        return planTexts[plan] || plan;
    }

    // 获取计划图标
    getPlanIcon(plan) {
        const planIcons = {
            'Free': 'fas fa-gift',
            'Pro': 'fas fa-star',
            'Enterprise': 'fas fa-crown'
        };
        return planIcons[plan] || 'fas fa-question';
    }

    // 获取到期状态
    getExpirationStatus(expiresAt) {
        if (!expiresAt) {
            return { text: '永久有效', color: '#28a745' };
        }

        const now = new Date();
        const expireDate = new Date(expiresAt);
        const daysLeft = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));

        if (daysLeft < 0) {
            return { text: '已过期', color: '#dc3545' };
        } else if (daysLeft <= 7) {
            return { text: `${daysLeft}天后到期`, color: '#ffc107' };
        } else {
            return { text: `${daysLeft}天后到期`, color: '#28a745' };
        }
    }

    // ==================== ID映射处理方法 ====================

    // 验证订单状态是否与WB系统一致
    async validateOrderStatuses(newOrders) {
        try {
            console.log('🔍 开始验证订单状态与WB系统一致性');
            
            // 如果没有订单存储服务，跳过验证
            if (!window.bookingStorage || !window.bookingStorage.isAvailable()) {
                console.log('⚠️ 订单存储服务不可用，跳过状态验证');
                return;
            }

            // 获取所有活跃的预约状态（正在预约中或监控中的订单）
            const activeBookings = window.bookingStorage.getActiveBookings();
            
            if (activeBookings.length === 0) {
                console.log('📋 没有活跃的预约需要验证');
                return;
            }
            
            console.log(`🔍 找到 ${activeBookings.length} 个活跃预约需要验证:`, 
                activeBookings.map(b => `${b.orderId}(${b.status})`));
            
            for (const booking of activeBookings) {
                const bookingOrderId = booking.orderId;
                
                // 在新获取的订单中查找对应的订单
                const wbOrder = newOrders.find(order => 
                    order.orderNumber == bookingOrderId || 
                    order.supplyId == bookingOrderId ||
                    order.preorderId == bookingOrderId ||
                    order.originalPreorderId == bookingOrderId ||
                    order.actualSupplyId == bookingOrderId
                );
                
                if (wbOrder) {
                    const localStatus = booking.status;
                    
                    console.log(`🔎 验证订单 ${bookingOrderId}:`, {
                        本地状态: localStatus,
                        WB状态ID: wbOrder.wbStatusId,
                        WB状态名: wbOrder.wbStatusName,
                        preorderId: wbOrder.preorderId,
                        supplyId: wbOrder.actualSupplyId || wbOrder.supplyId,
                        原始preorderId: wbOrder.originalPreorderId
                    });
                    
                    // 预约完成检测逻辑：
                    // 1. 本地状态是booking或monitoring（正在预约/监控中）
                    // 2. WB系统中preorderId变为null且supplyId有值
                    // 3. statusId不再是-1（未计划）
                    const isBookingCompleted = (
                        (localStatus === 'booking' || localStatus === 'monitoring') &&
                        (!wbOrder.preorderId || wbOrder.preorderId === null) &&
                        (wbOrder.actualSupplyId || wbOrder.supplyId) && 
                        (wbOrder.actualSupplyId || wbOrder.supplyId).toString().trim() !== '' &&
                        wbOrder.wbStatusId !== -1
                    );
                    
                    if (isBookingCompleted) {
                        const finalSupplyId = wbOrder.actualSupplyId || wbOrder.supplyId;
                        console.log(`✅ 检测到订单 ${bookingOrderId} 预约已完成：preorderId: ${wbOrder.preorderId} -> supplyId: ${finalSupplyId}, statusId: ${wbOrder.wbStatusId}`);
                        
                        // 更新本地状态为scheduled
                        await window.bookingStorage.setBookingState(bookingOrderId, 'scheduled', {
                            detectedAt: new Date().toISOString(),
                            supplyId: finalSupplyId,
                            statusId: wbOrder.wbStatusId,
                            correctedFromWB: true,
                            previousStatus: localStatus
                        });
                        
                        // 如果有原始preorderId，保存ID映射关系
                        const originalPreorderId = booking.config?.originalPreorderId || 
                                                 wbOrder.originalPreorderId || 
                                                 bookingOrderId;
                        
                        if (originalPreorderId && finalSupplyId && originalPreorderId !== finalSupplyId) {
                            await window.bookingStorage.saveIdMapping(originalPreorderId, finalSupplyId, {
                                detectedAt: new Date().toISOString(),
                                method: 'status_validation',
                                statusId: wbOrder.wbStatusId,
                                bookingOrderId: bookingOrderId
                            });
                            
                            console.log(`💾 保存ID映射: ${originalPreorderId} -> ${finalSupplyId}`);
                        }
                        
                        console.log(`🔄 已修复订单 ${bookingOrderId} 的状态不一致问题: ${localStatus} -> scheduled`);
                    } else {
                        // 详细的调试信息
                        const debugInfo = {
                            订单ID: bookingOrderId,
                            本地状态: localStatus,
                            是否预约中: localStatus === 'booking' || localStatus === 'monitoring',
                            WB_preorderId: wbOrder.preorderId,
                            WB_supplyId: wbOrder.actualSupplyId || wbOrder.supplyId,
                            WB_statusId: wbOrder.wbStatusId,
                            preorderId为空: !wbOrder.preorderId || wbOrder.preorderId === null,
                            supplyId有值: !!(wbOrder.actualSupplyId || wbOrder.supplyId),
                            statusId不是负1: wbOrder.wbStatusId !== -1
                        };
                        
                        if (localStatus === 'booking' || localStatus === 'monitoring') {
                            console.log(`⏳ 订单 ${bookingOrderId} 仍在等待预约完成:`, debugInfo);
                        } else {
                            console.log(`📊 订单 ${bookingOrderId} 状态检查:`, debugInfo);
                        }
                    }
                } else {
                    console.log(`⚠️ 在WB数据中未找到订单 ${bookingOrderId}`);
                }
            }
        } catch (error) {
            console.error('❌ 验证订单状态时出错:', error);
            
            // 如果是存储相关错误，尝试重新初始化存储服务
            if (error.message.includes('storage') || error.message.includes('Chrome')) {
                console.warn('🔄 检测到存储错误，可能需要重新初始化存储服务');
                try {
                    if (window.bookingStorage && typeof window.bookingStorage.initialize === 'function') {
                        await window.bookingStorage.initialize();
                        console.log('✅ 存储服务重新初始化成功');
                    }
                } catch (reinitError) {
                    console.error('💥 重新初始化存储服务失败:', reinitError);
                }
            }
            
            // 验证失败不应该影响正常流程，所以只记录错误
        }
    }

    // 处理ID映射（当preorderId转换为supplyId时调用）
    async handleIdMapping(preorderId, supplyId) {
        try {
            // 在缓存中查找原始订单
            const originalOrder = this.cachedOrders.find(order =>
                order.preorderId && order.preorderId.toString() === preorderId.toString()
            );

            if (originalOrder) {
                // 更新订单信息：preorderId变为null，supplyId有值
                originalOrder.preorderId = null;
                originalOrder.supplyId = supplyId;
                originalOrder.orderStatus = 'supply'; // 状态从预订单变为供应单
                originalOrder.lastUpdated = new Date().toISOString();

                // 清除缓存以确保下次加载时获取最新状态
                this.clearCache();

                console.log(`已处理ID映射: ${preorderId} -> ${supplyId}`);
            }

            return true;
        } catch (error) {
            console.error('处理ID映射失败:', error);
            return false;
        }
    }

    // 根据任意ID（preorderId或supplyId）查找订单
    getOrderByAnyId(id) {
        if (!id) return null;

        const idStr = id.toString();

        // 首先在缓存中直接查找
        let order = this.cachedOrders.find(o =>
            (o.orderNumber && o.orderNumber.toString() === idStr) ||
            (o.supplyId && o.supplyId.toString() === idStr) ||
            (o.preorderId && o.preorderId.toString() === idStr)
        );

        // 如果没找到，尝试通过ID映射查找
        if (!order && window.bookingStorage && window.bookingStorage.isAvailable()) {
            // 检查是否是preorderId，如果是，获取对应的supplyId
            const supplyId = window.bookingStorage.getSupplyIdByPreorderId(idStr);
            if (supplyId) {
                order = this.cachedOrders.find(o =>
                    o.supplyId && o.supplyId.toString() === supplyId
                );
            }

            // 检查是否是supplyId，如果是，获取对应的preorderId
            if (!order) {
                const preorderId = window.bookingStorage.getPreorderIdBySupplyId(idStr);
                if (preorderId) {
                    order = this.cachedOrders.find(o =>
                        o.preorderId && o.preorderId.toString() === preorderId
                    );
                }
            }
        }

        return order;
    }

    // 获取订单的完整ID历史（包括映射关系）
    getOrderIdHistory(id) {
        if (!id || !window.bookingStorage || !window.bookingStorage.isAvailable()) {
            return { currentId: id, mappings: [] };
        }

        const idStr = id.toString();
        const mappings = [];

        // 检查是否是preorderId
        const supplyId = window.bookingStorage.getSupplyIdByPreorderId(idStr);
        if (supplyId) {
            mappings.push({
                type: 'preorder_to_supply',
                from: idStr,
                to: supplyId,
                mapping: window.bookingStorage.getIdMapping(idStr)
            });
        }

        // 检查是否是supplyId
        const preorderId = window.bookingStorage.getPreorderIdBySupplyId(idStr);
        if (preorderId) {
            mappings.push({
                type: 'supply_from_preorder',
                from: preorderId,
                to: idStr,
                mapping: window.bookingStorage.getIdMapping(preorderId)
            });
        }

        return {
            currentId: idStr,
            mappings: mappings,
            hasMapping: mappings.length > 0
        };
    }

    // 清理过期的ID映射
    async cleanupExpiredIdMappings() {
        if (window.bookingStorage && window.bookingStorage.isAvailable()) {
            try {
                const cleanedCount = await window.bookingStorage.cleanupExpiredMappings();
                if (cleanedCount > 0) {
                    console.log(`已清理 ${cleanedCount} 个过期的ID映射`);
                }
                return cleanedCount;
            } catch (error) {
                console.error('清理过期ID映射失败:', error);
                return 0;
            }
        }
        return 0;
    }
}

// 创建全局真实数据服务实例
window.realDataService = new RealDataService();
