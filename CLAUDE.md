# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

WBAssist is a comprehensive Wildberries reservation automation system consisting of:
1. **Web Application** (`/WBAssist-web`) - Next.js 14 web platform with Supabase backend
2. **Chrome Extension** (`/cnakubengbi`) - Browser extension for automated warehouse booking

## Development Commands

### Web Application (WBAssist-web/)
```bash
# Development server (uses pnpm preferred)
pnpm dev
npm run dev

# Production build
pnpm build
npm run build

# Start production server
pnpm start
npm run start

# Linting
pnpm lint
npm run lint
```

### Chrome Extension (cnakubengbi/)
```bash
# Load extension in Chrome Developer Mode:
# 1. Navigate to chrome://extensions/
# 2. Enable "Developer mode" 
# 3. Click "Load unpacked" and select /cnakubengbi directory

# Debug background script:
# Go to chrome://extensions/ → WBAssist → "Inspect views: service worker"

# Debug main UI:
# Open extension popup and use browser DevTools
```

## Architecture

### Web Application Architecture
- **Framework**: Next.js 14 with App Router
- **Database**: Supabase (Project ID: `cdnpddlbecxqnpkfkvid`)
- **Styling**: Tailwind CSS with custom fonts (Inter + Cabinet Grotesk)
- **Language**: Chinese (zh-CN) with TypeScript

**Key Directories**:
- `/app/(auth)` - Authentication flows (signin, signup, reset-password)
- `/app/(default)` - Main landing page with default layout
- `/app/dashboard` - User dashboard and card management
- `/app/api` - API routes (checkout, extension auth, products)
- `/components` - Shared React components including `ExtensionBridge.tsx`
- `/utils/supabase/` - Database client configurations (client, server, middleware)

### Chrome Extension Architecture
- **Manifest V3** with service worker background script
- **Single Page App**: Main UI in `index.html` with modular JS components
- **Service-based**: Each major component implemented as service class

**Core Services**:
- `WBApiService` (`js/api-service.js`) - Wildberries API communication
- `RealDataService` (`js/real-data-service.js`) - Data transformation and caching
- `BookingStorageService` (`js/booking-storage.js`) - Chrome storage persistence
- `WildberriesBookingApp` (`js/main.js`) - Main application controller

### Cross-Component Communication
- **Web ↔ Extension**: `ExtensionBridge.tsx` handles runtime messaging
- **Extension Internal**: Chrome runtime messaging between background and content scripts
- **State Sync**: Event-driven updates (`bookingStatusChanged`, `monitoringStatusChanged`)

## Database Integration

**Supabase Project**:
- Project ID: `cdnpddlbecxqnpkfkvid`
- Region: ap-southeast-1
- Key table: `zpay_transactions` with RLS enabled

**MCP Usage**: Use Supabase MCP tools directly with project ID for database operations.

## Extension Development Details

### Wildberries API Integration
- **Base URLs**: `seller.wildberries.ru`, `seller-supply.wildberries.ru`
- **Authentication**: Bearer tokens (`authorizev3`) + validation cookies (`wbx-validation-key`)
- **Multi-store**: Supplier switching via `x-supplier-id` headers

### Order Lifecycle Management
Complex ID transition handling:
1. **Pre-order**: Has `preorderId`, no `supplyId`
2. **Booking Process**: User initiates booking for `preorderId`
3. **Supply Stage**: Gets `supplyId`, `preorderId` becomes null
4. **ID Mapping**: System maintains `preorderId → supplyId` mapping in storage

### Storage Keys
- `wb_booking_states`: Order booking status and metadata
- `wb_booking_configs`: User booking preferences per order
- `wb_id_mappings`: preorderId to supplyId mappings

## Important Implementation Notes

### Date Range Bug Prevention
When implementing date filtering for booking automation, always iterate through the full date range:
```javascript
// CORRECT - Check range of dates
for (let i = 0; i <= bufferDays; i++) {
    const targetDate = new Date(baseDate);
    targetDate.setDate(baseDate.getDate() + i);
    if (matchesDate(costDate, targetDate)) return true;
}

// WRONG - Only checks single calculated date
const targetDate = new Date(selectedDate);
targetDate.setDate(targetDate.getDate() + bufferDays);
return matchesDate(costDate, targetDate);
```

### Security Requirements
- Never log sensitive cookies or tokens
- Use secure origins for all Wildberries API communications
- Validate user inputs before API calls
- Implement proper CORS handling

### Background Operations
- Use `active: false` for automated tab operations
- Implement cleanup (close tabs, clear intervals) after completion
- Handle race conditions in concurrent booking operations
- Use random intervals (30s-5min) for API polling to avoid detection

## TypeScript Configuration
- Uses `@/*` path aliases pointing to project root
- Non-strict mode enabled (`"strict": false`)
- JSX preserve mode for Next.js processing