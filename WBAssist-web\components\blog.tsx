// 用户成功案例数据
const successCases = [
  {
    id: 1,
    name: "张先生",
    business: "服装类目卖家",
    avatar: "bg-gradient-to-br from-blue-400 to-blue-600",
    initial: "张",
    result: "预约成功率提升至98%",
    description: "使用WBAssist后，我的预约成功率从原来的60%提升到98%，每月节省大量时间用于其他业务发展。",
    stats: {
      improvement: "+38%",
      timeSaved: "每月节省40小时",
      revenue: "月收入增长25%"
    }
  },
  {
    id: 2,
    name: "李女士",
    business: "家居用品卖家",
    avatar: "bg-gradient-to-br from-green-400 to-green-600",
    initial: "李",
    result: "24/7自动监控，从不错过",
    description: "WBAssist的智能监控让我再也不用担心错过预约时机，系统会自动帮我抢到最佳仓位。",
    stats: {
      improvement: "100%",
      timeSaved: "全天候监控",
      revenue: "仓储成本降低30%"
    }
  },
  {
    id: 3,
    name: "王总",
    business: "多品类大卖家",
    avatar: "bg-gradient-to-br from-purple-400 to-purple-600",
    initial: "王",
    result: "管理50+店铺，效率翻倍",
    description: "作为拥有50多个店铺的大卖家，WBAssist帮我实现了批量管理，大大提升了运营效率。",
    stats: {
      improvement: "+200%",
      timeSaved: "团队效率翻倍",
      revenue: "整体利润增长40%"
    }
  }
];

export default function Blog() {
  return (
    <section className="bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="py-12 md:py-20">
          {/* Section header */}
          <div className="pb-12 md:pb-16">
            <h2 className="h2 font-cabinet-grotesk text-center md:text-left">
              用户成功案例
            </h2>
            <p className="text-xl text-gray-600 mt-4 text-center md:text-left">
              看看其他卖家如何通过WBAssist实现业务增长
            </p>
          </div>

          {/* Success Cases */}
          <div className="max-w-sm mx-auto md:max-w-none grid gap-8 md:grid-cols-3 md:gap-6">
            {successCases.map((caseItem, index) => (
              <article
                key={caseItem.id}
                className="bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 p-8 h-full flex flex-col"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                {/* User Avatar and Info */}
                <div className="flex items-center mb-6">
                  <div className={`w-12 h-12 ${caseItem.avatar} rounded-full flex items-center justify-center text-white font-bold text-lg mr-4`}>
                    {caseItem.initial}
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900">{caseItem.name}</h3>
                    <p className="text-sm text-gray-500">{caseItem.business}</p>
                  </div>
                </div>

                {/* Result Highlight */}
                <div className="bg-blue-50 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold text-blue-900 mb-2">核心成果</h4>
                  <p className="text-blue-700 font-medium">{caseItem.result}</p>
                </div>

                {/* Description */}
                <p className="text-gray-600 leading-relaxed mb-6 flex-grow">
                  "{caseItem.description}"
                </p>

                {/* Stats */}
                <div className="grid grid-cols-1 gap-3 pt-4 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">成功率提升</span>
                    <span className="font-bold text-green-600">{caseItem.stats.improvement}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">时间节省</span>
                    <span className="font-bold text-blue-600">{caseItem.stats.timeSaved}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">收益提升</span>
                    <span className="font-bold text-purple-600">{caseItem.stats.revenue}</span>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
