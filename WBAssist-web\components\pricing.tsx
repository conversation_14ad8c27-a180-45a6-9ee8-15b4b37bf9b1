"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";

// 产品特性接口
interface ProductFeature {
  id: string;
  text: string;
}

// 产品类型定义
interface Product {
  id: string;
  name: string;
  title: string;
  description: string;
  price: string;
  priceLabel: string;
  isSubscription: boolean;
  subscriptionPeriod?: string;
  features: ProductFeature[];
}

export default function Pricing() {
  const [annual, setAnnual] = useState<boolean>(false); // 默认选择月卡
  const [loading, setLoading] = useState<boolean>(false);
  const [products, setProducts] = useState<Record<string, Product>>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClient();

  // 检查用户登录状态
  useEffect(() => {
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUserId(session?.user?.id || null);
    };
    
    checkUser();
  }, [supabase]);

  // 获取产品信息
  useEffect(() => {
    const fetchProducts = async () => {
      console.log("获取产品信息");
      try {
        const response = await fetch("/api/products");
        const data = await response.json();
        setProducts(data.products);
      } catch (error) {
        console.error("获取产品信息失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // 处理免费版体验
  const handleFreeExperience = () => {
    if (!userId) {
      // 未登录，跳转到注册页面
      router.push('/signup');
    } else {
      // 已登录，跳转到控制台
      router.push('/dashboard');
    }
  };

  // 处理支付请求
  const handlePayment = async (productId: string) => {
    // 更严格检查用户是否登录
    if (!userId) {
      console.log("用户未登录，跳转到登录页面");
      // 保存当前页面URL，以便登录后返回
      router.push('/signin?redirect=/');
      return;
    }

    // 再次验证用户登录状态
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      console.log("会话已过期，需要重新登录");
      router.push('/signin?redirect=/');
      return;
    }

    // 设置加载状态
    setLoading(true);

    try {
      // 获取产品信息
      const product = products[productId];
      if (!product) {
        throw new Error("产品不存在");
      }

      // 构建支付请求数据
      const paymentData: {
        productId: string;
        productName: string;
        amount: string;
        isSubscription: boolean;
        paymentMethod: string;
        subscriptionPeriod?: "monthly" | "yearly";
      } = {
        productId: product.id,
        productName: product.name,
        amount: product.price,
        isSubscription: product.isSubscription,
        paymentMethod: "alipay", // 默认使用支付宝
      };

      // 如果是订阅产品，添加订阅周期
      if (product.isSubscription && product.subscriptionPeriod) {
        paymentData.subscriptionPeriod = product.subscriptionPeriod === "yearly" ? "yearly" : "monthly";
      }

      // 发送请求获取支付链接
      const response = await fetch("/api/checkout/providers/zpay/url", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(paymentData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "获取支付链接失败");
      }

      // 获取支付链接
      const { url } = await response.json();

      // 跳转到支付页面
      window.location.href = url;
    } catch (error) {
      console.error("支付处理失败:", error);
      alert(`支付处理失败: ${error instanceof Error ? error.message : "未知错误"}`);
    } finally {
      setLoading(false);
    }
  };

  // 显示加载状态
  if (isLoading) {
    return (
      <section className="relative border-t border-gray-100">
        <div className="max-w-6xl mx-auto px-4 sm:px-6">
          <div className="py-12 md:py-20 text-center">
            <p>加载产品信息中...</p>
          </div>
        </div>
      </section>
    );
  }

  // 获取免费版产品
  const freeProduct = products["basic-onetime"];

  // 获取当前选择的专业版产品（年付或月付）
  const proProduct = annual ? products["pro-yearly"] : products["pro-monthly"];

  // 获取当前选择的至尊版产品（年付或月付）
  const ultimateProduct = annual ? products["ultimate-yearly"] : products["ultimate-monthly"];

  return (
    <section id="pricing" className="relative border-t border-gray-100">
      {/* Bg gradient */}
      <div
        className="absolute top-0 left-0 right-0 bg-gradient-to-b from-gray-50 to-white h-1/2 pointer-events-none -z-10"
        aria-hidden="true"
      />
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="py-12 md:py-20">
          {/* Section header */}
          <div className="max-w-3xl mx-auto text-center pb-12 md:pb-16">
            <h2 className="h2 font-cabinet-grotesk">
              选择适合您的WBAssist计划
            </h2>
            <p className="text-xl text-gray-500 mt-4">
              从免费版开始体验，升级到专业版解锁更多强大功能
            </p>
          </div>
          {/* Pricing tables */}
          <div>
            {/* Pricing toggle */}
            <div className="flex justify-center max-w-[20rem] m-auto mb-8 lg:mb-16">
              <div className="relative flex w-full mx-6 p-1 bg-gray-200 rounded-full">
                <span
                  className="absolute inset-0 m-1 pointer-events-none"
                  aria-hidden="true"
                >
                  <span
                    className={`absolute inset-0 w-1/2 bg-white rounded-full shadow transform transition duration-150 ease-in-out ${
                      annual ? "translate-x-full" : "translate-x-0"
                    }`}
                  />
                </span>
                <button
                  className={`relative flex-1 text-sm font-medium p-1 transition duration-150 ease-in-out ${
                    !annual ? "text-gray-900" : "text-gray-500"
                  }`}
                  onClick={() => setAnnual(false)}
                >
                  月卡
                </button>
                <button
                  className={`relative flex-1 text-sm font-medium p-1 transition duration-150 ease-in-out ${
                    annual ? "text-gray-900" : "text-gray-500"
                  } relative`}
                  onClick={() => setAnnual(true)}
                >
                  年卡
                  <span className="absolute -top-2 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                    -20%
                  </span>
                </button>
              </div>
            </div>
            <div className="max-w-sm mx-auto grid gap-8 lg:grid-cols-3 lg:gap-6 items-stretch lg:max-w-6xl pt-4">
              {/* Pricing table 1 - 免费版 */}
              {freeProduct && (
                <div
                  className="relative flex flex-col h-full p-6 bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-300 rounded-lg shadow-lg"
                  data-aos="fade-right"
                >
                  <div className="mb-6">
                    <div className="font-cabinet-grotesk text-xl font-semibold mb-1">
                      {freeProduct.title}
                    </div>
                    <div className="font-cabinet-grotesk inline-flex items-baseline mb-2">
                      <span className="text-3xl font-medium">¥</span>
                      <span className="text-5xl font-bold">
                        {freeProduct.price}
                      </span>
                      <span className="font-medium">
                        {freeProduct.priceLabel}
                      </span>
                    </div>
                    <div className="text-gray-500 mb-6">
                      {freeProduct.description}
                    </div>
                    <button
                      className="btn text-white bg-emerald-500 hover:bg-emerald-600 w-full shadow-sm"
                      onClick={handleFreeExperience}
                      disabled={loading}
                    >
                      {loading ? "处理中..." : "立即体验"}
                    </button>
                  </div>
                  <div className="font-medium mb-4">功能特性：</div>
                  <ul className="text-gray-500 space-y-3 grow">
                    {freeProduct.features.map((feature) => (
                      <li key={feature.id} className="flex items-center">
                        <svg
                          className="w-3 h-3 fill-current text-emerald-500 mr-3 shrink-0"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M10.28 2.28L3.989 8.575 1.695 6.28A1 1 0 00.28 7.695l3 3a1 1 0 001.414 0l7-7A1 1 0 0010.28 2.28z" />
                        </svg>
                        <span>{feature.text}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Pricing table 2 - 专业版 */}
              {proProduct && (
                <div
                  className="relative flex flex-col h-full p-6 bg-gray-800 rounded-lg shadow-lg"
                  data-aos="fade-left"
                >
                  <div className="absolute top-0 right-0 mr-6 -mt-4">
                    <div className="inline-flex items-center text-sm font-semibold py-1 px-4 text-emerald-600 bg-emerald-200 rounded-full">
                      最受欢迎
                    </div>
                  </div>
                  <div className="mb-6">
                    <div className="font-cabinet-grotesk text-xl text-gray-100 font-semibold mb-1">
                      {proProduct.title}
                    </div>
                    <div className="font-cabinet-grotesk text-gray-100 inline-flex items-baseline mb-2">
                      <span className="text-3xl font-medium text-gray-400">
                        ¥
                      </span>
                      <span className="text-5xl font-bold">
                        {proProduct.price}
                      </span>
                      <span className="font-medium text-gray-400">
                        {proProduct.priceLabel}
                      </span>
                    </div>
                    <div className="text-gray-400 mb-6">
                      {proProduct.description}
                    </div>
                    <button
                      className="btn text-white bg-blue-600 hover:bg-blue-700 w-full shadow-sm"
                      onClick={() =>
                        handlePayment(annual ? "pro-yearly" : "pro-monthly")
                      }
                      disabled={loading}
                    >
                      {loading ? "处理中..." : "订阅"}
                    </button>
                  </div>
                  <div className="font-medium text-gray-100 mb-4">
                    功能特性：
                  </div>
                  <ul className="text-gray-400 space-y-3 grow">
                    {proProduct.features.map((feature) => (
                      <li key={feature.id} className="flex items-center">
                        <svg
                          className="w-3 h-3 fill-current text-emerald-500 mr-3 shrink-0"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M10.28 2.28L3.989 8.575 1.695 6.28A1 1 0 00.28 7.695l3 3a1 1 0 001.414 0l7-7A1 1 0 0010.28 2.28z" />
                        </svg>
                        <span>{feature.text}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Pricing table 3 - 至尊版 */}
              {ultimateProduct && (
                <div
                  className="relative flex flex-col h-full p-6 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg shadow-lg"
                  data-aos="fade-up"
                >
                  <div className="absolute top-0 right-0 mr-6 -mt-4">
                    <div className="inline-flex items-center text-sm font-semibold py-1 px-4 text-yellow-600 bg-yellow-200 rounded-full">
                      企业首选
                    </div>
                  </div>
                  <div className="mb-6">
                    <div className="font-cabinet-grotesk text-xl text-white font-semibold mb-1">
                      {ultimateProduct.title}
                    </div>
                    <div className="font-cabinet-grotesk text-white inline-flex items-baseline mb-2">
                      <span className="text-3xl font-medium text-gray-200">
                        ¥
                      </span>
                      <span className="text-5xl font-bold">
                        {ultimateProduct.price}
                      </span>
                      <span className="font-medium text-gray-200">
                        {ultimateProduct.priceLabel}
                      </span>
                    </div>
                    <div className="text-gray-200 mb-6">
                      {ultimateProduct.description}
                    </div>
                    <button
                      className="btn text-white bg-yellow-500 hover:bg-yellow-600 w-full shadow-sm"
                      onClick={() =>
                        handlePayment(annual ? "ultimate-yearly" : "ultimate-monthly")
                      }
                      disabled={loading}
                    >
                      {loading ? "处理中..." : "订阅"}
                    </button>
                  </div>
                  <div className="font-medium text-white mb-4">
                    功能特性：
                  </div>
                  <ul className="text-gray-200 space-y-3 grow">
                    {ultimateProduct.features.map((feature) => (
                      <li key={feature.id} className="flex items-center">
                        <svg
                          className="w-3 h-3 fill-current text-yellow-400 mr-3 shrink-0"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M10.28 2.28L3.989 8.575 1.695 6.28A1 1 0 00.28 7.695l3 3a1 1 0 001.414 0l7-7A1 1 0 0010.28 2.28z" />
                        </svg>
                        <span>{feature.text}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
