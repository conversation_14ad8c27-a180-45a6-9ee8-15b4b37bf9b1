# preorderId→supplyId转换逻辑问题分析与修复方案

## 🚨 当前发现的关键问题

### 问题1: 预约后查找订单的逻辑错误

#### 现有问题代码：
```javascript
// background.js 第479-481行
const postBookingOrder = postBookingOrders.find(order => 
    order.preorderId.toString() === preorderID.toString()  // ❌ 错误逻辑！
);
```

#### 问题分析：
根据WB API的实际行为：
- **预约前**: `{ preorderId: 44109242, supplyId: null, statusId: -1 }`
- **预约后**: `{ preorderId: null, supplyId: 32320787, statusId: 1 }`

**问题**：预约成功后，`preorderId`变为`null`，用原来的`preorderId`查找会找不到订单！

#### 修复方案：
```javascript
// 智能查找策略：同时考虑preorderId和转换后的supplyId
const postBookingOrder = postBookingOrders.find(order => {
    // 方法1: 通过preorderId查找（适用于状态还未完全转换的情况）
    if (order.preorderId && order.preorderId.toString() === preorderID.toString()) {
        return true;
    }
    
    // 方法2: 通过时间窗口和仓库匹配查找转换后的订单
    if (order.supplyId && !order.preorderId) {
        // 检查创建时间是否在预约时间窗口内（5分钟内）
        const orderCreateTime = new Date(order.createDate || order.changeDate);
        const timeDiff = Math.abs(orderCreateTime - bookingStartTime);
        
        // 检查仓库是否匹配（同一个仓库）
        const warehouseMatch = order.warehouseId === preBookingOrder?.warehouseId;
        
        return timeDiff < 5 * 60 * 1000 && warehouseMatch; // 5分钟内且同仓库
    }
    
    return false;
});

// 方法3: 从映射存储中查找
if (!postBookingOrder && window.bookingStorage) {
    const mappedSupplyId = window.bookingStorage.getSupplyIdByPreorderId(preorderID);
    if (mappedSupplyId) {
        postBookingOrder = postBookingOrders.find(order => 
            order.supplyId && order.supplyId.toString() === mappedSupplyId.toString()
        );
    }
}
```

---

### 问题2: getAllOrdersFromWBNewAPI函数未定义

#### 现有问题：
```javascript
// 在background.js中多处调用但找不到函数定义
const preBookingOrders = await getAllOrdersFromWBNewAPI();  // ❌ 函数不存在
```

#### 修复方案：
```javascript
// 替换为正确的API调用
async function getAllOrdersFromWBNewAPI() {
    try {
        // 使用现有的real-data-service
        if (!window.realDataService) {
            throw new Error('RealDataService未初始化');
        }
        
        const result = await window.realDataService.getOrders({
            forceRefresh: true,    // 强制刷新，获取最新状态
            pageSize: 100,         // 获取更多数据
            status: 'all'          // 获取所有状态的订单
        });
        
        return result.orders || [];
    } catch (error) {
        console.error('获取订单数据失败:', error);
        return [];
    }
}

// 或者直接调用API服务
async function getAllOrdersFromWBAPI() {
    try {
        if (!window.wbApiService || !window.wbApiService.isAvailable()) {
            throw new Error('WB API Service未初始化');
        }
        
        const apiResult = await window.wbApiService.getSupplyList({
            pageSize: 100,
            statusId: -2  // 获取所有状态
        });
        
        return apiResult.data || [];
    } catch (error) {
        console.error('调用WB API失败:', error);
        return [];
    }
}
```

---

### 问题3: 状态转换检测不够智能

#### 现有问题：
只检查简单的ID和状态变化，没有考虑边界情况。

#### 改进方案：
```javascript
function detectOrderConversion(preBookingOrder, postBookingOrders, preorderID) {
    // 策略1: 直接匹配（如果状态还未完全转换）
    let matchedOrder = postBookingOrders.find(order => 
        order.preorderId && order.preorderId.toString() === preorderID.toString()
    );
    
    if (matchedOrder) {
        return {
            found: true,
            order: matchedOrder,
            conversionStatus: matchedOrder.supplyId ? 'converted' : 'same',
            matchMethod: 'direct_preorder_id'
        };
    }
    
    // 策略2: 通过特征匹配查找转换后的订单
    const bookingTime = new Date();
    const candidateOrders = postBookingOrders.filter(order => {
        // 必须有supplyId且没有preorderId（转换后的特征）
        if (!order.supplyId || order.preorderId) return false;
        
        // 仓库必须匹配
        if (order.warehouseId !== preBookingOrder?.warehouseId) return false;
        
        // 商品数量必须匹配
        if (order.detailsQuantity !== preBookingOrder?.detailsQuantity) return false;
        
        // 时间窗口检查（预约后5分钟内）
        const orderTime = new Date(order.changeDate || order.createDate);
        const timeDiff = Math.abs(orderTime - bookingTime);
        
        return timeDiff < 5 * 60 * 1000; // 5分钟内
    });
    
    if (candidateOrders.length === 1) {
        // 找到唯一匹配的转换后订单
        return {
            found: true,
            order: candidateOrders[0],
            conversionStatus: 'converted',
            matchMethod: 'feature_matching',
            confidence: 'high'
        };
    } else if (candidateOrders.length > 1) {
        // 多个候选，选择最新的
        const latestOrder = candidateOrders.sort((a, b) => 
            new Date(b.changeDate) - new Date(a.changeDate)
        )[0];
        
        return {
            found: true,
            order: latestOrder,
            conversionStatus: 'converted',
            matchMethod: 'feature_matching_latest',
            confidence: 'medium',
            warning: `找到${candidateOrders.length}个候选订单，选择最新的`
        };
    }
    
    // 策略3: 从映射存储中查找
    if (window.bookingStorage) {
        const mappedSupplyId = window.bookingStorage.getSupplyIdByPreorderId(preorderID);
        if (mappedSupplyId) {
            const mappedOrder = postBookingOrders.find(order => 
                order.supplyId && order.supplyId.toString() === mappedSupplyId.toString()
            );
            
            if (mappedOrder) {
                return {
                    found: true,
                    order: mappedOrder,
                    conversionStatus: 'converted',
                    matchMethod: 'storage_mapping'
                };
            }
        }
    }
    
    return {
        found: false,
        conversionStatus: 'unknown',
        error: '无法在预约后的订单列表中找到对应订单'
    };
}
```

---

### 问题4: 时序问题处理不完善

#### 现有问题：
```javascript
// 只等待3秒，可能不够
await new Promise(resolve => setTimeout(resolve, 3000));
```

#### 改进方案：
```javascript
async function waitForOrderConversion(preorderID, maxWaitTime = 30000, checkInterval = 2000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
        try {
            const orders = await getAllOrdersFromWBNewAPI();
            const result = detectOrderConversion(preBookingOrder, orders, preorderID);
            
            if (result.found) {
                console.log(`✅ 在${Date.now() - startTime}ms后检测到订单转换`);
                return result;
            }
            
            console.log(`⏳ 第${Math.floor((Date.now() - startTime) / checkInterval) + 1}次检查，未发现转换，继续等待...`);
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            
        } catch (error) {
            console.warn('检查订单转换时出错:', error);
            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
    }
    
    return {
        found: false,
        timeout: true,
        error: `等待${maxWaitTime}ms后仍未检测到订单转换`
    };
}
```

---

### 问题5: 映射关系维护不完整

#### 改进方案：
```javascript
// 在检测到转换后立即保存映射关系
async function saveOrderConversionMapping(preorderID, supplyId, orderData) {
    if (!window.bookingStorage) return;
    
    try {
        await window.bookingStorage.saveIdMapping(preorderID, supplyId, {
            conversionTime: new Date().toISOString(),
            warehouseId: orderData.warehouseId,
            warehouseName: orderData.warehouseName,
            detailsQuantity: orderData.detailsQuantity,
            statusChange: {
                from: { statusId: -1, statusName: "Не запланировано" },
                to: { statusId: orderData.statusId, statusName: orderData.statusName }
            },
            supplyDate: orderData.supplyDate,
            conversionMethod: 'auto_booking_detection'
        });
        
        console.log(`✅ 已保存ID映射: ${preorderID} → ${supplyId}`);
    } catch (error) {
        console.error('保存ID映射失败:', error);
    }
}
```

---

## 🔧 完整修复方案

### 1. 修复background.js中的查找逻辑
```javascript
// 替换第477-481行
const postBookingOrders = await getAllOrdersFromWBNewAPI();
const conversionResult = detectOrderConversion(preBookingOrder, postBookingOrders, preorderID);

if (conversionResult.found) {
    const postBookingOrder = conversionResult.order;
    
    // 如果发生了转换，保存映射关系
    if (conversionResult.conversionStatus === 'converted' && postBookingOrder.supplyId) {
        await saveOrderConversionMapping(preorderID, postBookingOrder.supplyId, postBookingOrder);
    }
    
    const currentStatus = {
        wbStatusId: postBookingOrder.wbStatusId || postBookingOrder.statusId,
        supplyId: postBookingOrder.actualSupplyId || postBookingOrder.supplyId,
        statusName: postBookingOrder.wbStatusName || postBookingOrder.statusName,
        conversionInfo: conversionResult
    };
    
    // ... 继续原有逻辑
}
```

### 2. 添加getAllOrdersFromWBNewAPI函数定义
在background.js开头添加：
```javascript
// 全局函数定义 - 获取WB订单数据
async function getAllOrdersFromWBNewAPI() {
    try {
        if (typeof window !== 'undefined' && window.realDataService) {
            // 在插件环境中使用realDataService
            const result = await window.realDataService.getOrders({
                forceRefresh: true,
                pageSize: 100,
                status: 'all'
            });
            return result.orders || [];
        } else {
            // 在background环境中直接调用API
            // 这里需要实现background环境的API调用逻辑
            throw new Error('Background环境暂不支持直接调用getAllOrdersFromWBNewAPI');
        }
    } catch (error) {
        console.error('getAllOrdersFromWBNewAPI调用失败:', error);
        return [];
    }
}
```

### 3. 增强错误处理和日志
```javascript
// 添加详细的转换检测日志
function logConversionDetection(preorderID, result) {
    console.log(`🔍 订单转换检测结果 [${preorderID}]:`, {
        found: result.found,
        conversionStatus: result.conversionStatus,
        matchMethod: result.matchMethod,
        confidence: result.confidence,
        warning: result.warning,
        newSupplyId: result.order?.supplyId,
        statusChange: result.order ? {
            statusId: result.order.statusId,
            statusName: result.order.statusName
        } : null
    });
}
```

---

## 🎯 修复优先级

1. **🔥 紧急**: 修复getAllOrdersFromWBNewAPI未定义问题
2. **🔥 紧急**: 修复预约后查找订单的逻辑错误  
3. **⚡ 重要**: 实现智能转换检测算法
4. **⚡ 重要**: 完善映射关系维护
5. **📈 优化**: 改进时序等待和错误处理

这些修复将显著提高preorderId→supplyId转换检测的准确性和可靠性！ 