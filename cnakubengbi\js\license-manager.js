// 卡密管理器 - 处理卡密绑定和权限管理
class LicenseManager {
    constructor() {
        this.initialized = false;
    }

    // 初始化
    async initialize() {
        if (this.initialized) return;

        // 消息监听器已在WBAssistApiService构造函数中自动设置
        console.log('📋 License Manager initialized');
        
        this.initialized = true;
    }

    // 打开卡密绑定模态框
    openLicenseBindModal() {
        console.log('🔑 license-manager: 开始打开模态框');
        
        this.populateStoreSelectModal();
        const modal = document.getElementById('licenseBindModal');
        
        console.log('🔍 模态框元素检查:', {
            modalExists: !!modal,
            modalHTML: modal?.outerHTML.substring(0, 200) + '...',
            currentDisplay: modal?.style.display,
            computedDisplay: modal ? window.getComputedStyle(modal).display : null
        });
        
        if (modal) {
            // 添加active类来触发CSS显示动画
            modal.classList.add('active');
            
            console.log('✅ 模态框active类已添加');
            
        } else {
            console.error('❌ 找不到卡密绑定模态框元素');
        }
    }

    // 关闭卡密绑定模态框
    closeLicenseBindModal() {
        const modal = document.getElementById('licenseBindModal');
        if (modal) {
            // 移除active类来触发CSS隐藏动画
            modal.classList.remove('active');
        }
        // 清空表单
        this.clearLicenseBindForm();
    }

    // 填充店铺选择下拉框
    populateStoreSelectModal() {
        const storeSelect = document.getElementById('storeSelectModal');
        if (!storeSelect) return;

        // 清空现有选项
        storeSelect.innerHTML = '<option value="">请选择店铺...</option>';

        // 获取店铺数据
        const stores = window.realDataService.getStores();
        stores.forEach(store => {
            const option = document.createElement('option');
            option.value = store.id;
            option.textContent = `${store.displayName}`;
            storeSelect.appendChild(option);
        });
    }

    // 绑定卡密
    async bindLicense() {
        const storeSelect = document.getElementById('storeSelectModal');
        const licenseKeyInput = document.getElementById('licenseKeyInput');
        const bindBtn = document.getElementById('bindLicenseBtn');

        const wbSupplierId = storeSelect?.value;
        const licenseKey = licenseKeyInput?.value?.trim();

        if (!wbSupplierId) {
            this.showNotification('请选择要绑定的店铺', 'warning');
            return;
        }

        if (!licenseKey) {
            this.showNotification('请输入卡密', 'warning');
            return;
        }

        try {
            // 设置loading状态
            if (bindBtn) {
                bindBtn.disabled = true;
                bindBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 绑定中...';
            }

            // 调用绑定API
            const result = await window.wbAssistApiService.bindLicense(licenseKey, wbSupplierId);

            if (result.success) {
                this.showNotification(result.message || '卡密绑定成功！', 'success');
                this.closeLicenseBindModal();

                // 通知主应用重新检查店铺权限（这会自动刷新店铺显示）
                if (window.app && window.app.checkStoreCapabilities) {
                    await window.app.checkStoreCapabilities(wbSupplierId);
                }

                // 刷新店铺显示
                this.refreshStoreDisplay();
            }
        } catch (error) {
            console.error('卡密绑定失败:', error);
            this.showNotification('卡密绑定失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            if (bindBtn) {
                bindBtn.disabled = false;
                bindBtn.innerHTML = '<i class="fas fa-link"></i> 绑定卡密';
            }
        }
    }

    // 清空卡密绑定表单
    clearLicenseBindForm() {
        const storeSelect = document.getElementById('storeSelectModal');
        const licenseKeyInput = document.getElementById('licenseKeyInput');
        const licenseInfo = document.getElementById('licenseInfo');

        if (storeSelect) storeSelect.value = '';
        if (licenseKeyInput) licenseKeyInput.value = '';
        if (licenseInfo) licenseInfo.style.display = 'none';
    }

    // 打开店铺权限状态模态框
    openStoreCapabilitiesModal() {
        this.displayStoreCapabilities();
        const modal = document.getElementById('storeCapabilitiesModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    // 关闭店铺权限状态模态框
    closeStoreCapabilitiesModal() {
        const modal = document.getElementById('storeCapabilitiesModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 显示店铺权限信息
    displayStoreCapabilities() {
        const capabilitiesInfo = document.getElementById('capabilitiesInfo');
        if (!capabilitiesInfo) {
            return;
        }

        let html = '';
        const stores = window.realDataService.getStores();

        stores.forEach(store => {
            const capabilities = window.app?.storeCapabilities?.[store.id];
            html += `<div class="store-capability-item">`;
            html += `<h4>${store.displayName} (${store.id})</h4>`;

            if (capabilities) {
                const license = capabilities.license_binding;
                html += `
                    <div class="capability-status success">
                        <i class="fas fa-check-circle"></i>
                        <span>已绑定卡密</span>
                    </div>
                    <div class="capability-details">
                        <div class="detail-item">
                            <span class="label">卡密类型:</span>
                            <span class="value">${license.license_type}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">过期时间:</span>
                            <span class="value">${new Date(license.expires_at).toLocaleDateString()}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">月度配额:</span>
                            <span class="value">${license.remaining_quota}/${license.monthly_quota}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">状态:</span>
                            <span class="value ${license.status}">${license.status}</span>
                        </div>
                    </div>
                `;
            } else {
                html += `
                    <div class="capability-status warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>未绑定卡密或配额不足</span>
                    </div>
                    <div class="capability-actions">
                        <button class="btn btn-sm btn-primary" data-action="open-license-bind-modal">
                            绑定卡密
                        </button>
                    </div>
                `;
            }

            html += `</div>`;
        });

        capabilitiesInfo.innerHTML = html;
    }

    // 刷新店铺显示
    refreshStoreDisplay() {
        // 通知主应用刷新
        if (window.app && window.app.refreshStoreDisplay) {
            window.app.refreshStoreDisplay();
        } else if (window.app) {
            // 重新初始化店铺选择器
            window.app.initStoreSelector();
            // 重新加载订单
            window.app.loadOrders();
        }
    }

    // 显示通知（调用主应用的方法）
    showNotification(message, type = 'info') {
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// 创建全局卡密管理器实例
window.licenseManager = new LicenseManager();

// 等待DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.licenseManager.initialize();
});

// 全局函数已移除，现在使用data-action属性和事件监听器 