# 店铺权限全局共享重构技术文档

## 📋 重构概述

**重构名称**：店铺权限全局共享实现  
**重构日期**：2025年1月  
**重构版本**：v4.0 - 全局权限共享版  
**重构原因**：用户明确需求 - "跨浏览器、跨用户的店铺权限全局共享"

## 🎯 核心需求分析

### 用户最终需求
用户明确表示：**"就算你是不同浏览器，只要用户浏览器中存在已有的数据库店铺，就可以使用店铺的Ultimate/Pro权限"**

### 核心理念变更
```
v3.0 权限继承优先 → v4.0 全局权限共享

旧逻辑：权限转移，新用户"继承"店铺权限，变更所有权
新逻辑：权限共享，所有用户都能使用店铺权限，不变更所有权
```

### 业务模型重新定义
- **店铺权限全局化**：wb_supplier_id在系统中有权限，任何能WB认证此店铺的用户都能用
- **用户透明化**：WBAssist用户账户只管JWT和购买，权限使用完全看WB店铺
- **跨平台共享**：不管哪个浏览器、哪个WBAssist账户，只要能WB认证店铺就能用权限

## 🔧 技术实现方案

### 核心原则
1. **全局店铺查询**：所有接口基于`wb_supplier_id`进行全局查询，不限制`user_id`
2. **权限状态保持**：不转移店铺所有权，保持原始归属关系
3. **配额全局共享**：任何用户都能使用店铺的配额，扣减统一到全局店铺
4. **用户行为记录**：记录实际操作用户，但不影响权限判断

### 重构范围
```
重构Edge Functions：
├── stores-register (店铺同步) - 核心重构
├── capabilities (权限查询) - 核心重构  
├── tasks-create (任务创建) - 支持重构
└── tasks-report-success (成功上报) - 支持重构

数据库Schema：
└── 无变更，利用现有6表架构
```

## 📝 详细技术实现

### 1. stores-register接口重构

**重构前逻辑**：
```javascript
// v3.0 权限转移逻辑
if (existingStore) {
    // ❌ 转移所有权和相关数据到新用户
    await transferOwnership(existingStore, newUserId);
}
```

**重构后逻辑**：
```javascript
// v4.0 全局权限共享逻辑
async function syncStoresWithGlobalSharing(adminClient, currentUserId, localSuppliers) {
    for (const supplier of localSuppliers) {
        // 🎯 全局查询，不限用户
        const { data: globalStore } = await adminClient
            .from('stores')
            .select('*, license_bindings!inner(*, licenses!inner(*))')
            .eq('wb_supplier_id', supplier.id)
            .eq('status', 'active')
            .single();
            
        if (globalStore) {
            // 🎯 直接返回全局权限状态，不转移所有权
            existing_stores.push({
                ...globalStore,
                sync_status: 'global_shared_access',
                owner_user_id: globalStore.user_id,
                current_user_id: currentUserId,
                can_use_quota: true,  // 关键：任何用户都能使用
                is_shared: globalStore.user_id !== currentUserId
            });
        } else {
            // 🎯 新店铺：创建免费版权限
            const newStore = await createNewStoreWithFreeLicense(adminClient, currentUserId, supplier);
            new_stores.push(newStore);
        }
    }
}
```

### 2. capabilities接口重构

**重构前逻辑**：
```javascript
// 查询用户自己的店铺
.eq('user_id', userId)
.eq('wb_supplier_id', wbSupplierId)
```

**重构后逻辑**：
```javascript
// 🎯 全局店铺权限查询（不限用户）
const { data: store } = await supabase
    .from('stores')
    .select('*')
    .eq('wb_supplier_id', wbSupplierId)  // 只检查店铺ID
    .eq('status', 'active')
    .single();

// 🎯 全局许可证查询（不限用户）    
const { data: licenseBinding } = await supabase
    .from('license_bindings')
    .select('*, licenses!inner(*)')
    .eq('store_id', store.id)  // 只检查店铺ID
    .eq('status', 'active')
    .single();

// 🎯 返回共享权限信息
return {
    // ...权限详情
    sharing_info: {
        is_shared_access: store.user_id !== userId,
        original_owner_id: store.user_id,
        current_user_id: userId,
        can_use_quota: true,  // 🎯 关键：任何用户都能使用配额
        sharing_type: 'global_store_permission'
    }
};
```

### 3. tasks-create接口重构

**核心变更**：
```javascript
// 🎯 全局店铺权限验证（不限用户）
const { data: store } = await supabase
    .from('stores')
    .select('id, store_name, status, user_id')
    .eq('wb_supplier_id', wb_supplier_id)  // 全局查询
    .eq('status', 'active')
    .single();

// 🎯 检查卡密绑定和配额（全局查询）
const { data: licenseBinding } = await supabase
    .from('license_bindings')
    .select('*, licenses!inner(*)')
    .eq('store_id', store.id)  // 基于店铺ID，不限用户
    .eq('status', 'active')
    .single();

// 🎯 创建任务（使用当前用户ID，但可以使用共享店铺的配额）
const { data: newTask } = await supabase
    .from('tasks')
    .insert({
        user_id: userId,  // 任务创建者
        store_id: store.id,  // 全局共享店铺
        license_binding_id: licenseBinding.id,  // 全局共享许可
        // ...其他字段
    });
```

### 4. tasks-report-success接口重构

**核心变更**：
```javascript
// 🎯 全局权限模式：任何用户都可以为店铺任务上报成功（不限制任务创建者）
const isSharedAccess = task.user_id !== userId;
const isStoreOwner = task.stores.user_id === userId;
const isLicenseOwner = task.license_bindings.user_id === userId;

console.log(`任务权限分析:`, {
    task_creator: task.user_id,
    current_user: userId,
    store_owner: task.stores.user_id,
    license_owner: task.license_bindings.user_id,
    is_shared_access: isSharedAccess
});

// 🎯 只要任务和店铺存在，任何用户都能上报成功结果
// 配额扣减从全局店铺权限中扣减
await supabase
    .from('license_bindings')
    .update({
        current_month_used: newMonthUsed,
        total_used: (licenseBinding.total_used || 0) + 1
    })
    .eq('id', licenseBinding.id);  // 扣减全局店铺的配额

// 🎯 记录使用者信息
await supabase
    .from('credits_usage')
    .insert({
        user_id: userId,  // 实际使用者
        store_id: task.store_id,  // 全局店铺
        license_binding_id: licenseBinding.id,  // 全局许可证
        // ...其他字段
    });
```

## 📊 数据流转变化

### 重构前数据流（v3.0 权限转移）
```
新用户登录 → 检测冲突店铺 → 转移所有权 → 更新user_id → 新用户独占权限
```

### 重构后数据流（v4.0 全局共享）
```
任意用户登录 → 查询全局店铺 → 返回权限状态 → 多用户共享权限
```

### 关键数据变化对比

| 操作 | v3.0 权限转移模式 | v4.0 全局共享模式 |
|------|------------------|------------------|
| 店铺同步 | 转移store.user_id | 保持store.user_id不变 |
| 权限查询 | 查询用户店铺 | 查询全局店铺 |
| 任务创建 | 用户店铺权限 | 全局店铺权限 |
| 配额扣减 | 用户许可证配额 | 全局店铺配额 |

## 🧪 测试验证方案

### 测试场景设计
1. **原用户测试**：**********@qq.com重新登录，验证4个店铺权限完整
2. **新用户测试**：*******************登录，验证可使用4个店铺的全局权限
3. **跨浏览器测试**：不同浏览器登录相同或不同账户，验证权限一致性
4. **配额扣减测试**：任意用户使用配额后，所有用户看到的剩余配额一致

### 预期测试结果
```
用户**********@qq.com:
├── ИП Стельмах А. Л.: Ultimate权限，996次剩余配额
├── ИП Шеломенцева А. А.: Ultimate权限，999次剩余配额  
├── ИП Немковский Д. И.: Pro权限，3次剩余配额
└── ООО «ЛАЙТЕРА»: Basic权限，1次剩余配额

用户*******************:
├── ИП Стельмах А. Л.: Ultimate权限，996次剩余配额 (共享)
├── ИП Шеломенцева А. А.: Ultimate权限，999次剩余配额 (共享)
├── ИП Немковский Д. И.: Pro权限，3次剩余配额 (共享)
└── ООО «ЛАЙТЕРА»: Basic权限，1次剩余配额 (共享)
```

## 🔍 关键技术细节

### 全局查询策略
```sql
-- 不再使用用户限制的查询
SELECT * FROM stores WHERE user_id = $1 AND wb_supplier_id = $2;

-- 改为全局店铺查询
SELECT * FROM stores WHERE wb_supplier_id = $1 AND status = 'active';
```

### 权限判断逻辑
```javascript
// 旧逻辑：严格用户权限
if (store.user_id !== currentUserId) {
    throw new Error('无权限访问');
}

// 新逻辑：全局权限共享
const isSharedAccess = store.user_id !== currentUserId;
const canUseQuota = true;  // 任何用户都能使用
```

### 配额管理策略
```javascript
// 配额扣减：从全局店铺许可证扣减，不管操作用户
await supabase
    .from('license_bindings')
    .update({ current_month_used: newMonthUsed })
    .eq('store_id', globalStoreId);  // 全局店铺

// 使用记录：记录实际操作用户
await supabase
    .from('credits_usage')
    .insert({
        user_id: actualUserId,  // 实际使用者
        store_id: globalStoreId,  // 全局店铺
        usage_type: 'booking_success'
    });
```

## 📈 业务价值与影响

### 正面影响
1. **用户体验提升**：跨设备、跨账户无缝权限使用
2. **灵活性增强**：不再受限于单一用户账户
3. **协作模式支持**：团队成员可共享店铺权限
4. **技术债务减少**：简化了复杂的权限转移逻辑

### 潜在风险控制
1. **滥用防护**：保留用户操作记录，便于追踪
2. **安全边界**：仍需WB系统认证，不是完全开放
3. **配额管理**：全局配额统一管理，避免超额使用

## 🚀 部署状态

### Edge Functions部署状态
- ✅ `stores-register` v9 - 已部署生效
- ✅ `capabilities` v5 - 已部署生效  
- ✅ `tasks-create` v6 - 已部署生效
- ✅ `tasks-report-success` v3 - 已部署生效

### 数据迁移状态
- ✅ 重构日志已记录到`migration_logs`表
- ✅ 原始数据完整性已验证
- ✅ 权限状态已恢复到正确状态

## 📚 后续维护指南

### 监控要点
1. **权限使用模式**：监控跨用户权限使用情况
2. **配额消耗趋势**：关注全局配额的使用模式
3. **异常访问检测**：识别潜在的权限滥用行为

### 扩展空间
1. **权限精细化**：未来可按功能模块细分权限
2. **配额策略优化**：可实现更灵活的配额分配策略
3. **多租户支持**：为企业用户提供团队权限管理

---

## 总结

本次重构成功实现了**店铺权限全局共享**的核心需求，将系统从"用户中心的权限转移模式"升级为"店铺中心的全局共享模式"。重构后的系统更加灵活、用户友好，真正实现了"跨浏览器、跨用户的店铺权限全局共享"目标。

**重构版本**：v4.0 - 全局权限共享版  
**技术状态**：✅ 生产就绪 - 已完成部署  
**业务状态**：✅ 功能验证 - 等待用户测试确认 