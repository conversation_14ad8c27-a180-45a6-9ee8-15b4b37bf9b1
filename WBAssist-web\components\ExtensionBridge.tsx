'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/utils/supabase/client'

// 插件通信消息类型
interface ExtensionMessage {
  type: string
  payload?: any
}

// 插件通信组件
export default function ExtensionBridge() {
  const [isExtensionConnected, setIsExtensionConnected] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false) // 🔒 防止重复处理
  const [hasDetected, setHasDetected] = useState(false) // 🔒 防止重复检测插件
  const [lastSyncTime, setLastSyncTime] = useState(0) // 🕐 限制同步频率
  const [lastAuthEvent, setLastAuthEvent] = useState<string>('') // 🔄 缓存上次认证事件
  const supabase = createClient()

  useEffect(() => {
    // 检测插件是否存在
    const detectExtension = () => {
      // 🔒 只在首次检测时发送消息
      if (hasDetected) {
        console.log('🔄 插件已检测过，跳过重复检测')
        return
      }
      
      setHasDetected(true)
      // 向页面发送检测消息
      window.postMessage({
        type: 'WBASSIST_WEBSITE_READY',
        payload: { timestamp: Date.now() }
      }, '*')
      console.log('🔍 首次检测插件，已发送WEBSITE_READY消息')
    }

    // 处理来自插件的消息
    const handleExtensionMessage = async (event: MessageEvent) => {
      // 验证消息来源（在生产环境中应该更严格）
      if (event.source !== window) return
      
      // 🔒 防止重复处理
      if (isProcessing) {
        console.log('⚠️ 正在处理消息，跳过重复请求')
        return
      }

      const message: ExtensionMessage = event.data
      
      // 只处理WBAssist相关消息
      if (!message.type?.startsWith('WBASSIST_')) return

      try {
        setIsProcessing(true)

      switch (message.type) {
        case 'WBASSIST_EXTENSION_READY':
          console.log('🔌 插件已连接')
          setIsExtensionConnected(true)
          // 如果用户已登录，同步认证状态
          await syncAuthToExtension()
          break

        case 'WBASSIST_REQUEST_AUTH_STATUS':
          console.log('🔐 插件请求认证状态')
          await syncAuthToExtension()
          break

        case 'WBASSIST_REQUEST_USER_CARDS':
          console.log('🎫 插件请求用户卡密信息')
          await syncUserCardsToExtension()
          break
          
        case 'WBASSIST_WEBSITE_READY':
          // 🔄 忽略网站自己发送的准备就绪消息，避免循环
          console.log('🔄 收到网站准备就绪消息，已忽略')
          break
          
        case 'WBASSIST_AUTH_SUCCESS':
          // 🔄 忽略从插件返回的认证成功消息，避免循环
          console.log('🔄 收到插件认证确认消息，已忽略')
          break

        default:
          // 📨 只记录真正未知的消息，减少日志噪音
          if (!message.type.startsWith('WBASSIST_WEBSITE_') && 
              !message.type.startsWith('WBASSIST_AUTH_')) {
          console.log('📨 收到未知插件消息:', message.type)
          }
        }
      } catch (error) {
        console.error('❌ 处理插件消息失败:', error)
      } finally {
        setIsProcessing(false)
      }
    }

    // 同步认证状态到插件
    const syncAuthToExtension = async () => {
      try {
        // 🕐 限制同步频率，避免过度调用
        const now = Date.now()
        if (now - lastSyncTime < 2000) {
          console.log('⏱️ 同步太频繁，跳过此次认证同步')
          return
        }
        setLastSyncTime(now)
        
        // 🚀 直接使用同步方法获取session，避免超时问题
        let session = null
        try {
          const { data: { session: currentSession } } = await supabase.auth.getSession()
          session = currentSession
        } catch (sessionError) {
          console.warn('⚠️ getSession失败，尝试从User获取:', sessionError)
          // 回退方案：直接从getUser获取
          const { data: { user } } = await supabase.auth.getUser()
          if (user) {
            // 如果有用户但无session，创建一个基本的session对象
            session = {
              user,
              access_token: 'fallback_token' // 后端会重新验证
            }
          }
        }
        
        if (session?.user) {
          // 发送认证成功消息到插件
          window.postMessage({
            type: 'WBASSIST_AUTH_SUCCESS',
            payload: {
              token: session.access_token,
              user: {
                id: session.user.id,
                email: session.user.email,
                name: session.user.user_metadata?.full_name || session.user.email
              },
              supabase_url: process.env.NEXT_PUBLIC_SUPABASE_URL,
              timestamp: Date.now()
            }
          }, '*')
          
          console.log('✅ 认证状态已同步到插件')
        } else {
          // 发送登出消息到插件
          window.postMessage({
            type: 'WBASSIST_LOGOUT',
            payload: { timestamp: Date.now() }
          }, '*')
          
          console.log('🚪 登出状态已同步到插件')
        }
      } catch (error) {
        console.error('❌ 同步认证状态失败:', error)
        // 🛡️ 不重新抛出错误，避免阻塞页面
      }
    }

    // 同步用户卡密信息到插件
    const syncUserCardsToExtension = async () => {
      try {
        // 🚀 进一步缩短超时时间
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('获取用户卡密超时')), 3000)
        )
        
        const sessionPromise = supabase.auth.getSession()
        const { data: { session } } = await Promise.race([sessionPromise, timeoutPromise]) as any
        
        if (!session?.user) {
          console.log('⚠️ 用户未登录，无法获取卡密信息')
          return
        }

        // 🔧 简化查询，减少关联表数量
        const queryPromise = supabase
          .from('license_bindings')
          .select('*')
          .eq('user_id', session.user.id)
          .eq('status', 'active')
          .limit(10) // 🚀 限制查询结果数量

        const { data: licenseBindings, error } = await Promise.race([queryPromise, timeoutPromise]) as any

        if (error) {
          console.error('❌ 获取用户卡密失败:', error)
          return
        }

        // 🚀 简化数据格式，减少数据传输量
        const cards = (licenseBindings || []).map(binding => ({
          id: binding.id,
          license_key: binding.license_key || '未知',
          license_type: binding.license_type || 'unknown',
          monthly_quota: binding.monthly_quota || 0,
          current_month_used: binding.current_month_used || 0,
          expires_at: binding.expires_at,
          wb_supplier_id: binding.wb_supplier_id,
          status: binding.status
        }))

        // 发送卡密信息到插件
        window.postMessage({
          type: 'WBASSIST_USER_CARDS_UPDATE',
          payload: {
            cards: cards,
            timestamp: Date.now()
          }
        }, '*')

        console.log('🎫 用户卡密信息已同步到插件')
      } catch (error) {
        console.error('❌ 同步卡密信息失败:', error)
        // 🛡️ 不重新抛出错误，避免阻塞页面
        // 发送空的卡密信息作为回退
        try {
          window.postMessage({
            type: 'WBASSIST_USER_CARDS_UPDATE',
            payload: {
              cards: [],
              timestamp: Date.now(),
              error: '获取卡密信息失败'
            }
          }, '*')
        } catch (postError) {
          console.error('发送回退消息失败:', postError)
        }
      }
    }

    // 🚀 移除自动认证状态监听，改为手动触发模式
    // 根据系统架构蓝本5.0.1，网站端不应该与插件频繁通信认证状态
    // const { data: { subscription } } = supabase.auth.onAuthStateChange(...)
    
    // 仅在用户明确的登录/登出操作时才同步认证状态
    const handleAuthAction = async (action: 'login' | 'logout') => {
      console.log('🔐 处理认证动作:', action)
      if (action === 'logout') {
        // 登出时清除插件认证状态
        window.postMessage({
          type: 'WBASSIST_LOGOUT',
          payload: { timestamp: Date.now() }
        }, '*')
        console.log('🚪 已发送登出消息到插件')
      } else if (action === 'login') {
        // 登录时同步一次认证信息
        await syncAuthToExtension()
      }
    }
    
    // 暴露给全局，供登录/登出组件调用
    ;(window as any).wbAssistAuthAction = handleAuthAction

    // 添加消息监听器
    window.addEventListener('message', handleExtensionMessage)
    
    // 检测插件
    detectExtension()

    // 清理函数
    return () => {
      window.removeEventListener('message', handleExtensionMessage)
      // subscription已移除，无需清理
    }
  }, [supabase]) // 🔧 移除isExtensionConnected和isProcessing依赖，避免重复注册监听器

  // 手动触发卡密激活（用于测试）
  const triggerCardActivation = (cardCode: string, plan: string) => {
    window.postMessage({
      type: 'WBASSIST_CARD_ACTIVATED',
      payload: {
        cardCode,
        plan,
        expiry: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天后过期
        timestamp: Date.now()
      }
    }, '*')
    
    console.log('🎉 卡密激活消息已发送到插件')
  }

  // 移除开发环境弹窗显示

  return null
}
