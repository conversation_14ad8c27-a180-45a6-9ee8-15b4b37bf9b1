'use client';

import { useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export const dynamic = 'force-dynamic';

function PaymentSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const orderId = searchParams.get('orderId');
    const tradeStatus = searchParams.get('trade_status');
    
    if (!orderId) {
      // 如果没有orderId，跳转到首页
      router.push('/');
      return;
    }

    // 如果有支付成功的参数，说明是从支付平台返回的，跳转到卡密展示页面
    if (tradeStatus === 'TRADE_SUCCESS') {
      router.push(`/payment/success/${orderId}`);
      return;
    }

    // 否则跳转到卡密展示页面（用户直接访问或其他情况）
    router.push(`/payment/success/${orderId}`);
  }, [searchParams, router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">正在处理您的支付结果...</h1>
          <p className="text-gray-600">请稍候片刻</p>
        </div>
      </div>
    </div>
  );
}

export default function PaymentSuccessRedirect() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">正在加載...</h1>
            <p className="text-gray-600">請稍候片刻</p>
          </div>
        </div>
      </div>
    }>
      <PaymentSuccessContent />
    </Suspense>
  );
} 