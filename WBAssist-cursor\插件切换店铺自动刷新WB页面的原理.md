# 🔄 插件切换店铺自动刷新WB页面的原理

## 📊 核心机制：Cookie劫持与注入

### 🎯 完整流程

#### 1. 用户触发切换
```javascript
// 文件: cnakubengbi/js/main.js (第559行)
await window.wbApiService.switchToSupplier(store.id);
```

#### 2. 插件前端 → Background Script
```javascript
// 文件: cnakubengbi/js/api-service.js (第101-103行)
chrome.runtime.sendMessage({
    action: 'switchSupplier',
    supplierId: supplierId
});
```

#### 3. Background Script处理
```javascript
// 文件: cnakubengbi/background.js (第639-650行)
async function switchToSupplier(supplierId) {
    // 1. 保存到插件存储
    await setStorage({ currentSupplierId: supplierId });
    
    // 2. 🔑 关键：修改WB网站的Cookie
    await setCurrentSupplierIdInWB(supplierId, supplierId);
    return { success: true };
}
```

#### 4. 🔑 核心：Cookie注入机制
```javascript
// 文件: cnakubengbi/background.js (第598-636行)
async function setCurrentSupplierIdInWB(supplierId, supplierIdExternal) {
    const domains = ['.wildberries.ru', '.seller.wildberries.ru'];
    
    for (const domain of domains) {
        // 🎯 设置关键Cookie：x-supplier-id-external
        await chrome.cookies.set({
            url: `https://seller.wildberries.ru`,
            name: 'x-supplier-id-external',
            value: supplierIdExternal,
            domain: domain,      // 作用域：整个wildberries.ru域
            path: '/',
            secure: true,
            httpOnly: false
        });
        
        // 🎯 设置关键Cookie：x-supplier-id  
        await chrome.cookies.set({
            url: `https://seller.wildberries.ru`,
            name: 'x-supplier-id',
            value: supplierId,
            domain: domain,      // 作用域：整个wildberries.ru域
            path: '/',
            secure: true,
            httpOnly: false
        });
    }
}
```

## 🧠 WB网站的反应机制

### WB网站如何检测Cookie变化

#### 1. **前端JavaScript监听**
WB网站的前端JavaScript会定期检查或监听cookie变化：
```javascript
// WB网站内部可能的实现（推测）
const currentSupplierId = document.cookie.match(/x-supplier-id=([^;]+)/)?.[1];

// 检测到supplier ID变化时自动刷新数据
if (currentSupplierId !== lastKnownSupplierId) {
    refreshStoreData(currentSupplierId);
    lastKnownSupplierId = currentSupplierId;
}
```

#### 2. **API请求自动携带新Cookie**
WB网站的AJAX请求会自动携带新的cookie：
```javascript
// WB API调用时自动包含新的x-supplier-id
fetch('/api/some-endpoint', {
    credentials: 'include'  // 自动携带所有cookie
});
```

#### 3. **服务端状态检测**
WB后端检测到不同的supplier-id时，返回对应店铺的数据。

## 🔍 技术细节分析

### Cookie设置的精妙之处

#### 1. **域名覆盖策略**
```javascript
const domains = ['.wildberries.ru', '.seller.wildberries.ru'];
```
- `.wildberries.ru`: 覆盖所有wildberries子域名
- `.seller.wildberries.ru`: 特定覆盖seller子域名
- 确保所有WB相关页面都能读取到新cookie

#### 2. **双重Cookie机制**
```javascript
'x-supplier-id-external': supplierIdExternal,  // 外部供应商ID
'x-supplier-id': supplierId,                   // 内部供应商ID
```
WB使用两个cookie来保证兼容性和安全性。

#### 3. **Cookie属性设置**
```javascript
{
    domain: domain,     // 域名作用范围
    path: '/',          // 路径作用范围（根路径）
    secure: true,       // 仅HTTPS传输
    httpOnly: false     // JavaScript可访问
}
```

## 🎭 为什么WB页面会自动"刷新"

### 现象分析
用户看到的"刷新"实际上是：

#### 1. **数据重新加载**

Cookie变化 → WB检测 → API重新请求 → 界面更新新店铺数据


#### 2. **界面状态更新**
- 店铺名称改变
- 订单列表更新为新店铺的订单
- 权限状态可能改变

#### 3. **可能的页面跳转**
某些情况下WB可能会：
```javascript
// WB可能的内部处理
if (supplierChanged) {
    window.location.reload();  // 强制刷新页面
    // 或
    window.location.href = '/dashboard?supplier=' + newSupplierId;
}
```

## 🚀 这种设计的优势

### 1. **用户体验无缝**
- 插件切换 → WB页面立即响应
- 无需手动刷新WB页面
- 状态同步自动完成

### 2. **技术实现巧妙**
- 利用浏览器原生Cookie机制
- 不需要修改WB网站代码
- 完全通过浏览器扩展权限实现

### 3. **兼容性强**
- 适用于所有WB页面
- 不依赖特定的WB版本
- 利用标准Web技术

## ⚡ 可能的风险和限制

### 1. **依赖WB内部实现**
如果WB改变cookie检测机制，这个功能可能失效。

### 2. **权限要求**
需要Chrome扩展的cookie权限：
```json
// manifest.json
"permissions": [
    "cookies",
    "https://seller.wildberries.ru/*",
    "https://*.wildberries.ru/*"
]
```

### 3. **时序问题**
Cookie设置和WB检测之间可能有微小延迟。

---

## 🎯 总结

**原理核心**: 通过Chrome扩展API修改WB网站的关键Cookie (`x-supplier-id`, `x-supplier-id-external`)，让WB网站"误以为"用户通过正常方式切换了店铺，从而自动重新加载对应店铺的数据。

这是一个非常聪明的"Cookie劫持"实现，既保证了功能完整性，又提供了良好的用户体验！