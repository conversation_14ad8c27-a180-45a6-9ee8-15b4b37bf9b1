/* 全局样式和基础设置 */
:root {
    /* 颜色变量 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 过渡 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Header 样式 */
.header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(8px);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: 18px;
    color: var(--primary-color);
}

.logo i {
    font-size: 24px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* 连接状态指示器 */
.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    opacity: 0.6;
    animation: pulse 2s infinite;
}

.status-indicator.connecting {
    background: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.status-indicator.connecting::after {
    background: #f59e0b;
}

.status-indicator.connected {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-indicator.connected::after {
    background: #10b981;
}

.status-indicator.error {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.status-indicator.error::after {
    background: #ef4444;
}

.status-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.3;
    }
}

.user-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* 网站集成按钮 */
.auth-actions {
    display: flex;
    gap: 8px;
    margin-left: 12px;
}

.btn-website-login,
.btn-website-upgrade {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-website-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-website-login:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-website-upgrade {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.btn-website-upgrade:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 87, 108, 0.3);
}

.plan-badge-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    overflow: hidden;
}

.plan-icon {
    font-size: 12px;
    width: 14px;
    text-align: center;
}

.plan-badge {
    margin: 0;
    color: white;
}

.plan-badge-container.free {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
}

.plan-badge-container.free .plan-icon {
    color: white;
}

.plan-badge-container.pro {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.plan-badge-container.pro .plan-icon {
    color: white;
}

.plan-badge-container.ultimate {
    background: linear-gradient(135deg, #a855f7 0%, #f97316 100%);
    color: white;
}

.plan-badge-container.ultimate .plan-icon {
    color: white;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: var(--transition-fast);
}

.user-avatar:hover {
    transform: scale(1.05);
}

/* 主要内容区域 */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-lg);
    display: grid;
    gap: var(--spacing-xl);
    min-height: calc(100vh - 80px); /* 减去header高度 */
}

/* 店铺信息面板 */
.store-panel {
    grid-column: 1 / -1;
    margin-bottom: 4rem; /* 增加与下方组件的间距，确保有足够的视觉分离 */
}

.store-info-card {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.store-info-card:hover {
    box-shadow: var(--shadow-md);
}

.store-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.store-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.store-header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.store-selector {
    min-width: 280px;
}

.license-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.custom-select-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.store-dropdown {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--surface-color);
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition-fast);
    appearance: none;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.store-dropdown:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1), var(--shadow-md);
}

.store-dropdown:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.select-arrow {
    position: absolute;
    right: var(--spacing-md);
    pointer-events: none;
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.custom-select-wrapper:hover .select-arrow {
    color: var(--primary-color);
}

.store-dropdown:focus + .select-arrow {
    color: var(--primary-color);
    transform: rotate(180deg);
}

.store-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* 表头样式 */
.store-details-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.2fr 1fr 1.5fr;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) 0;
}

.store-header-item {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    background: linear-gradient(135deg, var(--text-secondary) 0%, var(--text-muted) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    text-align: center;
}

.store-header-item::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    border-radius: 1px;
}

/* 数据行样式 */
.store-details-data {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.2fr 1fr 1.5fr;
    gap: var(--spacing-md);
    align-items: center;
    padding: var(--spacing-md) 0;
    border-top: 1px solid var(--border-color);
}

.store-data-item {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}



.credits-count {
    color: var(--primary-color);
    font-weight: 600;
}

.credits-count.unlimited {
    color: var(--success-color);
}

.credits-count.low {
    color: var(--warning-color);
}

.credits-count.empty {
    color: var(--danger-color);
}

/* 店铺等级信息样式 */
.store-plan-info {
    display: flex;
    align-items: center;
}

.plan-badge-with-icon {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.plan-badge-with-icon.free .plan-icon {
    color: #64748b;
}

.plan-badge-with-icon.pro .plan-icon {
    color: #3b82f6;
}

.plan-badge-with-icon.ultimate .plan-icon {
    color: #a855f7;
}

.plan-badge-with-icon .fa-exclamation-triangle {
    color: #ffa502;
}

.plan-badge-with-icon .plan-icon {
    font-size: 16px;
}

.plan-text {
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

/* 独立的店铺等级图标样式（用于店铺详情表格） */
.plan-icon-basic {
    color: #6b7280;
}

.plan-icon-pro {
    color: #f59e0b;
}

.plan-icon-ultimate {
    color: #8b5cf6;
}

.plan-icon-warning {
    color: #f59e0b;
}

.plan-icon-error {
    color: #ef4444;
}

.plan-icon-unknown {
    color: #6b7280;
}

/* 剩余天数样式 */
.days-remaining {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.days-remaining.expired {
    color: #ff4757;
}

.days-remaining.critical {
    color: #ff6b35;
}

.days-remaining.warning {
    color: #ffa502;
}

.days-remaining.active {
    color: var(--text-primary);
}

/* 订单部分 */
.orders-section {
    grid-column: 1 / -1;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.section-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.filters {
    display: flex;
    gap: var(--spacing-md);
}

/* 刷新按钮样式 */
.btn-refresh {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 16px;
}

.btn-refresh:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-refresh:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-refresh.loading {
    pointer-events: none;
}

.btn-refresh.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface-color);
    font-size: 14px;
    min-width: 120px;
    transition: var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* 订单容器 */
.orders-container {
    display: grid;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.pagination-btn {
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface-color);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0 var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-container {
        padding: 0 var(--spacing-md);
    }

    .user-info {
        gap: var(--spacing-sm);
    }

    .connection-status {
        padding: var(--spacing-xs);
    }

    .status-text {
        font-size: 0.8rem;
    }

    .main-content {
        padding: var(--spacing-lg) var(--spacing-md);
        gap: var(--spacing-lg);
    }

    .store-info-card {
        padding: var(--spacing-lg);
    }

    .store-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .store-details {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .header-actions {
        justify-content: space-between;
        align-items: center;
    }

    .btn-refresh {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
}

/* 连接错误弹窗样式 */
.connection-error-modal {
    max-width: 450px;
    text-align: center;
}

.connection-error-modal .modal-header h3 {
    color: var(--danger-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.error-icon {
    font-size: 48px;
    color: var(--danger-color);
    margin-bottom: var(--spacing-lg);
}

.error-icon i {
    animation: shake 0.5s ease-in-out;
}

.error-message {
    font-size: 16px;
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.connection-error-modal .modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 加载状态样式 */
.loading-orders {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-style: italic;
}

.loading-orders::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-sm);
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
