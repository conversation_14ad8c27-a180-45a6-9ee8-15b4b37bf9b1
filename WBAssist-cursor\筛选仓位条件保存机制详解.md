# 📊 筛选仓位条件保存机制详解

## ✅ 保存的筛选条件

### 1. 包含的筛选条件
```javascript
// 文件: cnakubengbi/js/booking-wizard.js (第132-142行)
this.bookingData = {
    orderId: orderId,
    type: type,
    selectedDates: [],          // 🎯 用户选择的目标日期
    bufferDays: 1,              // 🎯 缓冲天数
    maxCoefficient: 20,         // 🎯 最大承兑系数
    autoAccept: true,
    originalPreorderId: order.preorderId
};
```

### 2. 完整的存储层次

#### 第一层：BookingWizard临时存储
```javascript
// 用户操作时实时更新
this.bookingData.selectedDates = dates;       // 选择日期
this.bookingData.bufferDays = parseInt(value); // 缓冲天数
this.bookingData.maxCoefficient = coefficient;  // 最大系数
```

#### 第二层：Chrome Storage持久化
```javascript
// 文件: cnakubengbi/js/booking-storage.js (第194-207行)
async setBookingConfig(orderId, config) {
    const key = String(orderId);
    this.bookingConfigs.set(key, {
        ...config,
        orderId: orderId,
        timestamp: new Date().toISOString()
    });
    
    await this.saveToStorage(); // 保存到chrome.storage.local
    return this.bookingConfigs.get(key);
}
```

#### 第三层：Background Script监控配置
```javascript
// 文件: cnakubengbi/js/booking-wizard.js (第732-743行)
const monitoringConfig = {
    taskId: taskResult.task_id,
    orderId: orderId,
    selectedDates: this.bookingData.selectedDates,    // ✅ 保存筛选条件
    bufferDays: this.bookingData.bufferDays,          // ✅ 保存筛选条件
    maxCoefficient: this.bookingData.maxCoefficient,  // ✅ 保存筛选条件
    storeId: currentSupplierId,
    startTime: new Date().toISOString(),
    status: 'monitoring'
};
```

## 📁 存储位置和结构

### Chrome Storage结构
```javascript
// 存储键: 'wb_booking_configs'
{
    "订单ID1": {
        "selectedDates": ["2024-03-15", "2024-03-16"],
        "bufferDays": 2,
        "maxCoefficient": 15,
        "timestamp": "2024-03-14T10:30:00Z"
    },
    "订单ID2": {
        "selectedDates": ["2024-03-20"],
        "bufferDays": 1,
        "maxCoefficient": 20,
        "timestamp": "2024-03-14T11:15:00Z"
    }
}
```

### Background Script内存缓存
```javascript
// 文件: cnakubengbi/background.js (第1725行)
let activeMonitorings = new Map(); // 存储活跃的监控任务配置
```

## 🔄 数据流转过程

### 1. 用户设置筛选条件
用户界面操作 → BookingWizard.bookingData → 实时更新


### 2. 提交预约时保存
```javascript
// 文件: cnakubengbi/js/booking-wizard.js (第687-692行)
const taskConfig = {
    date_range: this.bookingData.selectedDates,     // 保存选择日期
    coefficient_min: this.bookingData.maxCoefficient, // 保存最大系数
    buffer_days: this.bookingData.bufferDays        // 保存缓冲天数
};
```

### 3. 传递到Background监控
```javascript
// 通过chrome.runtime.sendMessage传递到后台
await this.sendMessageToBackground('startMonitoring', {
    config: monitoringConfig  // 包含完整筛选条件
});
```

### 4. 监控过程中使用
```javascript
// 文件: cnakubengbi/background.js (第2540行)
const { selectedDates, bufferDays, maxCoefficient } = monitoring;

// 在筛选仓位时使用这些条件
const filteredSlots = filterCostsByUserConditions(costs, monitoring);
```

## 🎯 持久化特性

### ✅ 持久保存
- **跨浏览器会话**: 保存在chrome.storage.local中
- **多标签页同步**: 通过chrome.storage.onChanged事件同步
- **监控续行**: 浏览器重启后监控配置仍然有效

### ✅ 自动恢复
```javascript
// 文件: cnakubengbi/background.js (第2605-2620行)
async function getBookingConfigFromStorage(orderId) {
    const result = await chrome.storage.local.get(['wb_booking_configs']);
    const configs = result['wb_booking_configs'] || {};
    return configs[String(orderId)] || null;
}
```

### ✅ 清理机制
```javascript
// 文件: cnakubengbi/js/booking-storage.js (第380-401行)
async cleanupExpiredMappings(maxAge = 90 * 24 * 60 * 60 * 1000) {
    // 自动清理90天前的配置
}
```

## 📊 总结

**保存状态**: ✅ **完整保存**
- **选择日期**: selectedDates数组
- **缓冲天数**: bufferDays数值  
- **最大系数**: maxCoefficient数值
- **订单信息**: orderId, type等

**存储方式**: 
- 📁 Chrome本地存储 (持久化)
- 💾 内存缓存 (快速访问)
- 🔄 多层同步 (数据一致性)

**应用场景**:
- ✅ 预约监控过程中使用筛选条件
- ✅ 浏览器重启后恢复监控状态
- ✅ 用户可以查看历史预约配置