// WBAssist后端API服务类 - 处理与后端系统的集成
class WBAssistApiService {
    constructor() {
        // 从环境变量或配置获取后端URL
        this.backendUrl = this.getBackendUrl();
        this.supabaseUrl = this.getSupabaseUrl();
        this.authToken = null;
        this.isAuthenticated = false;
        this.userInfo = null;
        this.loginPopup = null;
        this.registerPopup = null;
        this.messageListenerSetup = false;
        this.lastProcessedToken = null;
        this.pendingMessages = []; // 存储在监听器设置前收到的消息
        
        // 立即设置消息监听器，确保第一次点击就能收到消息
        this.setupAuthMessageListener();
    }

    // 获取后端URL配置（网站端）
    getBackendUrl() {
        // 🔧 使用统一的API配置 - 只需要修改 constants.js 中的配置即可
        try {
            // 优先使用全局API配置
            if (typeof window !== 'undefined' && window.API_CONFIG) {
                return window.API_CONFIG.getApiUrl();
            }
        } catch (e) {
            console.warn('API_CONFIG not loaded, using fallback detection');
        }
        
        // 备用方案：手动环境检测
        try {
            // 如果当前页面是localhost，说明在本地开发
            if (window.location.hostname === 'localhost' || 
                document.location.href.includes('localhost:3000')) {
                return 'http://localhost:3000';
            }
        } catch (e) {
            // 在扩展环境中无法访问location，使用默认设置
        }
        
        // 生产环境fallback（使用正确的生产地址）
        return 'https://wb-assist-web.vercel.app';
    }

    // 获取Supabase URL配置（Edge Functions）
    getSupabaseUrl() {
        // 🔧 直接使用固定的Supabase URL，避免null值
        return 'https://cdnpddlbecxqnpkfkvid.supabase.co';
    }

    // 获取认证信息（从background script获取）
    async initializeFromWebsite() {
        try {
            console.log('🔄 开始初始化WBAssist API服务...');
            
            // 🚀 优先从存储获取认证信息（更快，避免无效重试）
            const storageData = await this.getAuthFromStorage();
            if (storageData && storageData.access_token) {
                this.authToken = storageData.access_token;
                this.userInfo = storageData.user;
                this.isAuthenticated = true;
                console.log('✅ 从存储加载认证信息成功:', {
                    user: this.userInfo?.email || '未知用户',
                    hasToken: !!this.authToken
                });
                return true;
            }
            
            // 🔄 如果存储中没有认证信息，尝试通过background script获取（仅1次尝试）
            console.log('💾 存储中无认证信息，尝试从background获取...');
            const authData = await this.getAuthFromBackground();
            if (authData && authData.authenticated) {
                this.authToken = authData.access_token;
                this.userInfo = authData.user;
                this.isAuthenticated = true;
                console.log('✅ 从background加载认证信息成功:', {
                    user: this.userInfo?.email || '未知用户',
                    hasToken: !!this.authToken
                });
                return true;
            }
            
            console.log('❌ 未找到有效的认证信息，等待网站同步...');
            return false;
        } catch (error) {
            console.error('❌ 初始化WBAssist API服务失败:', error);
            return false;
        }
    }

    // 带重试机制的认证信息获取
    async getAuthFromBackgroundWithRetry(maxRetries = 3, delay = 1000) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                console.log(`🔄 尝试获取认证信息 (${i + 1}/${maxRetries})...`);
                const authData = await this.getAuthFromBackground();
                
                if (authData && authData.authenticated) {
                    console.log(`✅ 第${i + 1}次尝试成功获取认证信息`);
                    return authData;
                }
                
                if (i < maxRetries - 1) {
                    console.log(`⏳ 第${i + 1}次尝试未获取到认证信息，${delay}ms后重试...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    delay *= 1.5; // 指数退避
                }
            } catch (error) {
                console.error(`❌ 第${i + 1}次尝试获取认证信息失败:`, error);
                if (i < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                    delay *= 1.5;
                }
            }
        }
        
        console.log(`❌ ${maxRetries}次尝试后仍未获取到认证信息`);
        return null;
    }

    // 从background script获取认证信息
    async getAuthFromBackground() {
        return new Promise((resolve) => {
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                // 设置超时处理
                const timeout = setTimeout(() => {
                    console.warn('⏰ 从background获取认证信息超时');
                    resolve(null);
                }, 5000); // 5秒超时

                chrome.runtime.sendMessage({ action: 'get_wbassist_auth' }, (response) => {
                    clearTimeout(timeout);
                    
                    if (chrome.runtime.lastError) {
                        console.error('❌ 获取认证信息失败:', chrome.runtime.lastError.message);
                        resolve(null);
                    } else if (response && response.authenticated) {
                        console.log('✅ 成功从background获取认证信息');
                        resolve(response);
                    } else {
                        console.log('⚠️ Background返回了无效的认证信息:', response);
                        resolve(null);
                    }
                });
            } else {
                console.warn('⚠️ Chrome runtime不可用');
                resolve(null);
            }
        });
    }

    // 从存储中获取认证信息
    async getAuthFromStorage() {
        if (typeof chrome === 'undefined' || !chrome.storage) {
            return null;
        }

        return new Promise((resolve) => {
            chrome.storage.local.get(['wbassist_auth'], (result) => {
                if (chrome.runtime.lastError) {
                    console.error('获取认证信息失败:', chrome.runtime.lastError);
                    resolve(null);
                } else {
                    resolve(result.wbassist_auth || null);
                }
            });
        });
    }

    // 保存认证信息到存储
    async saveAuthToStorage(authData) {
        if (typeof chrome === 'undefined' || !chrome.storage) {
            return false;
        }

        return new Promise((resolve) => {
            chrome.storage.local.set({ wbassist_auth: authData }, () => {
                if (chrome.runtime.lastError) {
                    console.error('保存认证信息失败:', chrome.runtime.lastError);
                    resolve(false);
                } else {
                    resolve(true);
                }
            });
        });
    }

    // 清除认证信息
    async clearAuth() {
        console.log('🧹 开始清理认证状态...');
        
        // 🔧 清理本地认证状态（最可靠的方案）
        this.authToken = null;
        this.isAuthenticated = false;
        this.userInfo = null;
        
        // 🔧 清理Chrome存储中的认证数据
        try {
            await new Promise((resolve) => {
                chrome.storage.local.remove(['wbassist_auth'], () => {
                    console.log('🗑️ Chrome存储认证数据已清理');
                    resolve();
                });
            });
        } catch (error) {
            console.error('❌ 清理Chrome存储失败:', error);
        }
        
        // 🔧 清理浏览器Cookie（尽力而为）
        try {
            // 注意：这只能清理当前域名的Cookie，不能清理Supabase域名的Cookie
            if (typeof document !== 'undefined') {
                // 获取当前域名的所有Cookie并设置为过期
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });
                console.log('🍪 本地Cookie已清理');
            }
        } catch (error) {
            console.warn('⚠️ Cookie清理时出错:', error);
        }
        
        console.log('🧹 认证状态清理完成（依靠强制重新认证确保完整登出）');
    }

    // 打开登录页面（弹窗模式，支持强制重新登录）
    async openLoginPage(forceReauth = false) {
        // 🔧 构建登录URL，支持强制重新认证
        let loginUrl = `${this.backendUrl}/signin?redirect=${encodeURIComponent('extension://plugin-auth')}`;
        
        // 🔧 如果是强制重新认证，添加参数清理缓存状态
        if (forceReauth) {
            loginUrl += '&force_reauth=true&t=' + Date.now(); // 添加时间戳避免缓存
            console.log('🔄 强制重新认证模式启动');
        }
        
        console.log('🔐 打开登录页面:', loginUrl);

        this.loginPopup = window.open(
            loginUrl,
            'wbassist-login',
            'width=500,height=700,scrollbars=yes,resizable=yes'
        );

        if (this.loginPopup) {
            // 监控弹窗是否被用户手动关闭
            const checkClosed = setInterval(() => {
                if (this.loginPopup.closed) {
                    clearInterval(checkClosed);
                    console.log('🔒 登录弹窗已关闭');
                }
            }, 1000);
        }
    }

    // 检查用户认证状态（调用extension-auth Edge Function）
    async checkAuthStatus() {
        if (!this.authToken) {
            return { authenticated: false, message: '未找到认证令牌' };
        }

        try {
            // 🔧 修正：所有插件API都使用Supabase Edge Functions
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000); // 缩短超时

            const response = await fetch(`${this.supabaseUrl}/functions/v1/extension-auth`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const data = await response.json();
                this.isAuthenticated = data.authenticated;
                this.userInfo = data.user;
                
                console.log('🔐 认证状态验证成功:', {
                    authenticated: data.authenticated,
                    user: data.user?.email || '未知用户'
                });
                
                return data;
            } else if (response.status === 401 || response.status === 403) {
                // JWT token已过期或无效，清理本地认证状态
                console.warn('🔐 JWT token已过期，清理本地认证状态');
                await this.clearAuth();
                this.isAuthenticated = false;
                return { 
                    authenticated: false, 
                    expired: true,
                    message: 'JWT token已过期，需要重新登录' 
                };
            } else {
                console.error('❌ 认证验证失败:', response.status, response.statusText);
                this.isAuthenticated = false;
                return { authenticated: false, message: `认证验证失败: ${response.status}` };
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.error('❌ 认证检查超时');
                this.isAuthenticated = false;
                return { authenticated: false, message: '认证检查超时，请重试' };
            }
            
            console.error('❌ 认证状态检查失败:', error);
            this.isAuthenticated = false;
            return { authenticated: false, message: error.message };
        }
    }

    // 获取店铺权限和配额信息（调用capabilities Edge Function）
    async getStoreCapabilities(wbSupplierId) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        try {
            const url = `${this.supabaseUrl}/functions/v1/capabilities?wb_supplier_id=${encodeURIComponent(wbSupplierId)}`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                return await response.json();
            } else {
                const error = await response.json();
                throw new Error(error.message || '获取店铺权限失败');
            }
        } catch (error) {
            console.error('获取店铺权限失败:', error);
            throw error;
        }
    }

    // 同步WB店铺到系统（调用stores-register Edge Function）
    async syncStores(authToken, wbxValidKey) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        try {
            const response = await fetch(`${this.supabaseUrl}/functions/v1/stores-register`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    authToken: authToken,
                    wbxValidKey: wbxValidKey
                })
            });

            if (response.ok) {
                return await response.json();
            } else {
                const error = await response.json();
                throw new Error(error.message || '店铺同步失败');
            }
        } catch (error) {
            console.error('店铺同步失败:', error);
            throw error;
        }
    }

    // 绑定卡密到店铺（支持升级，调用license-upgrade Edge Function）
    async bindLicense(licenseKey, wbSupplierId) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        try {
            // 使用新的license-upgrade API，支持升级逻辑
            const response = await fetch(`${this.supabaseUrl}/functions/v1/license-upgrade`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    license_key: licenseKey,
                    wb_supplier_id: wbSupplierId
                })
            });

            if (response.ok) {
                return await response.json();
            } else {
                const error = await response.json();
                throw new Error(error.error || '卡密绑定失败');
            }
        } catch (error) {
            console.error('卡密绑定失败:', error);
            throw error;
        }
    }

    // 创建预约任务（调用tasks-create Edge Function）
    async createTask(wbSupplierId, wbPreorderId, taskConfig) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        try {
            // 1. 先检查店铺权限和配额
            const capabilities = await this.getStoreCapabilities(wbSupplierId);
            if (!capabilities.has_valid_license) {
                throw new Error('店铺未绑定有效卡密，请先绑定卡密');
            }
            
            if (capabilities.license_binding.remaining_quota <= 0) {
                throw new Error('月度配额已用完，请等待下月周期或升级套餐');
            }

            // 2. 验证taskConfig格式
            if (!taskConfig.date_range || !Array.isArray(taskConfig.date_range)) {
                throw new Error('任务配置中缺少有效的日期范围');
            }

            // 3. 验证和处理preorderId格式
            if (!wbPreorderId) {
                throw new Error('preorderId不能为空');
            }
            
            // 确保preorderId是字符串格式
            const preorderIdStr = String(wbPreorderId).trim();
            if (!preorderIdStr) {
                throw new Error('preorderId格式无效：不能为空字符串');
            }

            // 创建请求体
            const requestBody = {
                wb_supplier_id: wbSupplierId,
                wb_preorder_id: preorderIdStr,  // 确保是字符串格式
                task_config: taskConfig
            };

            console.log('🚀 创建任务请求详情:', {
                url: `${this.supabaseUrl}/functions/v1/tasks-create`,
                body: requestBody,
                preorder_id_type: typeof preorderIdStr,
                preorder_id_length: preorderIdStr.length,
                preorder_id_value: preorderIdStr,
                wb_supplier_id_type: typeof wbSupplierId,
                wb_supplier_id_value: wbSupplierId
            });

            // 4. 创建任务
            const response = await fetch(`${this.supabaseUrl}/functions/v1/tasks-create`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 任务创建成功:', {
                    task_id: result.task_id,
                    wb_preorder_id: result.wb_preorder_id,
                    wb_supply_id: result.wb_supply_id, // 初始为null
                    remaining_quota: result.remaining_quota
                });
                return result;
            } else {
                const errorText = await response.text();
                console.error('❌ 任务创建失败响应:', {
                    status: response.status,
                    statusText: response.statusText,
                    errorText: errorText
                });
                
                let errorMessage;
                let errorCode = 'UNKNOWN_ERROR';
                
                try {
                    const errorObj = JSON.parse(errorText);
                    errorCode = errorObj.code || 'UNKNOWN_ERROR';
                    errorMessage = errorObj.message || errorObj.error || '创建任务失败';
                    
                    // 🔧 针对特定错误提供友好的错误信息
                    if (errorCode === 'TASK_CREATION_FAILED' && errorText.includes('unique constraint')) {
                        if (errorText.includes('unique_user_preorder_active')) {
                            errorMessage = '该订单已存在活跃的预约任务，请勿重复创建。如需重新预约，请先取消现有任务。';
                            errorCode = 'DUPLICATE_ACTIVE_TASK';
                        } else {
                            errorMessage = '检测到重复的预约任务，请稍后重试或检查是否已有相同的预约。';
                            errorCode = 'DUPLICATE_TASK';
                        }
                    } else if (errorCode === 'PREORDER_ID_EXISTS') {
                        errorMessage = '该preorderId已存在正在进行的任务，请勿重复创建';
                    } else if (errorCode === 'QUOTA_EXCEEDED') {
                        errorMessage = '月度预约配额已用完，请等待下月周期或升级套餐';
                    } else if (errorCode === 'LICENSE_REQUIRED') {
                        errorMessage = '未绑定有效卡密或卡密已过期，请先绑定卡密';
                    } else if (errorCode === 'STORE_NOT_FOUND') {
                        errorMessage = '店铺不存在或无权限访问，请确认店铺信息';
                    }
                } catch {
                    errorMessage = errorText || '创建任务失败，请稍后重试';
                }
                
                // 🔧 创建包含错误代码的错误对象
                const error = new Error(errorMessage);
                error.code = errorCode;
                error.status = response.status;
                throw error;
            }
        } catch (error) {
            console.error('❌ 创建任务失败:', error);
            throw error;
        }
    }

    // 上报预约成功（调用tasks-report-success Edge Function）
    async reportTaskSuccess(taskId, wbSupplyId, bookingSuccessAt, bookingDetails) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        try {
            // 验证必要参数
            if (!taskId || !wbSupplyId) {
                throw new Error('缺少必要参数：taskId或supplyId');
            }

            // 验证supplyId格式（应该是预约成功页面的"送货单号: XXX"）
            if (typeof wbSupplyId !== 'string' || wbSupplyId.trim() === '') {
                throw new Error('无效的supplyId格式');
            }

            const response = await fetch(`${this.supabaseUrl}/functions/v1/tasks-report-success`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    task_id: taskId,
                    wb_supply_id: wbSupplyId,  // 预约成功页面"送货单号: XXX"的ID值
                    booking_success_at: bookingSuccessAt,
                    booking_details: bookingDetails
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('预约成功上报完成:', {
                    success: result.success,
                    quota_deducted: result.quota_deducted,
                    remaining_quota: result.remaining_quota,
                    preorder_to_supply_mapped: result.preorder_to_supply_mapped,
                    mapping_details: result.mapping_details
                });

                // 处理ID映射信息
                if (result.mapping_details) {
                    console.log('ID映射已建立:', {
                        preorderId: result.mapping_details.wb_preorder_id,
                        supplyId: result.mapping_details.wb_supply_id
                    });

                    // 触发ID映射事件，通知其他组件
                    window.dispatchEvent(new CustomEvent('wbassist-id-mapped', {
                        detail: {
                            preorderId: result.mapping_details.wb_preorder_id,
                            supplyId: result.mapping_details.wb_supply_id,
                            taskId: taskId
                        }
                    }));
                }

                return result;
            } else {
                const error = await response.json();
                throw new Error(error.message || '上报成功结果失败');
            }
        } catch (error) {
            console.error('上报成功结果失败:', error);
            throw error;
        }
    }


    // 上报预约失败（调用tasks-report-failure Edge Function）
    async reportTaskFailure(taskId, status, errorInfo) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        try {
            // 验证必要参数
            if (!taskId || !status || !errorInfo) {
                throw new Error('缺少必要参数：taskId、status或errorInfo');
            }

            // 验证status值
            if (!['failed', 'cancelled'].includes(status)) {
                throw new Error('status必须是failed或cancelled');
            }

            // 验证errorInfo格式
            if (!errorInfo.error_code || !errorInfo.error_message) {
                throw new Error('errorInfo必须包含error_code和error_message');
            }

            const response = await fetch(`${this.supabaseUrl}/functions/v1/tasks-report-failure`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    task_id: taskId,
                    status: status,
                    error_info: errorInfo
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 失败上报成功:', {
                    task_id: taskId,
                    status: status,
                    can_retry: result.can_retry
                });
                return result;
            } else {
                // 🔧 使用通用错误处理方法
                await this.handleApiError(response, '上报任务失败');
            }
        } catch (error) {
            console.error('❌ 上报失败结果失败:', error);
            throw error;
        }
    }

    // 获取任务列表（调用tasks-list Edge Function）
    async getTasks(storeId = null, status = null, page = 1, limit = 20) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        try {
            // 构建查询参数
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });

            if (storeId) {
                params.set('store_id', storeId);
            }

            if (status) {
                params.set('status', status);
            }

            const response = await fetch(`${this.supabaseUrl}/functions/v1/tasks-list?${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 获取任务列表成功:', {
                    total: result.pagination?.total || 0,
                    current_page: result.pagination?.page || 1,
                    tasks_count: result.tasks?.length || 0
                });
                return result;
            } else {
                const errorText = await response.text();
                let errorMessage;
                try {
                    const errorObj = JSON.parse(errorText);
                    errorMessage = errorObj.message || errorObj.error || '获取任务列表失败';
                } catch {
                    errorMessage = errorText || '获取任务列表失败';
                }
                throw new Error(errorMessage);
            }
        } catch (error) {
            console.error('❌ 获取任务列表失败:', error);
            throw error;
        }
    }

    // 获取配额使用情况（调用credits-usage Edge Function）
    async getCreditsUsage(storeId = null, monthPeriod = null) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        try {
            const params = new URLSearchParams();
            if (storeId) params.append('store_id', storeId);
            if (monthPeriod) params.append('month_period', monthPeriod);

            const response = await fetch(`${this.supabaseUrl}/functions/v1/credits-usage?${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                return await response.json();
            } else {
                const error = await response.json();
                throw new Error(error.message || '获取配额使用情况失败');
            }
        } catch (error) {
            console.error('获取配额使用情况失败:', error);
            throw error;
        }
    }

    // 处理来自全局监听器的消息
    handleMessage(message, sender, sendResponse) {
        console.log('🔔 API Service 处理消息:', message);
        
        // 统一处理认证成功消息（避免重复处理）
        if (message.action === 'auth_success') {
            // 防止重复处理同一个token
            if (this.lastProcessedToken && this.lastProcessedToken === message.data.access_token) {
                console.log('⚠️ 跳过重复的认证成功消息');
                sendResponse({ success: true, skipped: true });
                return true;
            }
            
            this.lastProcessedToken = message.data.access_token;
            this.handleAuthSuccess(message.data);
            sendResponse({ success: true });
            return true;
        }
        
        // 处理认证状态更新（登出等）
        if (message.action === 'auth_status_updated') {
            if (!message.authenticated) {
                console.log('🚪 收到登出消息');
                this.clearAuth();
                this.lastProcessedToken = null; // 清除token记录
                
                // 通知界面更新
                window.dispatchEvent(new CustomEvent('wbassist-auth-updated', { 
                    detail: { authenticated: false, user: null }
                }));
                
                // 如果主应用存在，直接调用更新方法
                if (window.app) {
                    window.app.showWBAssistAuthStatus(false);
                    window.app.showLoginPrompt();
                    window.app.isInitialized = false; // 重置初始化状态
                }
            }
            sendResponse({ success: true });
            return true;
        }
        
        return false; // 不处理其他消息
    }

    // 监听来自background的认证消息（现在只是标记已设置）
    setupAuthMessageListener() {
        // 防止重复监听
        if (this.messageListenerSetup) {
            console.log('⚠️ 消息监听器已设置，跳过重复设置');
            return;
        }
        this.messageListenerSetup = true;
        console.log('🔧 WBAssist认证消息监听器已通过全局监听器设置');
    }

    // 处理认证成功
    async handleAuthSuccess(authData) {
        console.log('🔐 处理认证成功数据:', authData);
        
        await this.saveAuthToStorage(authData);
        this.authToken = authData.access_token;
        this.userInfo = authData.user;
        this.isAuthenticated = true;

        console.log('✅ 认证信息已保存，用户:', this.userInfo);

        // 关闭登录/注册弹窗
        this.closeAuthPopups();

        // 通知界面更新
        window.dispatchEvent(new CustomEvent('wbassist-auth-updated', { 
            detail: { authenticated: true, user: this.userInfo }
        }));

        // 如果主应用存在，触发认证检查来重新加载数据
        if (window.app) {
            console.log('🔄 认证成功，重置初始化状态并触发完整初始化流程');
            // 重置初始化状态，确保走完整初始化流程
            window.app.isInitialized = false;
            // 让checkWBAssistAuth来处理显示界面和数据初始化
            window.app.checkWBAssistAuth();
        }
    }

    // 关闭认证相关弹窗
    closeAuthPopups() {
        if (this.loginPopup && !this.loginPopup.closed) {
            this.loginPopup.close();
            console.log('🔒 登录弹窗已自动关闭');
        }
        
        if (this.registerPopup && !this.registerPopup.closed) {
            this.registerPopup.close();
            console.log('🔒 注册弹窗已自动关闭');
        }
    }

    // 获取认证状态
    getAuthStatus() {
        return {
            isAuthenticated: this.isAuthenticated,
            user: this.userInfo,
            hasToken: !!this.authToken
        };
    }
}

// 全局消息监听器 - 立即设置，确保不错过任何消息
let globalMessageListenerSetup = false;
function setupGlobalAuthMessageListener() {
    if (globalMessageListenerSetup) {
        return;
    }
    globalMessageListenerSetup = true;
    
    console.log('🔧 设置全局WBAssist认证消息监听器（脚本加载时）');
    
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('🔔 全局监听器收到消息:', message);
            
            // 如果服务实例存在，转发给它处理
            if (window.wbAssistApiService) {
                window.wbAssistApiService.handleMessage(message, sender, sendResponse);
            } else {
                console.log('⚠️ 服务实例还未创建，消息将被缓存');
                // 可以在这里缓存消息，但现在先简单处理
            }
            
            return true; // 保持消息通道开放
        });
    }
}

// 立即设置全局监听器
setupGlobalAuthMessageListener();

// 创建全局WBAssist API服务实例
window.wbAssistApiService = new WBAssistApiService(); 