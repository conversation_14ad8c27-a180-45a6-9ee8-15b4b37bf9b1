'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { CheckCircleIcon, ClipboardDocumentIcon, CheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { ChevronRightIcon } from '@heroicons/react/20/solid';

interface OrderInfo {
  out_trade_no: string;
  product_name: string;
  amount: string;
  status: string;
  payment_method: string;
}

interface LicenseInfo {
  license_key: string;
  license_type: string;
  duration_days: number;
  monthly_quota: number;
}

interface BindInstructions {
  steps: string[];
  bind_url: string;
}

interface PaymentLicenseResponse {
  success: boolean;
  order: OrderInfo;
  license: LicenseInfo;
  bind_instructions: BindInstructions;
}

interface PaymentSuccessWithLicenseProps {
  orderId: string;
}

export default function PaymentSuccessWithLicense({ orderId }: PaymentSuccessWithLicenseProps) {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<PaymentLicenseResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchLicenseData = async () => {
      try {
        const response = await fetch(`/api/payment/license/${orderId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '获取卡密信息失败');
        }

        const licenseData: PaymentLicenseResponse = await response.json();
        setData(licenseData);
      } catch (error: any) {
        console.error('获取卡密信息失败:', error);
        setError(error.message || '获取卡密信息失败');
      } finally {
        setLoading(false);
      }
    };

    if (orderId) {
      fetchLicenseData();
    } else {
      setError('缺少订单号');
      setLoading(false);
    }
  }, [orderId]);

  const copyLicenseKey = async () => {
    if (!data?.license.license_key) return;
    
    try {
      await navigator.clipboard.writeText(data.license.license_key);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
      // 降级方案：选择文本
      const textElement = document.getElementById('license-key-text');
      if (textElement) {
        const range = document.createRange();
        range.selectNodeContents(textElement);
        const selection = window.getSelection();
        selection?.removeAllRanges();
        selection?.addRange(range);
      }
    }
  };

  const getLicenseTypeDisplayName = (type: string) => {
    switch (type) {
      case 'pro':
        return '专业版';
      case 'ultimate':
        return '旗舰版';
      case 'basic':
        return '基础版';
      default:
        return type;
    }
  };

  const getQuotaDisplayText = (quota: number) => {
    if (quota >= 999) return '无限制';
    return `${quota}次/月`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">正在获取您的卡密信息...</h1>
            <p className="text-gray-600">请稍候片刻</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-pink-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-semibold text-gray-900 mb-4">获取卡密信息失败</h1>
            <p className="text-lg text-gray-600 mb-8">{error}</p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                重试
              </button>
              <Link
                href="/dashboard"
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                进入控制台
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        {/* 成功状态头部 */}
        <div className="text-center mb-12">
          <CheckCircleIcon className="h-20 w-20 text-green-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">支付成功！</h1>
          <p className="text-xl text-gray-600">您的卡密已生成，请按照以下步骤进行绑定</p>
        </div>

        {/* 订单信息卡片 */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-8">
          <div className="px-6 py-5 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">订单详情</h2>
          </div>
          <div className="px-6 py-5">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">产品名称</p>
                <p className="text-base text-gray-900">{data.order.product_name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">支付金额</p>
                <p className="text-base text-gray-900">¥{parseFloat(data.order.amount).toFixed(2)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">订单号</p>
                <p className="text-base text-gray-900 font-mono">{data.order.out_trade_no}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">支付方式</p>
                <p className="text-base text-gray-900 capitalize">{data.order.payment_method}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 卡密信息卡片 */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-8">
          <div className="px-6 py-5 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">您的专属卡密</h2>
            <p className="text-sm text-gray-600 mt-1">请妥善保存以下卡密信息，并尽快完成绑定</p>
          </div>
          <div className="px-6 py-5">
            {/* 卡密展示区域 */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm font-medium text-gray-700">卡密代码</span>
                <span className="text-xs text-gray-500">请点击下方按钮复制</span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <p
                    id="license-key-text"
                    className="text-2xl font-mono font-bold text-gray-900 bg-white rounded-lg px-4 py-3 border-2 border-dashed border-blue-300 text-center tracking-wider"
                  >
                    {data.license.license_key}
                  </p>
                </div>
                <button
                  onClick={copyLicenseKey}
                  className={`inline-flex items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${
                    copied
                      ? 'text-green-700 bg-green-100 hover:bg-green-200 focus:ring-green-500'
                      : 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                  }`}
                >
                  {copied ? (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      已复制
                    </>
                  ) : (
                    <>
                      <ClipboardDocumentIcon className="h-4 w-4 mr-2" />
                      复制卡密
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* 卡密属性信息 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-500 mb-1">卡密类型</p>
                <p className="text-lg font-semibold text-gray-900">
                  {getLicenseTypeDisplayName(data.license.license_type)}
                </p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-500 mb-1">有效期</p>
                <p className="text-lg font-semibold text-gray-900">{data.license.duration_days}天</p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-500 mb-1">预约配额</p>
                <p className="text-lg font-semibold text-gray-900">
                  {getQuotaDisplayText(data.license.monthly_quota)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 绑定指引卡片 */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-8">
          <div className="px-6 py-5 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">绑定指引</h2>
            <p className="text-sm text-gray-600 mt-1">按照以下步骤完成卡密绑定，即可开始使用</p>
          </div>
          <div className="px-6 py-5">
            <div className="space-y-4">
              {data.bind_instructions.steps.map((step, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-1 pt-1">
                    <p className="text-base text-gray-900">{step}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link
            href={data.bind_instructions.bind_url}
            className="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            立即绑定卡密
            <ChevronRightIcon className="ml-2 h-4 w-4" />
          </Link>
          <Link
            href="/dashboard"
            className="inline-flex items-center justify-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            进入控制台
          </Link>
          <Link
            href="/"
            className="inline-flex items-center justify-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            返回首页
          </Link>
        </div>

        {/* 重要提示 */}
        <div className="mt-12 bg-amber-50 border border-amber-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-amber-500 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-amber-800 mb-1">重要提示</h3>
              <ul className="text-sm text-amber-700 space-y-1">
                <li>• 卡密可以在控制台的卡密管理中查看</li>
                <li>• 每个卡密只能绑定一个店铺，绑定后无法更改</li>
                <li>• 如有问题，请联系客服获取帮助</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 