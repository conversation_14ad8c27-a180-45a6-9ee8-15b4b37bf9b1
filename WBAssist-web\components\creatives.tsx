import Link from "next/link";
import {
  StarIcon,
  TruckIcon,
  ClockIcon,
  CurrencyDollarIcon
} from "@heroicons/react/24/solid";

// 用户成功案例数据
const successStats = [
  {
    icon: StarIcon,
    value: "99.5%",
    label: "预约成功率",
    description: "基于智能算法优化"
  },
  {
    icon: TruckIcon,
    value: "1000+",
    label: "活跃用户",
    description: "遍布全国各地"
  },
  {
    icon: ClockIcon,
    value: "24/7",
    label: "不间断监控",
    description: "全天候智能服务"
  },
  {
    icon: CurrencyDollarIcon,
    value: "50万+",
    label: "节省成本",
    description: "为用户累计节省"
  }
];

// 用户头像数据（使用渐变色代替真实头像）
const userAvatars = [
  { id: 1, bg: "bg-gradient-to-br from-blue-400 to-blue-600", initial: "张" },
  { id: 2, bg: "bg-gradient-to-br from-green-400 to-green-600", initial: "李" },
  { id: 3, bg: "bg-gradient-to-br from-purple-400 to-purple-600", initial: "王" },
  { id: 4, bg: "bg-gradient-to-br from-pink-400 to-pink-600", initial: "刘" },
  { id: 5, bg: "bg-gradient-to-br from-indigo-400 to-indigo-600", initial: "陈" },
  { id: 6, bg: "bg-gradient-to-br from-yellow-400 to-yellow-600", initial: "杨" },
  { id: 7, bg: "bg-gradient-to-br from-red-400 to-red-600", initial: "赵" },
];

export default function Creatives() {
  return (
    <section className="bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="py-12 md:py-20">
          {/* Section header */}
          <div className="max-w-3xl mx-auto text-center pb-12 md:pb-16">
            <h2 className="h2 font-cabinet-grotesk">
              加入1000+成功卖家的选择
            </h2>
            <p className="text-xl text-gray-600 mt-4">
              WBAssist已帮助众多卖家实现高效预约，大幅提升业务效率
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4 mb-16">
            {successStats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div
                  key={index}
                  className="text-center"
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <IconComponent className="w-8 h-8 text-blue-500" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-lg font-semibold text-gray-700 mb-1">
                    {stat.label}
                  </div>
                  <div className="text-sm text-gray-500">
                    {stat.description}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Community Section */}
          <div className="max-w-xl mx-auto text-center">
            <div>
              {/* 堆叠的头像组 */}
              <div className="flex items-center justify-center -space-x-3 mb-10">
                {userAvatars.map((avatar) => (
                  <div
                    key={avatar.id}
                    className={`rounded-full border-2 border-white relative w-12 h-12 md:w-16 md:h-16 flex items-center justify-center text-white font-bold ${avatar.bg}`}
                    style={{ zIndex: avatar.id * 10 }}
                  >
                    {avatar.initial}
                  </div>
                ))}
                <div className="rounded-full bg-blue-500 text-white text-xs font-bold border-2 border-white relative w-12 h-12 md:w-16 md:h-16 flex items-center justify-center" style={{ zIndex: 80 }}>
                  <span>+1K</span>
                </div>
              </div>

              <h3 className="h3 font-cabinet-grotesk text-4xl mb-4">
                专业的Wildberries预约社区
              </h3>
              <p className="text-xl text-gray-500 mb-8">
                我们的用户社区汇聚了来自全国各地的Wildberries卖家，大家在这里分享经验、交流技巧，共同提升预约成功率和业务效率。
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  className="btn text-white bg-blue-500 hover:bg-blue-600 shadow-sm"
                  href="/signin"
                >
                  立即开始使用
                </Link>
                <Link
                  className="btn text-blue-600 bg-blue-50 hover:bg-blue-100 shadow-sm"
                  href="#pricing"
                >
                  查看定价方案
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
