# WBAssist项目JWT Token有效期分析

## 📊 当前配置状态

### 🎯 Access Token（JWT）有效期
**默认时间：1小时**

项目使用Supabase默认配置，access token的有效期为：
- **标准有效期**：3600秒（1小时）
- **配置位置**：Supabase Dashboard > Authentication > Settings > JWT Expiry
- **代码中无显式配置**：使用Supabase平台默认值

### 🔄 Refresh Token有效期
**默认时间：长期有效（理论上无限期）**

- **单次使用**：每个refresh token只能使用一次
- **重用检测**：10秒内允许重复使用（处理网络问题）
- **自动刷新**：客户端库会在token过期前自动刷新

## 🔍 代码分析发现

### 1. 存储机制（background.js）
```javascript
// 认证信息存储
chrome.storage.local.set({
    wbassist_auth: {
        access_token: request.data.access_token,
        user: request.data.user,
        supabase_url: request.data.supabase_url,
        authenticated: true,
        timestamp: Date.now()  // 仅记录时间戳，无过期检查
    }
})
```

### 2. 过期检测机制（wbassist-api-service.js）
```javascript
// API调用时检测token过期
if (response.status === 401 || response.status === 403) {
    console.warn('🔐 JWT token已过期，清理本地认证状态');
    await this.clearAuth();
    return { 
        authenticated: false, 
        expired: true,
        message: 'JWT token已过期，需要重新登录' 
    };
}
```

### 3. 自动刷新机制（middleware.ts）
```javascript
// Supabase中间件自动刷新session
await supabase.auth.getUser(); // 如果过期会自动刷新
```

## ⚠️ 当前问题分析

### 🚨 缺失的安全机制

1. **无本地过期检查**
   - 插件只在API调用失败时才知道token过期
   - 缺少主动的token有效性检查

2. **无定时刷新**
   - 依赖用户操作触发刷新
   - 长时间不使用可能导致突然失效

3. **存储无过期标记**
   - `chrome.storage`中只有timestamp，无expiry字段
   - 无法预判token何时过期

## 💡 建议优化方案

### 1. 添加本地过期检查
```javascript
// 建议在API服务中添加
isTokenExpired() {
    if (!this.authToken || !this.tokenTimestamp) return true;
    
    const tokenAge = Date.now() - this.tokenTimestamp;
    const expiryTime = 55 * 60 * 1000; // 55分钟（提前5分钟）
    
    return tokenAge > expiryTime;
}
```

### 2. 主动刷新机制
```javascript
// 定期检查并刷新token
setInterval(async () => {
    if (this.isTokenExpired()) {
        await this.refreshToken();
    }
}, 5 * 60 * 1000); // 每5分钟检查
```

### 3. 增强存储结构
```javascript
{
    wbassist_auth: {
        access_token: "...",
        refresh_token: "...",
        expires_at: Date.now() + (60 * 60 * 1000), // 添加过期时间
        user: {...},
        authenticated: true,
        timestamp: Date.now()
    }
}
```

## 📈 推荐配置

### 生产环境建议
- **Access Token**: 保持1小时（安全性）
- **主动刷新**: 提前5-10分钟刷新
- **错误处理**: 优雅的重新登录流程
- **监控**: 添加token刷新失败的监控

### 开发环境选项
- 可在Supabase设置中临时延长至2-4小时
- 便于开发调试，减少频繁重新登录

## 🎯 总结

**当前状态**: 使用Supabase默认1小时有效期
**安全级别**: 中等（依赖后端检测）
**用户体验**: 良好（自动处理过期）
**改进空间**: 可添加主动刷新和本地检查机制