# WBAssist 自动预约插件系统架构蓝本

## 1. 系统架构蓝图

### 1.1 组件边界与职责

#### 客户端层
- **Chrome插件 (cnakubengbi/)**
  - 职责：Wildberries页面自动化操作、本地任务监控、用户界面
  - 边界：只能读取本地状态，所有业务逻辑裁决通过API调用后端
  - 信任级别：不可信客户端，所有操作需后端验证

- **Web网站 (WBAssist-web/)**
  - 职责：用户注册登录、卡密管理、支付处理、数据展示
  - 边界：通过Supabase Auth进行用户认证，通过Edge Functions进行业务操作
  - 信任级别：不可信客户端，敏感操作需后端验证

#### 后端层
- **Supabase Edge Functions**
  - 职责：所有业务逻辑、权限验证、次数扣除、状态管理
  - 边界：作为唯一可信计算环境，处理所有关键业务决策
  - 信任级别：完全可信，是系统的权威数据源

- **Supabase数据库**
  - 职责：持久化存储、RLS策略执行、数据关系维护
  - 边界：通过RLS确保用户只能访问自己的数据
  - 信任级别：完全可信，通过RLS实现数据隔离

### 1.2 调用方向图
```
[Chrome插件] ──API──→ [Edge Functions] ──RPC──→ [Database]
     ↑                      ↑
     │                      │
[用户操作]              [业务逻辑]
     │                      │
     ↓                      ↓
[Web网站] ────API────→ [Edge Functions]
```

### 1.3 信任边界
- **红色边界**：客户端与服务端之间 - 所有跨越此边界的请求必须验证
- **绿色边界**：Edge Functions内部 - 可信环境内的操作
- **蓝色边界**：数据库RLS层 - 用户数据隔离边界

## 2. 用户流程图

### 2.1 初始化流程
```
打开插件 → 检查登录状态 → [未登录] → 跳转网站登录 → 返回插件
         ↓ [已登录]
         检查卡密绑定状态 → [未绑定] → 卡密绑定流程 → 激活验证
         ↓ [已绑定]
         检查卡密有效性 → [已过期] → 提示续费
         ↓ [有效]
         进入主界面
```

### 2.2 任务创建流程（插件简单逻辑）
```
用户点击开始预约 → 先查询剩余次数 → 有次数则创建任务 → 启动监控 → 成功后扣减 → 失败不扣减
        ↓                  ↓                ↓           ↓           ↓           ↓
    插件界面操作      GET /me/capabilities   POST create   background.js  report-success  report-failure
        ↓                  ↓                ↓           ↓           ↓           ↓
   选择店铺和参数       验证权限和配额      创建pending任务   定时监控     配额扣减     记录失败

核心逻辑要点：
1. 权限验证：检查店铺卡密状态和剩余配额
2. 任务创建：基于preorderId创建监控任务，预留配额（但不立即扣减）
3. 监控执行：background.js轮询检查可用时段并自动预约
4. 成功上报：仅在获得supplyId时扣减配额，建立ID映射关系
5. 失败处理：任何失败情况都不扣减配额，允许重试或取消

API调用点：
- GET /me/capabilities - 验证店铺权限和配额状态
- POST /api/tasks/create - 创建基于preorderId的预约任务
- POST /api/tasks/{id}/report-success - 成功时上报supplyId并扣减配额
- POST /api/tasks/{id}/report-failure - 失败时记录原因，不扣配额
```

### 2.3 任务执行流程
```
权限验证 → 创建任务 → 后台监控 → 发现可预约时段 → 执行自动预约 → 上报结果
   ↓          ↓         ↓            ↓              ↓           ↓
[配额检查]  [API接口]  [轮询检查]    [WB API调用]   [自动化操作]  [状态同步]
   ↓          ↓         ↓            ↓              ↓           ↓
API调用点：
- GET /me/capabilities              - 获取店铺权限和配额状态
- POST /api/tasks/create            - 创建预约任务（基于preorderId）
- 插件本地：autoBookSupplyDelivery  - 调用WB预约页面自动化操作
- 插件本地：getAcceptanceCosts      - 调用WB API获取可用时段和费用
- POST /api/tasks/{id}/report-success - 预约成功时上报（含supplyId）
- POST /api/tasks/{id}/report-failure - 预约失败时上报

WB API接口调用：
- getUserSuppliers                  - 获取用户店铺列表
- getAcceptanceCosts               - 获取指定preorderId的可预约时段和费用
- 页面自动化操作                     - 模拟用户在WB预约页面的操作
```

### 2.4 预约监控与自动化详细逻辑

#### 2.4.1 监控服务启动流程
```
用户点击开始预约 → 调用API创建任务 → background.js启动监控服务
        ↓                    ↓                     ↓
  [插件界面确认]       [POST /api/tasks/create]    [startBookingMonitoring]
        ↓                    ↓                     ↓
   传递preorderId      验证权限和配额            启动定时轮询
        ↓                    ↓                     ↓
   设置监控参数        创建pending任务           随机间隔检查(5-30秒)
```

#### 2.4.2 轮询检查与条件匹配
```
1. 调用getAcceptanceCosts获取可用时段
2. 根据用户条件筛选（日期范围、最大系数、时间段）
3. 按优先级排序（免费>低系数>高系数）
4. 找到匹配时段即触发自动预约

筛选条件逻辑：
- 日期范围：严格匹配用户选择的日期
- 系数限制：coefficient <= 用户设置的最大系数
- 可用性检查：coefficient !== -1（排除不可预约日期）
- 缓冲天数：超过缓冲期自动停止监控
```

#### 2.4.3 自动预约执行机制
```
发现匹配时段 → 停止监控服务 → 打开预约页面 → 执行自动化操作
      ↓                ↓              ↓             ↓
  触发预约流程      清除定时器      后台标签页        页面脚本注入
      ↓                ↓              ↓             ↓
调用autoBookSupply   防重复预约    模拟用户操作      自动点击流程

页面自动化步骤：
1. 页面加载检查 → 2. 点击"安排交货" → 3. 等待日历出现 → 4. 解析日期选项
5. 选择最优日期 → 6. 点击"选择"按钮 → 7. 确认预约操作 → 8. 检查成功标志
```

#### 2.4.4 preorderId与supplyId的完整数据映射
```
订单界面信息 → 任务创建 → 预约执行 → 结果获取 → 数据存储 → 配额扣减
      ↓            ↓         ↓         ↓         ↓         ↓
供应单: preorderId → tasks表存储 → 页面自动化 → 送货单号supplyId → 双ID记录 → credits_usage
      ↓            ↓         ↓         ↓         ↓         ↓
[用户订单界面]   [POST create] [WB系统预约] [预约成功页面] [数据库更新] [配额扣减记录]

数据库存储映射关系：
• tasks表 - 任务完整生命周期：
  ├── wb_preorder_id: "供应单: -" 的ID值（创建时存储）  
  ├── wb_supply_id: "送货单号: 32271788" 的ID值（成功时更新）
  ├── status: pending → running → completed
  └── 建立preorderId→supplyId的转换记录

• credits_usage表 - 配额使用明细：
  ├── wb_preorder_id: 关联原始订单标识
  ├── wb_supply_id: 关联预约成功标识  
  ├── task_id: 关联对应的任务记录
  └── 两个ID并存，完整记录业务映射

插件操作流程：
1. 用户从WB订单界面选择某个"供应单: preorderId"
2. 插件POST /api/tasks/create，传递preorderId并存储到tasks.wb_preorder_id
3. background.js启动监控，基于preorderId执行预约监控
4. 预约成功后从页面获取"送货单号: supplyId"  
5. 插件POST /api/tasks/{id}/report-success传递supplyId
6. Edge Function同时更新：
   - tasks.wb_supply_id = supplyId
   - 创建credits_usage记录，包含preorderId和supplyId
7. 两个表都保存完整的ID映射，支持双向查询和追踪
```

### 2.5 取消/失败处理
```
用户取消 → 停止监控 → API调用 → 验证权限 → 不扣次数 → 更新状态
失败重试 → 记录原因 → API调用 → 验证权限 → 不扣次数 → 标记失败  
成功完成 → 获取供应ID → API调用 → 验证权限 → 扣除次数 → 更新统计

失败类型处理：
- 网络错误：可重试，不扣配额
- 时段被抢：可重试，不扣配额  
- 系数超限：停止监控，不扣配额
- 缓冲期满：自动停止，不扣配额
- 预约成功：建立ID映射，扣减配额
```

## 3. 数据模型关系

### 3.1 核心实体关系

🎯 **v4.0全局权限共享架构**：基于店铺权限全局共享策略，保持**6张核心表**设计，实现跨浏览器、跨用户的店铺权限全局共享：

```
-- 1. users (Supabase Auth自动管理)
auth.users 
├── id (uuid, primary key)
├── email (unique)
├── created_at
└── ...其他Auth字段

-- 2. stores (店铺信息表) 🎯 全局权限共享设计
stores
├── id (uuid, primary key)
├── user_id (uuid, foreign key → auth.users.id) -- 🎯 原始注册者（权限不限于此用户）
├── wb_supplier_id (text, not null, UNIQUE) -- 🎯 全局唯一：任何用户都能通过此ID使用店铺权限
├── wb_supplier_id_external (text) -- WB系统external supplier ID
├── store_name (text, not null) -- 店铺显示名称
├── inn (text) -- 店铺INN税号
├── region (text) -- 店铺地区
├── is_approved (boolean, default: true) -- 是否通过WB审核
├── is_deactivated (boolean, default: false) -- 是否被WB停用
├── wb_created_at (timestamptz) -- WB系统创建时间
├── wb_updated_at (timestamptz) -- WB系统更新时间
├── store_config (jsonb) -- 店铺相关配置
├── status (text, default: 'active') -- active/inactive/suspended
├── created_at (timestamptz)
├── updated_at (timestamptz)
├── UNIQUE(wb_supplier_id) -- 🎯 关键：全局唯一约束，实现跨用户权限共享
└── INDEX(user_id, status) -- 查询优化

-- 3. licenses (卡密池表)
licenses
├── id (uuid, primary key)
├── license_key (text, unique, not null) -- 卡密字符串
├── license_type (text, not null) -- 卡密类型：monthly/quarterly/yearly
├── duration_days (integer, not null) -- 有效天数
├── monthly_quota (integer, not null) -- 月度预约次数限制
├── created_by (text) -- 创建来源：admin/system/import
├── status (text, default: 'available') -- available/bound/expired/banned
├── created_at (timestamptz)
├── expires_at (timestamptz) -- 卡密本身的过期时间（用于批次管理）
└── metadata (jsonb) -- 卡密附加信息

-- 4. license_bindings (卡密绑定店铺记录表)
license_bindings
├── id (uuid, primary key)
├── user_id (uuid, foreign key → auth.users.id)
├── store_id (uuid, foreign key → stores.id)
├── license_id (uuid, foreign key → licenses.id)
├── activated_at (timestamptz, not null)
├── expires_at (timestamptz, not null) -- activated_at + license.duration_days
├── current_month_start (date, not null) -- 当前月度周期开始日期
├── current_month_used (integer, default: 0) -- 当前月度已用次数
├── total_used (integer, default: 0) -- 总计使用次数
├── status (text, default: 'active') -- active/expired/suspended
├── created_at (timestamptz)
├── updated_at (timestamptz)
└── UNIQUE(store_id) -- 一个店铺只能绑定一个激活卡密

-- 5. tasks (任务记录表)
tasks
├── id (uuid, primary key)
├── user_id (uuid, foreign key → auth.users.id)
├── store_id (uuid, foreign key → stores.id)
├── license_binding_id (uuid, foreign key → license_bindings.id)
├── wb_preorder_id (text) -- 订单界面"供应单: -"的ID值，任务创建时存储
├── wb_supply_id (text) -- 预约成功页面"送货单号: 32271788"的ID值，预约成功后更新
├── task_type (text, default: 'booking') -- booking/monitor
├── task_config (jsonb, not null) -- 预约参数配置
├── status (text, default: 'pending') -- pending/running/completed/failed/cancelled
├── started_at (timestamptz) -- 任务开始执行时间
├── completed_at (timestamptz) -- 任务完成时间
├── booking_success_at (timestamptz) -- 预约成功时间（用于配额扣减）
├── result_data (jsonb) -- 执行结果详情
├── error_info (jsonb) -- 错误信息
├── created_at (timestamptz)
├── updated_at (timestamptz)
└── INDEX(user_id, status), INDEX(store_id, status), INDEX(wb_preorder_id)

-- 6. credits_usage (配额使用记录表)
credits_usage
├── id (uuid, primary key)
├── user_id (uuid, foreign key → auth.users.id)
├── store_id (uuid, foreign key → stores.id)
├── license_binding_id (uuid, foreign key → license_bindings.id)
├── task_id (uuid, foreign key → tasks.id)
├── wb_preorder_id (text, not null) -- 关联的原始"供应单: -"ID
├── wb_supply_id (text) -- 关联的预约成功"送货单号: XXX"ID
├── usage_type (text, not null) -- 'booking_success' | 'manual_deduction'
├── credits_deducted (integer, default: 1) -- 扣减的配额数量
├── month_period (date, not null) -- 所属月度周期
├── booking_date (date) -- 预约的具体日期
├── booking_time_slot (text) -- 预约时段
├── created_at (timestamptz)
└── INDEX(license_binding_id, month_period), INDEX(wb_preorder_id, wb_supply_id)

-- 7. zpay_transactions (支付交易表，现有表扩展设计)
zpay_transactions
├── id (uuid, primary key) -- 现有字段
├── created_at (timestamptz) -- 现有字段
├── updated_at (timestamptz) -- 现有字段
├── user_id (uuid, foreign key → auth.users.id) -- 现有字段
├── product_id (text, not null) -- 现有字段，对应产品套餐ID
├── amount (decimal(10,2), not null) -- 现有字段，支付金额
├── currency (text, default: 'CNY') -- 现有字段
├── status (text, default: 'pending') -- 现有字段：pending/success/failed/cancelled
├── out_trade_no (text, unique) -- 现有字段，系统生成的订单号
├── trade_no (text) -- 现有字段，ZPay返回的交易号
├── payment_method (text, not null) -- 现有字段：alipay/wxpay/qqpay/free
├── notify_count (integer, default: 0) -- 现有字段，webhook通知次数
├── expires_at (timestamptz) -- 现有字段，订单过期时间
├── subscription_start (timestamptz) -- 现有字段，订阅开始时间
├── subscription_end (timestamptz) -- 现有字段，订阅结束时间
├── is_subscription (boolean, default: false) -- 现有字段
└── metadata (jsonb) -- 现有字段，扩展用于存储：
    ├── product_name (text) -- 产品名称
    ├── subscription_period (text) -- monthly/yearly
    ├── license_duration_days (integer) -- 卡密有效天数
    ├── monthly_quota (integer) -- 月度预约次数限制
    ├── auto_activated_license_id (uuid) -- 自动激活的license_id
    └── activation_status (text) -- pending/completed/failed



-- 支付产品配置映射关系（仅包含付费产品）
PRODUCT_LICENSE_MAPPING = {
  "pro-monthly": {
    "license_type": "pro", 
    "duration_days": 30,
    "monthly_quota": 3,
    "price": 59.9,
    "is_free": false,
    "auto_activate": false
  },
  "pro-yearly": {
    "license_type": "pro",
    "duration_days": 365, 
    "monthly_quota": 3,
    "price": 575,
    "is_free": false,
    "auto_activate": false
  },
  "ultimate-monthly": {
    "license_type": "ultimate",
    "duration_days": 30,
    "monthly_quota": 999, -- 实际表示无限制
    "price": 99.9,
    "is_free": false,
    "auto_activate": false
  },
  "ultimate-yearly": {
    "license_type": "ultimate",
    "duration_days": 365,
    "monthly_quota": 999,
    "price": 958,
    "is_free": false,
    "auto_activate": false
  }
}

-- 免费版系统配置（不通过支付流程）
FREE_TIER_CONFIG = {
  "license_type": "basic",
  "duration_days": 365, -- 足够长的有效期
  "monthly_quota": 1,
  "price": 0,
  "is_free": true,
  "auto_activate": true, -- 新店铺自动激活
  "activation_trigger": "store_registration"
}
```

### 3.2 关键业务约束

#### 数据完整性约束
- **🎯 店铺权限全局共享**：wb_supplier_id全局唯一，任何能WB认证此店铺的用户都能使用其权限
- **一店一密**：license_bindings表中store_id唯一
- **ID转换追踪**：tasks表记录preorder_id→supply_id转换
- **配额控制**：credits_usage表记录每次成功预约的扣减
- **月度周期**：基于activated_at计算，非自然月

#### 业务逻辑约束
- **🎯 店铺权限全局化**：wb_supplier_id在系统中有权限，任何能WB认证此店铺的用户都能用
- **🎯 用户透明化**：WBAssist用户账户只管JWT和购买，权限使用完全看WB店铺
- **🎯 跨平台共享**：不管哪个浏览器、哪个WBAssist账户，只要能WB认证店铺就能用权限
- **🎯 权限状态保持**：不转移店铺所有权，保持原始归属关系
- **🎯 配额全局共享**：任何用户都能使用店铺的配额，扣减统一到全局店铺
- **免费版自动激活**：只有数据库中不存在的店铺才创建新的免费license_binding
- **免费版配额**：每个新店铺默认1次预约机会，用完即止
- **配额扣减时机**：仅在预约成功时（获得wb_supply_id）扣减
- **付费版升级**：免费配额用尽后，用户购买付费套餐获得卡密
- **一店一密原则**：每个店铺同时只能有一个active的license_binding
- **月度重置**：付费版每月周期开始时重置current_month_used
- **ID映射维护**：插件上报wb_supply_id时更新对应任务记录

### 3.3 核心查询索引

#### 高频查询优化
```sql
-- 用户店铺权限检查
CREATE INDEX idx_license_bindings_user_store ON license_bindings(user_id, store_id, status);

-- 任务状态查询
CREATE INDEX idx_tasks_user_status ON tasks(user_id, status, created_at DESC);

-- 配额使用统计
CREATE INDEX idx_credits_usage_binding_period ON credits_usage(license_binding_id, month_period);

-- preorder_id查询（插件频繁使用）
CREATE INDEX idx_tasks_preorder_id ON tasks(wb_preorder_id) WHERE wb_preorder_id IS NOT NULL;

-- supply_id反向查询
CREATE INDEX idx_tasks_supply_id ON tasks(wb_supply_id) WHERE wb_supply_id IS NOT NULL;
```

### 3.4 与现有系统集成

#### 支付系统集成详细流程

**免费版逻辑**（新用户默认权益）：
1. **店铺注册**：用户通过插件同步WB店铺到系统
2. **自动激活**：系统为新店铺自动创建免费版license_binding
3. **免费配额**：每个店铺默认获得1次预约机会
4. **配额用尽**：使用完免费配额后，需要购买付费套餐

**付费版支付流程**：
1. **产品选择**：用户选择付费套餐（pro/ultimate + monthly/yearly）
2. **订单创建**：POST `/api/checkout/providers/zpay/url` 创建支付订单
3. **交易记录**：在zpay_transactions表中创建记录，metadata存储产品配置
4. **支付处理**：生成ZPay支付URL，用户跳转支付

**支付成功流程**（Webhook + 卡密生成）：
1. **Webhook接收**：ZPay回调 `/api/checkout/providers/zpay/webhook`
2. **状态更新**：更新zpay_transactions.status为'success'
3. **自动生成卡密**：根据product_id配置创建license记录
4. **记录关联**：在metadata中记录生成的license_id和license_key
5. **用户获取**：用户在支付成功页面看到卡密，需要手动绑定到店铺

**具体集成策略**：
- **免费版自动激活**：在stores表新增记录时，同时创建免费license_binding
- **付费版metadata字段**：
  ```json
  {
    "product_name": "WBAssist专业版月卡",
    "subscription_period": "monthly",
    "license_duration_days": 30,
    "monthly_quota": 3,
    "generated_license_id": "uuid",
    "generated_license_key": "WBPRO-2024-XXXX-XXXX",
    "license_status": "generated",
    "generation_time": "2024-09-01T12:00:00Z"
  }
  ```
- **卡密格式**：`WB{TYPE}-{YEAR}-{RAND4}-{RAND4}`（如：WBPRO-2024-A8X9-K3M7）
- **升级机制**：免费配额用尽后，用户购买付费套餐获得卡密，绑定到店铺升级权限

#### 数据流转关系图

**免费版流程**（新用户默认）：
```
[用户注册WB店铺] → [插件同步店铺] → [自动创建免费license_binding] → [开始使用]
        ↓              ↓                    ↓                    ↓
   WB卖家账户       stores-register      系统自动处理           1次预约机会
        ↓              ↓                    ↓                    ↓
   [店铺信息] → [存储到stores表] → [license_bindings表] → [可使用插件预约]
        ↓                                  ↓                    ↓
   多店铺管理                        免费版权限激活          配额用尽需要付费
```

**付费版流程**（升级套餐）：
```
[配额用尽] → [选择付费套餐] → [创建支付订单] → [ZPay支付] → [Webhook回调]
     ↓              ↓              ↓            ↓            ↓
 免费版到期        网站产品页    zpay_transactions  第三方支付   状态更新
     ↓              ↓              ↓                        ↓
[需要升级] → [产品配置] → [metadata存储] → [支付成功] → [生成卡密license]
     ↓              ↓              ↓            ↓            ↓
  用户选择        产品属性        订单详情      状态确认      licenses表
     ↓                                                    ↓
[支付成功跳转] ← [/payment/success?orderId=xxx] ← [ZPay返回]
     ↓              ↓                                    ↓
[自动重定向] → [/payment/success/[orderId]] → [显示卡密和绑定指引]
     ↓              ↓                             ↓
[用户复制卡密] → [前往插件] → [手动绑定] → [升级权限]
     ↓              ↓              ↓
  用户操作        licenses-bind   license_bindings
     ↓                            ↓
[继续使用插件预约功能] ← [权限验证通过]
```

**使用阶段流程**（完整的预约执行流程）：
```
[用户点击开始预约] → [权限验证] → [创建任务] → [启动监控] → [发现时段] → [自动预约] → [成功上报] → [扣减配额]
        ↓               ↓          ↓          ↓           ↓          ↓          ↓          ↓
   [插件界面]      [Edge Function] [tasks表]  [background.js] [WB API调用] [页面自动化] [Edge Function] [配额更新]
        ↓               ↓          ↓          ↓           ↓          ↓          ↓          ↓
   用户操作        GET /me/capabilities  POST create  监控服务启动  检查可用时段  执行预约操作  report-success  credits_usage

详细执行步骤：
1. [权限验证阶段] - GET /me/capabilities：检查店铺卡密状态和剩余配额
2. [任务创建阶段] - POST /api/tasks/create：创建基于preorderId的预约任务
3. [监控启动阶段] - background.js：启动定时轮询监控服务（5-30秒随机间隔）
4. [时段检查阶段] - getAcceptanceCosts：调用WB API获取可预约时段和费用信息
5. [条件匹配阶段] - 根据用户筛选条件（日期、时段、最大系数）筛选最优时段
6. [自动预约阶段] - autoBookSupplyDelivery：页面自动化执行预约操作
7. [结果上报阶段] - POST /api/tasks/{id}/report-success：上报成功结果和supplyId
8. [配额扣减阶段] - 更新license_bindings和创建credits_usage记录

关键的ID转换流程：
- 任务创建时：使用preorderId作为任务标识
- 预约成功后：WB系统生成supplyId替代preorderId
- 上报阶段：建立preorderId→supplyId的映射关系
- 配额扣减：仅在获得有效supplyId时执行扣减操作
```

#### 关键业务规则
- **免费版权益**：每个新注册的WB店铺自动获得1次免费预约机会
- **升级触发**：免费配额用完后，系统提示用户购买付费套餐
- **付费版卡密**：每个成功的支付订单生成唯一卡密，用户需要手动绑定
- **一店一权限**：每个店铺同时只能绑定一个有效的license（免费或付费）
- **权限覆盖**：绑定付费卡密时，自动替换免费版权限
- **多套餐支持**：同一用户可为不同店铺购买不同套餐
- **卡密有效期**：付费卡密从绑定时开始计算有效期
- **配额重置**：付费版按激活日期计算月度周期，自动重置配额
- **退款处理**：支付退款时，对应的license状态更新为'refunded'，已绑定的权限失效

## 4. WB API接口调用详情

### 4.1 插件本地WB API调用

插件通过Chrome扩展权限直接调用Wildberries API，无需经过后端代理：

#### 4.1.1 获取用户店铺列表 - getUserSuppliers
```javascript
// API端点
POST https://seller.wildberries.ru/ns/suppliers/suppliers-portal-core/suppliers

// 请求体
[
  {
    "method": "getUserSuppliers",
    "params": {},
    "id": "json-rpc_8",
    "jsonrpc": "2.0"
  }
]

// 认证方式
Headers: {
  'authorizev3': 'WB_AUTH_TOKEN',
  'Cookie': 'wbx-validation-key=XXX; x-supplier-id=XXX'
}

// 响应格式
{
  "id": "json-rpc_8",
  "result": {
    "suppliers": [
      {
        "id": "12345678",                    // 主要supplier ID
        "supplierIdExternal": "87654321",    // external supplier ID  
        "name": "我的店铺名称",
        "inn": "**********",
        "address": {"region": "Moscow"},
        "approved": true,
        "deactivated": false,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-09-01T12:00:00Z"
      }
    ]
  }
}

// 调用时机：插件初始化时同步店铺信息
// 调用位置：background.js -> loadDataUsersWBNew()
```

#### 4.1.2 获取预约时段和费用 - getAcceptanceCosts  
```javascript
// API端点
POST https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/getAcceptanceCosts

// 请求体
{
  "params": {
    "dateFrom": "2024-09-01T00:00:00Z",    // 查询开始日期
    "dateTo": "2024-12-01T23:59:59Z",      // 查询结束日期（最多60天）
    "preorderID": "**********"              // 预约单号（preorderId）
  },
  "jsonrpc": "2.0",
  "id": "json-rpc_123"
}

// 认证方式
Headers: {
  'authorizev3': 'WB_AUTH_TOKEN',
  'Cookie': 'wbx-validation-key=XXX; x-supplier-id=XXX'
}

// 响应格式
{
  "result": {
    "costs": [
      {
        "date": "2024-09-15T00:00:00Z",     // 可预约日期
        "coefficient": 0,                    // 承兑系数（0=免费，>0=收费）
        "price": 0.00                       // 价格（卢布）
      },
      {
        "date": "2024-09-16T00:00:00Z",
        "coefficient": 1.2,
        "price": 150.50
      },
      {
        "date": "2024-09-17T00:00:00Z",
        "coefficient": -1,                   // -1表示不可预约
        "price": null
      }
    ]
  }
}

// 调用时机：监控服务轮询检查可用时段
// 调用位置：background.js -> getAcceptanceCostsForMonitoring()
// 轮询频率：5-30秒随机间隔，避免被检测
```

#### 4.1.3 页面自动化预约操作
```javascript
// 操作流程
1. 打开预约页面：https://seller.wildberries.ru/supplies-management/all-supplies/supply-detail?preorderId={preorderId}&supplyId
2. 等待页面加载完成
3. 查找并点击"安排交货"按钮（Запланировать）
4. 等待日历控件出现
5. 解析可用日期和系数信息
6. 根据用户筛选条件选择最优时段
7. 模拟点击选择日期
8. 等待"选择"按钮出现并点击
9. 点击最终的"日程"确认按钮
10. 检查预约成功标志
11. 获取新生成的supplyId（从URL或页面元素）

// 关键函数
- autoBookSupplyDelivery(preorderID, optimalDate, userFilterCriteria)
- performAutoBooking() // 注入到页面执行的脚本
- extractDateInfo() // 解析日期和系数信息
- simulateHover() // 模拟鼠标悬停
- waitForElementInContainer() // 等待元素出现

// 成功检测机制
1. 脚本返回值检查：页面自动化脚本返回成功状态
2. WB系统状态验证：通过API验证preorderId状态变化
3. ID转换确认：检查是否获得新的supplyId

// 调用位置：background.js -> autoBookSupplyDelivery()
```

### 4.2 WB API与后端系统的集成

#### 4.2.1 认证信息流转
```
用户浏览器 → WB认证页面 → 获取authToken和cookies → 插件存储 → 调用WB API
     ↓                                                    ↓
后端Edge Functions ← POST /api/stores/register ← 认证信息传递 ← 插件
```

#### 4.2.2 数据同步机制
```
WB API数据 → 插件本地处理 → 标准化格式 → Edge Functions → Supabase数据库

店铺同步：getUserSuppliers → stores表
任务创建：preorderId → tasks表  
结果上报：supplyId → tasks表 + credits_usage表
```

#### 4.2.3 ID转换追踪
```
初始状态：preorderId（草稿状态）
预约执行：页面自动化操作
成功结果：supplyId（确认状态）
系统映射：preorderId → supplyId 关系维护
```

## 5. API契约表

### 5.0 接口分工说明

#### 5.0.1 接口架构原则

**网站端接口（Next.js API Routes）**：
- **用途**：支付流程、产品展示、订单管理、用户dashboard相关
- **认证方式**：Session Cookie（基于Supabase Auth）
- **调用者**：网站前端、支付回调
- **特点**：面向展示和支付，与UI紧密结合

**Edge Functions接口**：
- **用途**：业务逻辑处理、插件专用、权限验证、配额管理
- **认证方式**：JWT Bearer Token
- **调用者**：Chrome插件、移动端应用
- **特点**：纯业务逻辑，无UI依赖

#### 5.0.2 修正后的接口分工

```
网站端接口 (Next.js API Routes):
├── GET /api/products - 产品信息展示
├── POST /api/checkout/providers/zpay/url - 创建支付订单  
├── POST /api/checkout/providers/zpay/webhook - 支付回调处理
├── GET /api/payment/license/[orderId] - 获取支付后卡密
├── GET /api/me/licenses - 获取用户购买的所有卡密
└── GET /api/me/orders - 用户订单历史

Edge Functions (v4.0 全局权限共享架构 🎯):
├── GET /api/extension/auth - 插件认证验证（验证JWT token有效性）
├── GET /me/capabilities - 全局权限查询（🎯 v5：基于wb_supplier_id的全局权限查询）
├── POST /api/stores/register - 全局共享同步（🎯 v9：店铺权限全局共享，不转移所有权）
├── POST /api/licenses/bind - 卡密绑定核心（手动输入卡密绑定到店铺）
├── POST /api/tasks/create - 全局权限任务创建（🎯 v6：支持全局共享店铺权限）
├── POST /api/tasks/{id}/report-success - 全局配额扣减（🎯 v3：支持全局配额扣减）
├── POST /api/tasks/{id}/report-failure - 失败处理（记录失败原因，不扣配额）
├── GET /me/tasks - 任务列表查询（历史任务记录和状态）
├── GET /me/credits/usage - 配额统计分析（月度使用情况和详情）
├── GET /api/products - 产品信息展示（可购买的套餐列表）
├── POST /api/checkout/providers/zpay/url - 创建支付订单（ZPay支付集成）
├── GET /api/payment/license/[orderId] - 获取支付后卡密（支付成功页面展示）
└── POST /api/license-upgrade - 卡密升级（支持用户升级现有卡密等级）
```

### 5.1 插件专用API（Edge Functions）

#### GET /api/extension/auth
- **用途**：插件验证用户登录状态
- **认证**：JWT Bearer Token（Header: Authorization）
- **入参**：无（从token获取用户信息）
- **出参**：
  ```json
  {
    "user": { "id": "uuid", "email": "string" },
    "authenticated": true
  }
  ```
- **错误码**：401 (未认证), 403 (token无效)
- **调用时机**：插件启动时、定期验证

#### GET /me/capabilities
- **用途**：🎯 全局权限查询 - 基于wb_supplier_id查询全局店铺权限状态
- **认证**：JWT Bearer Token
- **入参**：wb_supplier_id (query参数)
- **🎯 v4.0核心逻辑**（全局权限查询）：
  1. **全局店铺查询**（不限user_id）：
     ```sql
     SELECT * FROM stores 
     WHERE wb_supplier_id = $1 AND status = 'active'
     ```
  2. **全局许可证查询**（基于store_id）：
     ```sql
     SELECT * FROM license_bindings 
     WHERE store_id = $1 AND status = 'active'
     ```
  3. **权限共享判断**：比较store.user_id与当前user_id，标识是否为共享访问
- **出参**：
  ```json
  {
    "store": {
      "id": "uuid",
      "wb_supplier_id": "12345678",
      "store_name": "ИП Стельмах А. Л.",
      "inn": "**********",
      "region": "Moscow",
      "is_approved": true,
      "status": "active"
    },
    "license_binding": {
      "id": "uuid",
      "license_type": "ultimate",
      "expires_at": "2024-12-31T23:59:59Z",
      "monthly_quota": 999,
      "current_month_used": 3,
      "remaining_quota": 996,
      "current_month_start": "2024-09-01",
      "status": "active"
    },
    "has_valid_license": true,
    "sharing_info": {
      "is_shared_access": true,
      "original_owner_id": "original_owner_uuid",
      "current_user_id": "current_user_uuid", 
      "can_use_quota": true,
      "sharing_type": "global_store_permission"
    },
    "quota_status": {
      "is_unlimited": true,
      "can_create_task": true
    }
  }
  ```
- **错误码**：404 (店铺或卡密未绑定), 402 (卡密已过期)
- **调用时机**：插件启动后、创建任务前
- **🎯 核心特性**：支持跨用户的全局权限查询和共享状态显示

#### POST /api/stores/register
- **用途**：🎯 全局权限共享同步 - 查询全局店铺权限，新店铺创建免费版
- **认证**：JWT Bearer Token
- **入参**：
  ```json
  {
    "authToken": "wb_auth_token_from_extension",
    "wbxValidKey": "wb_validation_key"
  }
  ```
- **🎯 v4.0核心逻辑**（全局权限共享）：
  1. 验证WB认证信息，调用WB API获取supplier列表
  2. **对每个supplier进行全局查询**（不限user_id）：
     ```sql
     SELECT * FROM stores 
     WHERE wb_supplier_id = $1 AND status = 'active'
     ```
  3. **若店铺已存在**：直接返回全局共享权限状态，**不转移所有权**
  4. **若店铺不存在**：创建新店铺并激活免费版license_binding
- **出参**：
  ```json
  {
    "success": true,
    "existing_stores": [
      {
        "id": "uuid",
        "wb_supplier_id": "12345678",
        "store_name": "ИП Стельмах А. Л.",
        "license_type": "ultimate",
        "remaining_quota": 996,
        "sync_status": "global_shared_access",
        "owner_user_id": "original_owner_uuid",
        "current_user_id": "current_user_uuid",
        "can_use_quota": true,
        "is_shared": true
      }
    ],
    "new_stores": [
      {
        "id": "uuid",
        "wb_supplier_id": "87654321",
        "store_name": "新店铺",
        "license_type": "basic",
        "remaining_quota": 1,
        "sync_status": "newly_created_with_free_license",
        "can_use_quota": true,
        "is_shared": false
      }
    ],
    "message": "已同步 4 个店铺（共享3个，新增1个）",
    "sync_strategy": "global_sharing"
  }
  ```
- **错误码**：401 (WB认证失效), 422 (WB API调用失败)
- **调用时机**：插件初始化时
- **🎯 核心特性**：实现跨浏览器、跨用户的店铺权限全局共享

#### POST /api/licenses/bind
- **用途**：用户手动输入卡密绑定到指定店铺
- **认证**：JWT Bearer Token
- **入参**：
  ```json
  {
    "license_key": "WBPRO-2024-A8X9-K3M7",
    "wb_supplier_id": "12345678"
  }
  ```
- **验证流程**：
  1. 验证license_key格式和存在性
  2. 检查卡密状态（必须是available）
  3. 验证店铺权限和一店一密约束
  4. 创建绑定关系并激活
- **出参**：
  ```json
  {
    "success": true,
    "message": "卡密绑定成功！",
    "binding": {
      "id": "uuid",
      "license_type": "pro",
      "expires_at": "2024-10-01T12:00:00Z",
      "monthly_quota": 3,
      "current_month_used": 0,
      "remaining_quota": 3
    }
  }
  ```
- **错误码**：400 (格式无效), 404 (不存在), 409 (冲突), 422 (状态异常)
- **调用时机**：用户在插件中输入卡密绑定时

#### POST /api/tasks/create
- **用途**：创建预约任务（基于订单界面的"供应单: preorderId"）
- **认证**：JWT Bearer Token  
- **数据说明**：wb_preorder_id对应WB订单界面显示的"供应单: -"字段值
- **入参**：
  ```json
  {
    "wb_supplier_id": "12345678",
    "wb_preorder_id": "**********",  // 订单界面"供应单: -"的ID值
    "task_config": {
      "date_range": ["2024-09-01", "2024-09-03"],
      "time_slots": ["09:00", "14:00"],
      "coefficient_min": 1.0,
      "buffer_days": 1
    }
  }
  ```
- **出参**：
  ```json
  {
    "task_id": "uuid",
    "wb_preorder_id": "**********",  // 已存储到tasks.wb_preorder_id
    "wb_supply_id": null,            // 预约成功后才会有值
    "status": "pending",
    "quota_reserved": true,
    "remaining_quota": 2
  }
  ```
- **数据存储**：tasks表存储wb_preorder_id，wb_supply_id初始为null
- **错误码**：402 (配额不足), 403 (卡密过期), 422 (配置无效), 409 (preorderId重复)

#### POST /api/tasks/{task_id}/report-success
- **用途**：上报预约成功结果（含"送货单号: supplyId"）
- **认证**：JWT Bearer Token
- **数据说明**：wb_supply_id对应预约成功页面的"送货单号: 32271788"字段值
- **入参**：
  ```json
  {
    "wb_supply_id": "32271788",      // 预约成功页面"送货单号: XXX"的ID值
    "booking_success_at": "2024-09-01T14:30:00Z",
    "booking_details": {
      "booked_date": "2024-09-02",
      "booked_time_slot": "09:00-12:00",
      "booking_coefficient": 1.2
    }
  }
  ```
- **出参**：
  ```json
  {
    "success": true,
    "quota_deducted": true,
    "remaining_quota": 1,
    "credits_usage_id": "uuid",
    "preorder_to_supply_mapped": true,  // 确认已建立ID映射
    "mapping_details": {
      "wb_preorder_id": "**********",   // 原始供应单ID  
      "wb_supply_id": "32271788"        // 新生成送货单号
    }
  }
  ```
- **错误码**：404 (任务不存在), 403 (无权限), 409 (重复上报), 422 (supplyId无效)

#### POST /api/tasks/{task_id}/report-failure
- **用途**：上报预约失败或取消
- **认证**：JWT Bearer Token
- **入参**：
  ```json
  {
    "status": "failed|cancelled",
    "error_info": {
      "error_code": "NO_SLOTS_AVAILABLE",
      "error_message": "选定时间段无可用配额",
      "retry_attempts": 5
    }
  }
  ```
- **出参**：
  ```json
  {
    "success": true,
    "quota_deducted": false,
    "task_status": "failed", 
    "can_retry": true
  }
  ```
- **错误码**：404 (任务不存在), 403 (无权限)

#### GET /me/tasks
- **用途**：获取用户任务列表（插件历史记录）
- **认证**：JWT Bearer Token
- **入参**：page, limit, store_id (可选), status (可选)
- **出参**：
  ```json
  {
    "tasks": [
      {
        "id": "uuid",
        "wb_preorder_id": "**********", 
        "wb_supply_id": "WB87654321",
        "status": "completed",
        "store": {
          "store_identifier": "supplier123",
          "store_name": "我的店铺"
        },
        "booking_success_at": "2024-09-01T14:30:00Z",
        "quota_deducted": true,
        "created_at": "2024-09-01T12:00:00Z"
      }
    ],
    "pagination": { "total": 50, "page": 1, "limit": 20 }
  }
  ```
- **调用时机**：插件历史记录查看

#### GET /me/credits/usage
- **用途**：获取配额使用详情
- **认证**：JWT Bearer Token
- **入参**：store_id (可选), month_period (可选，格式: "2024-09")
- **出参**：
  ```json
  {
    "current_period": {
      "month_start": "2024-09-01",
      "month_end": "2024-09-30",
      "quota_limit": 3,
      "quota_used": 1,
      "quota_remaining": 2
    },
    "usage_details": [
      {
        "date": "2024-09-01",
        "wb_preorder_id": "**********",
        "wb_supply_id": "WB87654321", 
        "booking_time_slot": "09:00-12:00",
        "credits_deducted": 1
      }
    ]
  }
  ```
- **调用时机**：用户查看配额使用情况时

### 5.2 网站端API（Next.js API Routes）

#### GET /api/products
- **用途**：获取所有可购买的产品套餐信息
- **认证**：无需认证（公开接口）
- **入参**：无
- **出参**：
  ```json
  {
    "products": {
      "pro-monthly": {
        "id": "pro-monthly",
        "name": "WBAssist专业版月卡", 
        "title": "专业版",
        "price": "59.9",
        "priceLabel": "/月",
        "license_type": "pro",
        "duration_days": 30,
        "monthly_quota": 3,
        "is_free": false,
        "popular": true,
        "features": ["每月3次预约额度", "智能缓冲天数", "自定义系数", "多时段并发", "优先客服"]
      },
      "pro-yearly": {
        "id": "pro-yearly",
        "name": "WBAssist专业版年卡",
        "title": "专业版年付",
        "price": "575",
        "priceLabel": "/年",
        "originalPrice": "719",
        "badge": "节省 20%",
        "license_type": "pro",
        "duration_days": 365,
        "monthly_quota": 3,
        "is_free": false,
        "features": ["每月3次预约额度", "智能缓冲天数", "自定义系数", "多时段并发", "优先客服", "年付专享优惠"]
      },
      "ultimate-monthly": {
        "id": "ultimate-monthly",
        "name": "WBAssist旗舰版月卡",
        "title": "旗舰版", 
        "price": "99.9",
        "priceLabel": "/月",
        "license_type": "ultimate",
        "duration_days": 30,
        "monthly_quota": 999,
        "is_free": false,
        "features": ["无限预约次数", "批量预约", "API访问", "VIP客服", "定制功能"]
      },
      "ultimate-yearly": {
        "id": "ultimate-yearly",
        "name": "WBAssist旗舰版年卡",
        "title": "旗舰版年付",
        "price": "958",
        "priceLabel": "/年",
        "originalPrice": "1199", 
        "badge": "节省 25%",
        "license_type": "ultimate",
        "duration_days": 365,
        "monthly_quota": 999,
        "is_free": false,
        "features": ["无限预约次数", "批量预约", "API访问", "VIP客服", "定制功能", "年付专享优惠"]
      }
    },
    "free_tier_info": {
      "description": "新用户每个店铺默认获得1次免费预约机会",
      "quota": 1,
      "how_to_get": "通过插件同步店铺即可自动激活免费配额"
    }
  }
  ```
- **调用时机**：网站价格页面、购买页面
- **重要说明**：免费版不在产品列表中，而是作为系统默认权益提供

#### POST /api/checkout/providers/zpay/url
- **用途**：创建支付订单并获取支付URL
- **认证**：Session Cookie
- **入参**：
  ```json
  {
    "productId": "pro-monthly",
    "productName": "WBAssist专业版月卡",
    "amount": "59.9", 
    "paymentMethod": "alipay|wxpay|qqpay",
    "isSubscription": true,
    "subscriptionPeriod": "monthly"
  }
  ```
- **处理逻辑**：
  1. 验证用户登录状态
  2. 验证产品配置和金额
  3. 创建zpay_transactions记录
  4. 生成ZPay支付URL
- **出参**：
  ```json
  {
    "url": "https://z-pay.cn/submit.php?...",
    "orderId": "20240901123456789"
  }
  ```
- **错误码**：401 (未登录), 400 (参数无效), 500 (创建失败)
- **特殊处理**：免费产品（amount="0"）直接标记为success并生成免费license

#### POST /api/checkout/providers/zpay/webhook
- **用途**：接收ZPay支付成功回调，自动生成卡密
- **认证**：ZPay签名验证
- **入参**（ZPay回调参数）：
  ```
  pid=merchant_id&name=product_name&money=59.9&
  out_trade_no=20240901123456789&trade_no=zpay_trade_123&
  trade_status=TRADE_SUCCESS&type=alipay&sign=md5_signature
  ```
- **处理逻辑**：
  1. 验证ZPay签名合法性
  2. 查找zpay_transactions记录
  3. 更新支付状态为'success'
  4. **自动生成license记录**（关键改进）
  5. 在metadata中记录生成的license_key
- **出参**：`success` 或 `fail`
- **重要说明**：不在webhook中创建license_bindings，因为用户可能还没有店铺

#### GET /api/payment/license/[orderId]
- **用途**：支付成功后获取生成的卡密信息
- **认证**：Session Cookie
- **入参**：orderId (URL路径参数)
- **出参**：
  ```json
  {
    "success": true,
    "order": {
      "out_trade_no": "20240901123456789",
      "product_name": "WBAssist专业版月卡",
      "amount": "59.9",
      "status": "success",
      "payment_method": "alipay"
    },
    "license": {
      "license_key": "WBPRO-2024-A8X9-K3M7", 
      "license_type": "pro",
      "duration_days": 30,
      "monthly_quota": 3
    },
    "bind_instructions": {
      "steps": [
        "复制上方卡密",
        "打开WBAssist插件或进入网站个人中心",
        "选择要绑定的店铺", 
        "粘贴卡密并点击绑定",
        "绑定成功后即可开始使用"
      ],
      "bind_url": "/dashboard/cards"
    }
  }
  ```
- **错误码**：401 (未登录), 404 (订单不存在), 403 (无权限), 400 (未支付)
- **调用时机**：支付成功页面加载时

#### GET /api/me/licenses
- **用途**：获取用户购买的所有卡密信息（卡密管理页面用）
- **认证**：Session Cookie
- **入参**：无
- **出参**：
  ```json
  {
    "success": true,
    "licenses": [
      {
        "order_id": "20240901123456789",
        "product_name": "WBAssist专业版月卡",
        "purchase_date": "2024-09-01T12:00:00Z",
        "purchase_amount": "59.9",
        "license_id": "uuid",
        "license_key": "WBPRO-2024-A8X9-K3M7",
        "license_type": "pro",
        "duration_days": 30,
        "monthly_quota": 3,
        "binding_id": "uuid",
        "store_name": "我的店铺",
        "activated_at": "2024-09-01T14:00:00Z",
        "expires_at": "2024-10-01T14:00:00Z",
        "current_month_used": 1,
        "status": "active"
      }
    ]
  }
  ```
- **错误码**：401 (未登录), 500 (服务器错误)
- **调用时机**：卡密管理页面加载时

#### GET /api/me/orders  
- **用途**：获取用户购买历史和订单状态（网站dashboard用）
- **认证**：Session Cookie
- **入参**：page, limit, status (可选)
- **出参**：
  ```json
  {
    "orders": [
      {
        "id": "uuid",
        "out_trade_no": "20240901123456789",
        "product_name": "WBAssist专业版月卡",
        "amount": "59.9", 
        "status": "success",
        "payment_method": "alipay",
        "created_at": "2024-09-01T12:00:00Z",
        "license": {
          "id": "uuid",
          "status": "bound|available",
          "expires_at": "2024-10-01T12:00:00Z",
          "bound_to_store": "supplier123"
        }
      }
    ],
    "pagination": {"total": 10, "page": 1, "limit": 20}
  }
  ```
- **调用时机**：网站dashboard订单历史页面

## 6. 安全与RLS清单

### 6.1 Row Level Security 策略

基于6张核心表的RLS安全策略：

#### stores 表
- **SELECT策略**：用户只能查看自己的店铺
  ```sql
  auth.uid() = user_id
  ```
- **INSERT策略**：用户只能为自己创建店铺
  ```sql
  auth.uid() = user_id
  ```
- **UPDATE策略**：用户只能更新自己的店铺（仅限特定字段）
  ```sql
  auth.uid() = user_id
  ```

#### licenses 表
- **SELECT/INSERT/UPDATE/DELETE策略**：完全禁止直接访问
  ```sql
  false  -- 只允许Edge Functions通过service_role访问
  ```

#### license_bindings 表
- **SELECT策略**：用户只能查看自己的卡密绑定
  ```sql
  auth.uid() = user_id
  ```
- **INSERT策略**：用户只能为自己的店铺绑定卡密（通过Edge Functions）
  ```sql
  auth.uid() = user_id AND EXISTS (
    SELECT 1 FROM stores 
    WHERE stores.id = license_bindings.store_id 
    AND stores.user_id = auth.uid()
  )
  ```
- **UPDATE策略**：禁止直接更新，只允许通过Edge Functions

#### tasks 表  
- **SELECT策略**：用户只能查看自己的任务
  ```sql
  auth.uid() = user_id
  ```
- **INSERT策略**：用户只能为自己的店铺创建任务
  ```sql
  auth.uid() = user_id AND EXISTS (
    SELECT 1 FROM stores 
    WHERE stores.id = tasks.store_id 
    AND stores.user_id = auth.uid()
  )
  ```
- **UPDATE策略**：禁止直接更新，只允许通过Edge Functions

#### credits_usage 表
- **SELECT策略**：用户只能查看自己的配额使用记录
  ```sql
  auth.uid() = user_id
  ```
- **INSERT/UPDATE/DELETE策略**：禁止直接操作
  ```sql
  false  -- 只允许Edge Functions操作
  ```

#### zpay_transactions 表（现有表）
- **现有RLS策略保持不变**
- **关联验证**：Edge Functions需验证payment与license的关联

### 6.2 Edge Functions 验证清单

#### 权限验证顺序（升级版）
1. **身份验证**：验证JWT token有效性和用户状态
2. **店铺权限**：验证操作的店铺属于当前用户
3. **卡密状态**：检查店铺绑定的卡密是否有效且未过期
4. **配额验证**：检查月度剩余配额是否充足
5. **任务验证**：验证preorderId唯一性和任务参数有效性
6. **业务逻辑**：具体功能的业务规则校验

#### API验证矩阵

| API接口 | 身份验证 | 店铺权限 | 卡密状态 | 配额验证 | 业务验证 |
|---------|---------|---------|---------|---------|---------|
| GET /me/capabilities | ✓ | ✓ | ✓ | ✓ | - |
| POST /api/stores/register | ✓ | - | - | - | ✓ |
| POST /api/licenses/bind | ✓ | ✓ | - | - | ✓ |
| POST /api/tasks/create | ✓ | ✓ | ✓ | ✓ | ✓ |
| POST /api/tasks/{id}/report-success | ✓ | ✓ | ✓ | - | ✓ |
| POST /api/tasks/{id}/report-failure | ✓ | ✓ | - | - | ✓ |

#### 关键验证逻辑

**preorderId验证**：
```sql
-- 确保preorderId在用户的所有任务中唯一
SELECT COUNT(*) = 0 FROM tasks 
WHERE user_id = $1 AND wb_preorder_id = $2 
AND status IN ('pending', 'running')
```

**配额充足性验证**：
```sql
-- 检查当前月度剩余配额
SELECT (lb.monthly_quota - lb.current_month_used) > 0
FROM license_bindings lb
WHERE lb.user_id = $1 AND lb.store_id = $2 
AND lb.status = 'active' AND lb.expires_at > NOW()
```

**supplyId映射验证**：
```sql
-- 确保supplyId与preorderId的映射关系正确
SELECT wb_preorder_id FROM tasks 
WHERE id = $1 AND user_id = $2 AND status = 'running'
```

#### 常见拒绝场景（扩展版）
- **401 未认证**：JWT token缺失、无效或已过期
- **403 权限不足**：操作的店铺不属于当前用户
- **402 付费需求**：卡密已过期或月度配额已用完
- **404 资源不存在**：店铺、任务或绑定记录不存在
- **409 冲突状态**：preorderId重复、重复上报成功结果
- **422 参数无效**：无效的店铺标识、配置参数格式错误
- **429 频率限制**：单用户任务创建频率过高

### 6.3 数据边界控制

#### 读权限边界
- **用户数据隔离**：所有表通过user_id实现严格的用户数据隔离
- **店铺数据隔离**：通过stores表的外键关联确保跨店铺数据不可见
- **敏感数据保护**：licenses表完全隔离，卡密原始数据不暴露

#### 写权限边界
- **任务生命周期**：用户只能创建任务，状态更新只能通过Edge Functions
- **配额操作**：配额扣减和重置只能通过Edge Functions的原子操作
- **ID映射维护**：preorderId→supplyId转换只能通过report-success接口

#### 审计与监控边界
- **关键操作日志**：所有配额扣减操作记录在credits_usage表
- **状态变更追踪**：任务状态变更通过updated_at时间戳追踪
- **异常行为检测**：通过API调用频率和模式检测异常行为

## 7. 实施计划

基于6表结构和preorderId→supplyId转换机制的分阶段实施计划：

### 7.1 Phase 1: 数据库Schema与基础认证 (Week 1-2)

#### 里程碑 1.1: 数据库Schema建立
- **交付物**：6张核心表的DDL脚本 + RLS策略
- **验收标准**：
  - stores, licenses, license_bindings, tasks, credits_usage 表创建完成
  - RLS策略正确配置并测试通过
  - 外键约束和唯一性约束生效
  - 必要索引创建完成，查询性能测试通过

#### 里程碑 1.2: 用户认证打通
- **交付物**：GET /api/extension/auth 接口
- **验收标准**：
  - 插件能够验证用户登录状态
  - JWT token正确传递和验证
  - Supabase Auth集成正常工作

### 7.2 Phase 2: 店铺管理与卡密系统 (Week 2-3)

#### 里程碑 2.1: 店铺注册与管理
- **交付物**：POST /api/stores/register 接口
- **验收标准**：
  - 用户能够注册和管理多个店铺
  - 店铺标识符唯一性验证正确
  - 店铺权限验证机制工作正常

#### 里程碑 2.2: 免费版激活系统
- **交付物**：POST /api/free/activate 接口 + 前端"开始使用"按钮
- **验收标准**：
  - 用户点击免费版"开始使用"直接获得免费license
  - 免费版用户跳转到dashboard页面
  - 防重复激活机制正常工作
  - 引导用户绑定店铺的UI流程

#### 里程碑 2.3: 卡密绑定系统  
- **交付物**：POST /api/licenses/bind + GET /me/capabilities 接口
- **验收标准**：
  - 用户能够手动输入卡密绑定到指定店铺
  - 一店铺一卡密约束正确执行
  - 卡密格式验证和状态校验正确
  - 卡密绑定成功后权限立即生效
  - 插件能够获取店铺权限和配额信息

### 7.3 Phase 3: 任务流程核心功能 (Week 3-4)

#### 里程碑 3.1: 任务创建与preorderId处理
- **交付物**：POST /api/tasks/create 接口
- **验收标准**：
  - 插件能够基于preorderId创建预约任务
  - preorderId唯一性验证正确
  - 配额预检验证和锁定机制工作
  - 任务配置参数验证完整

#### 里程碑 3.2: 预约成功上报与supplyId映射
- **交付物**：POST /api/tasks/{id}/report-success 接口
- **验收标准**：
  - 插件能够上报预约成功结果和supplyId
  - preorderId→supplyId映射关系正确建立
  - 配额扣减机制精确执行（仅成功时扣减）
  - credits_usage记录创建正确

#### 里程碑 3.3: 失败处理与任务管理
- **交付物**：POST /api/tasks/{id}/report-failure + GET /me/tasks 接口
- **验收标准**：
  - 预约失败和取消时不扣减配额
  - 任务状态正确更新和追踪
  - 用户能够查看完整的任务历史
  - 任务列表支持分页和筛选

### 7.4 Phase 4: 配额统计与支付集成 (Week 4-5)

#### 里程碑 4.1: 配额统计与月度重置
- **交付物**：GET /me/credits/usage 接口 + 月度重置Edge Function
- **验收标准**：
  - 用户能够查看详细的配额使用情况
  - 月度周期计算正确（基于激活日期）
  - 月度配额自动重置机制正常工作
  - 历史使用记录完整保留

#### 里程碑 4.2: 支付系统集成与卡密生成
- **交付物**：zpay_transactions与license系统的集成 + 卡密生成逻辑
- **验收标准**：
  - 支付成功后自动生成对应卡密
  - 卡密格式符合规范（WBPRO-2024-XXXX-XXXX）
  - 支付webhook正确处理并生成license记录
  - metadata中正确记录卡密信息

#### 里程碑 4.3: 支付成功页面与卡密展示
- **交付物**：GET /api/payment/license/{orderId} 接口 + 双路由支付成功页面 + GET /api/me/licenses 接口
- **验收标准**：
  - 支付平台返回到 `/payment/success?orderId=xxx` 自动跳转到卡密展示页
  - 卡密展示页面 `/payment/success/[orderId]` 正确展示生成的卡密
  - 提供卡密复制功能和详细绑定指引
  - 卡密管理页面显示用户所有购买的卡密（包含购买日期、激活状态等）
  - 防止未支付用户访问卡密信息
  - 用户能够顺利从支付成功跳转到绑定流程

### 7.5 Phase 5: 生产优化与监控 (Week 5-6)

#### 里程碑 5.1: 性能优化与并发处理
- **交付物**：数据库查询优化 + 并发控制机制
- **验收标准**：
  - API响应时间 < 300ms (P95)
  - 高频查询索引优化完成
  - preorderId冲突检测机制健壮
  - 并发预约请求处理正确

#### 里程碑 5.2: 监控告警与审计日志
- **交付物**：监控Dashboard + 异常告警系统
- **验收标准**：
  - 关键业务指标监控覆盖完整
  - 配额异常和滥用行为自动告警
  - 用户操作审计日志完整可追溯
  - Edge Functions错误监控和告警

### 7.6 验收测试清单

#### 端到端功能测试
1. **免费版用户完整流程**：注册→点击"开始使用"→直接激活→绑定店铺→创建任务→使用配额
2. **付费版用户完整流程**：注册→选择套餐→支付→获得卡密→绑定店铺→创建任务→上报结果→查看统计
3. **卡密发放与绑定流程**：支付成功→展示卡密→复制卡密→输入绑定→权限激活→开始使用
4. **多店铺场景**：用户管理多个店铺，每个店铺独立的卡密和配额
5. **多套餐购买**：用户购买多个不同套餐，每个生成独立卡密
6. **并发任务处理**：同一用户多个任务并行执行和上报
7. **异常场景处理**：网络中断、重复请求、无效数据处理
8. **卡密状态管理**：卡密绑定后状态变更、重复绑定阻止、过期处理

#### 安全性测试
1. **权限隔离**：确保用户无法访问其他用户的数据
2. **API攻击防护**：SQL注入、XSS、CSRF防护测试
3. **配额绕过测试**：尝试绕过配额限制的各种方法
4. **数据完整性**：并发操作下数据一致性验证

#### 性能与稳定性测试
1. **负载测试**：模拟高并发用户同时使用系统
2. **压力测试**：系统在极限负载下的表现
3. **容错测试**：数据库连接失败、第三方服务不可用等场景

## 8. 店铺所有权冲突修复方案 🔧

### 8.1 问题根本原因分析

#### 8.1.1 发现的严重逻辑漏洞
- **重复店铺创建**：同一个WB店铺被多个用户账户重复注册
- **配额白嫖漏洞**：用户可通过注册新账户无限获取免费配额
- **权限状态不一致**：相同店铺在不同账户下权限等级不同步
- **缺乏全局约束**：数据库只有`UNIQUE(user_id, wb_supplier_id)`，缺少全局唯一性

#### 8.1.2 影响范围评估
- **数据完整性**：系统中存在4个店铺的重复记录
- **业务逻辑**：用户可通过技术手段绕过付费机制
- **用户体验**：多设备使用时店铺权限不一致
- **商业模式**：免费配额被恶意利用，影响收入

### 8.2 修复策略与实施

#### 8.2.1 High Priority 立即修复（已完成）

**1. 数据库Schema修复**
- ✅ 添加`UNIQUE(wb_supplier_id)`全局唯一约束
- ✅ 清理现有重复数据（保留原用户记录，删除新用户重复记录）
- ✅ 新增所有权验证字段：`ownership_verified`, `verification_method`, `ownership_notes`
- ✅ 创建冲突记录表：`store_ownership_conflicts`

**2. 店铺注册逻辑修复**
- ✅ 实现全局冲突检测机制
- ✅ 添加所有权验证流程
- ✅ 自动记录所有权冲突到专用表
- ✅ 防止重复注册的拦截逻辑

**3. 权限验证API修复**
- ✅ `/me/capabilities`接口增加所有权验证检查
- ✅ 返回冲突状态和解决方案引导
- ✅ 增强安全性和错误处理

#### 8.2.2 Medium Priority 中期改进（已完成）

**4. 所有权冲突管理系统**
- ✅ 创建`/api/ownership-management`系列接口
- ✅ 用户提交所有权证据功能
- ✅ 管理员审核和解决冲突功能
- ✅ 店铺转移和权限继承机制

**5. 多设备管理功能**
- ✅ 创建`user_devices`和`device_store_sync`表
- ✅ 实现设备指纹识别和注册
- ✅ 创建`/api/device-management`系列接口
- ✅ 设备间店铺同步状态追踪

**6. 云端数据优先同步策略**
- ✅ 修改`/api/stores/register`接口实现云端数据优先
- ✅ 新设备登录时继承云端已有店铺权限
- ✅ 设备间权限状态一致性保证
- ✅ 本地店铺与云端店铺的智能匹配

### 8.3 技术实现亮点

#### 8.3.1 云端数据优先策略
```javascript
// 核心逻辑：新设备同步时优先使用云端数据
async function syncStoresWithCloudPriority(adminClient, userId, localSuppliers, deviceId) {
  // 1. 获取用户云端店铺数据（优先级最高）
  const cloudStores = await getCloudStores(userId);
  
  // 2. 对本地店铺：优先使用云端数据
  for (const supplier of localSuppliers) {
    const cloudStore = cloudStores.find(cs => cs.wb_supplier_id === supplier.id);
    
    if (cloudStore) {
      // 🔧 云端数据优先：直接继承云端权限和配置
      await inheritCloudStoreConfig(cloudStore, supplier);
    } else {
      // 新店铺：检查全局冲突并创建
      await createNewStoreWithConflictCheck(supplier);
    }
  }
}
```

#### 8.3.2 所有权冲突检测机制
```javascript
// 全局冲突检测：确保wb_supplier_id唯一性
const globalExistingStore = await supabase
  .from('stores')
  .select('*')
  .eq('wb_supplier_id', supplier.id)
  .single();

if (globalExistingStore && globalExistingStore.user_id !== currentUserId) {
  // 自动记录冲突并阻止重复注册
  await recordOwnershipConflict(globalExistingStore.user_id, currentUserId);
  return { conflict: true };
}
```

#### 8.3.3 设备管理与同步
```javascript
// 设备注册与店铺同步状态追踪
await supabase.from('device_store_sync').upsert({
  device_id: deviceId,
  store_id: storeId,
  wb_supplier_id: supplierId,
  sync_status: 'synced',
  local_available: true,
  cloud_available: true,
  sync_details: {
    inherited_from_cloud: true,
    license_type: licenseType,
    remaining_quota: quota
  }
});
```

### 8.4 修复效果验证

#### 8.4.1 数据完整性验证
- ✅ stores表中wb_supplier_id全局唯一
- ✅ 重复数据已清理（从8条记录减少到4条）
- ✅ license_bindings对应关系正确
- ✅ 全局唯一约束生效

#### 8.4.2 业务逻辑验证
- ✅ 新用户无法注册已存在的店铺
- ✅ 所有权冲突自动检测并记录
- ✅ 多设备登录权限一致性
- ✅ 配额白嫖漏洞已封堵

#### 8.4.3 用户体验验证
- ✅ 跨设备店铺权限自动继承
- ✅ 冲突情况有明确的解决引导
- ✅ 设备管理界面完善
- ✅ 错误提示更加友好

### 8.5 长期价值

#### 8.5.1 技术架构优化
- **数据一致性**：全局唯一约束确保数据完整性
- **扩展性**：设备管理系统支持未来多端扩展
- **安全性**：所有权验证机制防止恶意利用
- **可维护性**：冲突管理系统便于运营处理

#### 8.5.2 商业模式保护
- **防止滥用**：封堵配额白嫖漏洞，保护收入
- **用户体验**：多设备一致性提升用户满意度
- **运营效率**：自动化冲突检测减少人工处理
- **数据价值**：准确的用户店铺关系数据

## 9. 风险与回滚策略

### 8.1 技术风险

#### 8.1.1 preorderId→supplyId转换失败
- **风险描述**：插件获取supplyId失败或上报时网络中断，导致映射关系丢失
- **缓解策略**：
  - 任务状态保持为'running'，允许重试上报
  - 插件本地存储supplyId，支持离线后重新同步
  - Edge Function提供补偿性API，手动建立映射关系
- **回滚方案**：基于时间窗口和插件日志恢复映射关系，必要时人工介入核实

#### 8.1.2 配额扣减一致性问题
- **风险描述**：并发请求导致配额计算错误或重复扣减
- **缓解策略**：
  - 数据库事务保证原子性操作
  - credits_usage表记录每次扣减的详细信息
  - 实现幂等性机制，相同supplyId不重复扣减
- **回滚方案**：基于credits_usage审计日志重建正确的配额状态

#### 8.1.3 月度周期边界处理
- **风险描述**：月度重置时机计算错误或跨月任务处理异常
- **缓解策略**：
  - 基于activated_at精确计算月度开始日期
  - 跨月任务按创建时间归属月度周期
  - 提供月度重置的手动触发和校验机制
- **回滚方案**：保留历史月度数据，支持重新计算和调整

#### 8.1.4 数据库连接与性能风险  
- **风险描述**：高并发下数据库连接池耗尽或查询性能下降
- **缓解策略**：
  - 实施连接池监控和自动扩容
  - 关键查询路径添加缓存层
  - 数据库读写分离和查询优化
- **回滚方案**：降级机制，暂停非关键功能，优先保证核心业务

### 8.2 业务风险

#### 8.2.1 插件与网站状态不一致
- **风险描述**：用户在多端操作导致数据状态不同步
- **缓解策略**：
  - 以Edge Functions中的数据库状态为权威源
  - 插件定期同步最新状态
  - 关键操作前强制状态校验
- **回滚方案**：提供状态修复API，强制同步到权威状态

#### 8.2.2 恶意刷单与滥用防护
- **风险描述**：用户通过技术手段绕过配额限制或滥用系统
- **缓解策略**：
  - 基于用户ID和IP的多维度频率限制
  - preorderId格式验证和WB系统一致性检查
  - 异常行为模式检测和自动告警
- **回滚方案**：账户临时封禁，人工审核，必要时退款处理

#### 8.2.3 支付与卡密激活异步问题
- **风险描述**：支付成功但卡密激活失败，或激活延迟导致用户体验差
- **缓解策略**：
  - 支付webhook重试机制和状态监控
  - 卡密激活失败时自动补偿和通知
  - 提供手动激活的管理接口
- **回滚方案**：基于zpay_transactions记录手动激活，记录补偿操作

### 8.3 数据风险

#### 8.3.1 敏感数据泄露风险
- **风险描述**：卡密原文、用户预约详情等敏感数据泄露
- **缓解策略**：
  - licenses表完全隔离，RLS策略禁止直接访问
  - API响应中不包含完整卡密信息，仅返回状态
  - 关键字段加密存储，Edge Functions中解密
- **回滚方案**：立即吊销泄露卡密，通知受影响用户，提供替代方案

#### 8.3.2 数据完整性破坏
- **风险描述**：外键关系破坏、孤立记录或数据不一致
- **缓解策略**：
  - 严格的外键约束和数据库触发器
  - 定期数据一致性检查脚本
  - 关键操作的事务完整性保证
- **回滚方案**：基于audit日志和backup恢复一致性，必要时冻结相关功能

#### 8.3.3 历史数据丢失风险
- **风险描述**：系统升级或故障导致用户历史任务和配额记录丢失
- **缓解策略**：
  - 软删除策略，保留所有历史记录
  - 多层次数据备份和异地容灾
  - 数据迁移前的完整性验证
- **回滚方案**：从备份恢复，按时间点恢复到最近一致性状态

### 8.4 运营风险

#### 8.4.1 WB平台政策变更
- **风险描述**：WB调整API接口或预约机制，导致插件失效
- **缓解策略**：
  - 监控WB平台变更公告和API文档
  - 插件适配层设计，降低直接依赖
  - 快速响应机制和版本发布流程
- **回滚方案**：暂停服务，通知用户，提供补偿方案

#### 8.4.2 法律合规风险  
- **风险描述**：自动化操作涉及的合规性和用户协议问题
- **缓解策略**：
  - 明确的用户协议和免责声明
  - 用户操作日志完整记录和可追溯
  - 与法务部门定期审查合规性
- **回滚方案**：配合调查，提供完整审计材料，必要时停止服务

### 8.5 回滚准备清单

#### 8.5.1 数据备份与恢复
- **实时备份**：关键表的CDC备份，RTO < 15分钟
- **定期快照**：每日完整数据库备份，保留30天
- **跨区域备份**：异地灾备，防止单点故障
- **恢复演练**：月度恢复演练，验证备份有效性

#### 8.5.2 API版本管理
- **向后兼容**：新API版本保持对旧版本的兼容性
- **渐进发布**：金丝雀发布，逐步扩大用户群体
- **快速回滚**：一键回滚到前一稳定版本的能力
- **版本监控**：实时监控各版本的使用情况和错误率

#### 8.5.3 监控告警体系
- **业务指标监控**：
  - 任务创建/成功/失败率
  - 配额使用异常告警
  - API响应时间和错误率
  - 用户活跃度和留存率

- **技术指标监控**：
  - 数据库连接池使用率
  - Edge Functions执行时间和内存使用
  - 缓存命中率和存储使用
  - 网络延迟和可用性

- **告警响应机制**：
  - 分级告警（Info/Warning/Error/Critical）
  - 多渠道通知（短信/邮件/企业微信）
  - 自动扩容和降级机制
  - 24x7运维值班制度

#### 8.5.4 应急预案
- **服务降级预案**：关键功能优先，非核心功能可临时关闭
- **用户沟通预案**：故障通知模板，补偿方案标准
- **数据修复预案**：常见数据问题的标准修复流程
- **安全事件预案**：数据泄露应急响应流程

---

## 🔧 全局权限共享完成总结

### 最终架构成果
**本次重构实现了店铺权限全局共享策略**，保持6表核心架构，实现跨浏览器、跨用户的店铺权限全局共享。

### 关键突破点
1. **店铺权限全局化** - wb_supplier_id在系统中有权限，任何能WB认证此店铺的用户都能用
2. **用户透明化** - WBAssist用户账户只管JWT和购买，权限使用完全看WB店铺
3. **跨平台共享** - 不管哪个浏览器、哪个WBAssist账户，只要能WB认证店铺就能用权限
4. **权限状态保持** - 不转移店铺所有权，保持原始归属关系
5. **配额全局共享** - 任何用户都能使用店铺的配额，扣减统一到全局店铺

### 核心实现策略
- ✅ **全局店铺查询** - 所有接口基于wb_supplier_id进行全局查询，不限制user_id
- ✅ **权限状态保持** - 不转移店铺所有权，保持原始归属关系
- ✅ **配额全局共享** - 任何用户都能使用店铺的配额，扣减统一到全局店铺
- ✅ **用户行为记录** - 记录实际操作用户，但不影响权限判断
- ✅ **跨用户协作** - 支持团队成员共享店铺权限

### 实际Edge Functions状态
- ✅ `stores-register` v9 - 全局权限共享同步 
- ✅ `capabilities` v5 - 全局权限查询
- ✅ `tasks-create` v6 - 支持全局共享权限
- ✅ `tasks-report-success` v3 - 支持全局配额扣减
- ✅ `licenses-bind` - 卡密绑定核心
- ✅ `tasks-report-failure` - 失败处理
- ✅ `tasks-list` - 任务列表查询
- ✅ `credits-usage` - 配额统计分析

---

*本架构蓝本基于店铺权限全局共享策略和preorderId→supplyId转换机制设计，为"Wildberries自动预约插件+网站+Supabase后端"项目提供跨平台权限共享的实施指导。最终架构实现了真正的"店铺中心化权限管理"，用户只是认证和购买的载体。*

**最终版本**: v4.0 - 全局权限共享版  
**重构日期**: 2025年1月  
**架构状态**: ✅ 生产就绪 - 全局权限共享  

---

*此文档作为"Wildberries 自动预约插件 + 网站 + Supabase 后端"项目的权威架构蓝本，所有技术实现必须严格遵循此修复后的架构设计。*