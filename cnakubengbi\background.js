// 后台脚本 - 处理扩展图标点击事件和认证管理

// ================================
// 🔧 重要修复：店铺特定API上下文隔离 (2025.01.26)
// ================================
// 问题：多店铺环境下，监控任务创建后如果切换店铺，会导致API认证冲突
// 原因：监控系统使用当前活跃店铺的认证上下文，而不是任务创建时的店铺上下文
// 解决方案：
// 1. 在创建监控任务时，捕获并保存完整的店铺认证上下文（token、cookies、店铺ID等）
// 2. 每个监控任务使用其专属的认证上下文进行API调用，不受店铺切换影响
// 3. 添加认证上下文验证机制，包括时效性检查和错误处理
// 4. 确保多店铺可以同时运行监控任务而不相互干扰
// 修复函数：
// - captureCurrentStoreAuthContext(): 捕获当前店铺认证上下文
// - validateStoreAuthContext(): 验证认证上下文有效性
// - getAcceptanceCostsForMonitoring(): 使用任务特定认证上下文调用API
// - startBookingMonitoring(): 在启动时保存认证上下文
// - performMonitoringCheck(): 使用任务专属认证上下文

// ================================
// WB API调用函数 - 修复getAllOrdersFromWBNewAPI未定义问题
// ================================

// 全局函数定义 - 获取WB订单数据
async function getAllOrdersFromWBNewAPI() {
    try {
        console.log('🔄 [getAllOrdersFromWBNewAPI] 开始获取WB订单数据...');
        
        // 获取认证数据
        const authData = await getStorage(['Authorizev3', 'wbxValidKey', 'supplierIds']);
        if (!authData.Authorizev3 || !authData.wbxValidKey) {
            throw new Error('缺少认证信息');
        }

        // 获取当前店铺ID
        let currentSupplierId = null;
        if (authData.supplierIds && authData.supplierIds.length > 0) {
            currentSupplierId = authData.supplierIds[0]; // 使用第一个店铺ID
        }

        // 构建Cookie字符串
        let fullCookieString = `external-locale=zh; locale=zh; ${authData.wbxValidKey}`;
        if (currentSupplierId) {
            fullCookieString += `; x-supplier-id=${currentSupplierId}; x-supplier-id-external=${currentSupplierId}`;
        }

        // 构建请求数据
        const requestData = {
            method: "listSupplies",
            params: {
                pageNumber: 1,
                pageSize: 100,  // 获取更多数据
                sortBy: "createDate",
                sortDirection: "desc",
                statusId: -2  // 获取所有状态的订单
            },
            jsonrpc: "2.0",
            id: `json-rpc_supplies_${Date.now()}`
        };

        console.log('📤 [getAllOrdersFromWBNewAPI] 发送API请求:', requestData);

        // 调用WB API
        const response = await fetch('https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/listSupplies', {
            method: 'POST',
            headers: {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'authorizev3': authData.Authorizev3,
                'content-type': 'application/json',
                'cookie': fullCookieString,
                'origin': 'https://seller.wildberries.ru',
                'referer': 'https://seller.wildberries.ru/',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.error) {
            throw new Error(`API错误: ${result.error.message || result.error}`);
        }

        const orders = result.result?.data || [];
        console.log(`✅ [getAllOrdersFromWBNewAPI] 成功获取 ${orders.length} 个订单`);
        
        return orders;

    } catch (error) {
        console.error('❌ [getAllOrdersFromWBNewAPI] 获取订单数据失败:', error);
        return [];
    }
}

// 智能转换检测函数
function detectOrderConversion(preBookingOrder, postBookingOrders, preorderID) {
    console.log(`🔍 [detectOrderConversion] 开始检测订单转换 [${preorderID}]`);
    console.log('📊 预约前订单:', preBookingOrder);
    console.log('📊 预约后订单数量:', postBookingOrders.length);

    // 策略1: 直接匹配（如果状态还未完全转换）
    let matchedOrder = postBookingOrders.find(order => 
        order.preorderId && order.preorderId.toString() === preorderID.toString()
    );
    
    if (matchedOrder) {
        return {
            found: true,
            order: matchedOrder,
            conversionStatus: matchedOrder.supplyId ? 'converted' : 'same',
            matchMethod: 'direct_preorder_id'
        };
    }

    // 策略2: 通过特征匹配查找转换后的订单（增强版）
    const bookingTime = new Date();
    const candidateOrders = postBookingOrders.filter(order => {
        console.log(`🔎 [detectOrderConversion] 检查订单:`, {
            preorderId: order.preorderId,
            supplyId: order.supplyId,
            statusId: order.statusId,
            warehouseId: order.warehouseId,
            detailsQuantity: order.detailsQuantity,
            statusName: order.statusName
        });
        
        // 🔧 增强条件1: 更灵活的ID状态检查
        // 转换后的订单特征：supplyId有值，且statusId不是-1
        if (!order.supplyId) {
            console.log(`❌ 跳过：没有supplyId`);
            return false;
        }
        
        // 🔧 增强条件2: 状态ID检查 - 转换后通常为1或7
        if (order.statusId === -1) {
            console.log(`❌ 跳过：statusId仍为-1（未计划）`);
            return false;
        }
        
        // 🔧 增强条件3: 如果有预约前订单，进行特征匹配
        if (preBookingOrder) {
            // 仓库匹配（但允许一定容错）
            if (order.warehouseId !== preBookingOrder.warehouseId) {
                console.log(`❌ 跳过：仓库不匹配 (${order.warehouseId} vs ${preBookingOrder.warehouseId})`);
                return false;
            }
            
            // 商品数量匹配（但允许一定容错）
            if (order.detailsQuantity !== preBookingOrder.detailsQuantity) {
                console.log(`❌ 跳过：商品数量不匹配 (${order.detailsQuantity} vs ${preBookingOrder.detailsQuantity})`);
                return false;
            }
            
            // 🔧 增强条件4: 扩大时间窗口到15分钟
            const orderTime = new Date(order.changeDate || order.createDate);
            const timeDiff = Math.abs(orderTime - bookingTime);
            
            if (timeDiff > 15 * 60 * 1000) { // 15分钟内
                console.log(`❌ 跳过：时间窗口外 (${Math.round(timeDiff/1000)}s > 900s)`);
                return false;
            }
        }
        
        console.log(`✅ 候选订单通过筛选`);
        return true;
    });
    
    console.log(`🎯 [detectOrderConversion] 找到 ${candidateOrders.length} 个候选订单`);
    
    if (candidateOrders.length === 1) {
        // 找到唯一匹配的转换后订单
        return {
            found: true,
            order: candidateOrders[0],
            conversionStatus: 'converted',
            matchMethod: 'feature_matching',
            confidence: 'high'
        };
    } else if (candidateOrders.length > 1) {
        // 多个候选，选择最新的
        const latestOrder = candidateOrders.sort((a, b) => 
            new Date(b.changeDate) - new Date(a.changeDate)
        )[0];
        
        return {
            found: true,
            order: latestOrder,
            conversionStatus: 'converted',
            matchMethod: 'feature_matching_latest',
            confidence: 'medium',
            warning: `找到${candidateOrders.length}个候选订单，选择最新的`
        };
    }
    
    // 🔧 新增策略4: 当没有预约前订单信息时，通过状态变化匹配
    if (!preBookingOrder) {
        console.log(`🎯 [detectOrderConversion] 没有预约前订单信息，尝试通过状态变化匹配`);
        
        // 查找最近状态变化为"已预约"的订单
        const recentScheduledOrders = postBookingOrders.filter(order => {
            // 必须有supplyId且状态为已预约
            if (!order.supplyId || (order.statusId !== 1 && order.statusId !== 7)) {
                return false;
            }
            
            // 检查创建或更新时间是否在最近30分钟内
            const orderTime = new Date(order.changeDate || order.createDate);
            const timeDiff = Math.abs(orderTime - bookingTime);
            
            return timeDiff < 30 * 60 * 1000; // 30分钟内
        });
        
        if (recentScheduledOrders.length > 0) {
            // 按时间排序，选择最新的
            const latestOrder = recentScheduledOrders.sort((a, b) => 
                new Date(b.changeDate || b.createDate) - new Date(a.changeDate || a.createDate)
            )[0];
            
            console.log(`✅ [detectOrderConversion] 通过状态变化找到可能的转换订单:`, latestOrder);
            
            return {
                found: true,
                order: latestOrder,
                conversionStatus: 'converted',
                matchMethod: 'status_change_matching',
                confidence: 'medium',
                warning: `基于状态变化匹配，找到最新的已预约订单`
            };
        }
    }
    
    return {
        found: false,
        conversionStatus: 'unknown',
        error: '无法在预约后的订单列表中找到对应订单',
        debugInfo: {
            totalPostBookingOrders: postBookingOrders.length,
            candidateOrdersCount: candidateOrders?.length || 0,
            hasPreBookingOrder: !!preBookingOrder
        }
    };
}

// 保存订单转换映射关系
async function saveOrderConversionMapping(preorderID, supplyId, orderData) {
    try {
        console.log(`💾 [saveOrderConversionMapping] 保存ID映射: ${preorderID} → ${supplyId}`);
        
        // 保存到Chrome存储
        const mappingKey = `id_mapping_${preorderID}`;
        const mappingData = {
            preorderId: preorderID,
            supplyId: supplyId,
            conversionTime: new Date().toISOString(),
            warehouseId: orderData.warehouseId,
            warehouseName: orderData.warehouseName,
            detailsQuantity: orderData.detailsQuantity,
            statusChange: {
                from: { statusId: -1, statusName: "Не запланировано" },
                to: { statusId: orderData.statusId, statusName: orderData.statusName }
            },
            supplyDate: orderData.supplyDate,
            conversionMethod: 'auto_booking_detection'
        };
        
        await setStorage({ [mappingKey]: mappingData });
        console.log(`✅ [saveOrderConversionMapping] ID映射已保存`);
        
    } catch (error) {
        console.error('❌ [saveOrderConversionMapping] 保存ID映射失败:', error);
    }
}

// 从映射存储中查找supplyId
async function getSupplyIdFromMapping(preorderID) {
    try {
        const mappingKey = `id_mapping_${preorderID}`;
        const result = await getStorage([mappingKey]);
        const mapping = result[mappingKey];
        
        if (mapping && mapping.supplyId) {
            console.log(`📋 [getSupplyIdFromMapping] 从映射存储找到: ${preorderID} → ${mapping.supplyId}`);
            return mapping.supplyId;
        }
        
        return null;
    } catch (error) {
        console.error('❌ [getSupplyIdFromMapping] 查找映射失败:', error);
        return null;
    }
}

// ================================
// 原有函数继续
// ================================

// 存储操作封装
async function setStorage(data) {
    return chrome.storage.local.set(data);
}

async function getStorage(keys) {
    return chrome.storage.local.get(keys);
}

// 查找现有的 WB 认证标签页
async function findExistingAuthTab() {
    try {
        const tabs = await chrome.tabs.query({
            url: ['https://seller.wildberries.ru/*']
        });

        return tabs.length > 0 ? tabs[0].id : null;
    } catch (error) {
        console.error('查找标签页失败:', error);
        return null;
    }
}

// 创建或激活认证标签页
async function createOrActivateAuthTab() {
    try {
        // 首先查找现有标签页
        const existingTabId = await findExistingAuthTab();

        if (existingTabId) {
            return existingTabId;
        } else {
            const newTab = await chrome.tabs.create({
                url: 'https://seller.wildberries.ru/supplies-management/all-supplies',
                active: false
            });
            return newTab.id;
        }
    } catch (error) {
        console.error('创建或激活认证标签页时出错:', error);
        throw error;
    }
}

// 检查 WBX Cookie 有效性
async function checkWbxCookieValidity(tabId) {
    try {
        const names = ['wbx-validation-key', 'wbx-seller-device-id', 'x-supplier-id'];
        const domains = ['.wildberries.ru', '.seller-auth.wildberries.ru'];
        const allCookies = [];

        for (const domain of domains) {
            for (const name of names) {
                try {
                    const cookies = await chrome.cookies.getAll({ domain, name });
                    allCookies.push(...cookies);
                } catch (error) {
                    // 静默处理错误
                }
            }
        }

        if (allCookies.length === 0) return false;

        const cookieString = allCookies
            .map(cookie => `${cookie.name}=${cookie.value}`)
            .join('; ');

        const token = await getTokenFromTab(tabId);
        if (!token) return false;

        return await loadDataUsersWBNew(cookieString, token);

    } catch (error) {
        console.error('认证检查失败:', error);
        return false;
    }
}

// 从标签页获取访问令牌
async function getTokenFromTab(tabId) {
    try {
        const possibleKeys = [
            'wb-eu-passport-v2.access-token',
            'access-token',
            'authToken',
            'token'
        ];

        const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: (keys) => {
                for (const key of keys) {
                    const token = localStorage.getItem(key);
                    if (token) return token;
                }
                return null;
            },
            args: [possibleKeys]
        });

        return results?.[0]?.result || null;

    } catch (error) {
        console.error('获取令牌失败:', error);
        return null;
    }
}

// 调用 WB API 获取用户数据并验证
async function loadDataUsersWBNew(cookieString, token) {
    try {
        // 根据API文档，需要发送数组格式的请求
        const requestData = [
            {
                method: 'getUserSuppliers',
                params: {},
                id: 'json-rpc_8',
                jsonrpc: '2.0'
            },
            {
                method: 'listCountries',
                params: {},
                id: 'json-rpc_9',
                jsonrpc: '2.0'
            }
        ];

        const fullCookieString = `external-locale=zh; locale=zh; ${cookieString}`;

        const response = await fetch('https://seller.wildberries.ru/ns/suppliers/suppliers-portal-core/suppliers', {
            method: 'POST',
            headers: {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'authorizev3': token,
                'content-type': 'application/json',
                'origin': 'https://seller.wildberries.ru',
                'priority': 'u=1, i',
                'referer': 'https://seller.wildberries.ru/supplies-management/all-supplies',
                'root-version': 'v1.50.2',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': navigator.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': fullCookieString
            },
            body: JSON.stringify(requestData),
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error(`API调用失败: ${response.status}`);
        }

        const data = await response.json();

        // 处理数组响应格式
        let suppliersResult = null;
        if (Array.isArray(data)) {
            // 查找getUserSuppliers的响应
            const suppliersResponse = data.find(item => item.id === 'json-rpc_8');
            if (suppliersResponse && suppliersResponse.result) {
                suppliersResult = suppliersResponse.result;
            }
        } else if (data.result) {
            suppliersResult = data.result;
        }

        if (suppliersResult && suppliersResult.suppliers && suppliersResult.suppliers.length > 0) {
            await setStorage({
                Authorizev3: token,
                isHasCookies: true,
                wbxValidKey: cookieString,
                wbSuppliersList: suppliersResult,
                supplierIds: extractSupplierIds(cookieString),
                lastAuthCheck: Date.now()
            });
            return true;
        }
        return false;

    } catch (error) {
        console.error('API调用失败:', error);
        return await tryAlternativeAPI(cookieString, token);
    }
}

// 提取供应商 ID
function extractSupplierIds(cookieString) {
    const supplierIds = [];
    const cookies = cookieString.split('; ');

    for (const cookie of cookies) {
        const [name, value] = cookie.split('=');
        if (name === 'x-supplier-id' && value) {
            supplierIds.push(value);
        }
    }

    return supplierIds;
}

// 备用 API 调用方法
async function tryAlternativeAPI(cookieString, token) {
    const alternatives = [
        {
            headers: {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'authorizev3': token,
                'content-type': 'application/json',
                'origin': 'https://seller.wildberries.ru',
                'referer': 'https://seller.wildberries.ru/',
                'Cookie': `external-locale=zh; locale=zh; ${cookieString}`
            }
        },
        {
            headers: {
                'Content-Type': 'application/json',
                'Cookie': cookieString,
                'Authorization': `Bearer ${token}`,
                'Origin': 'https://seller.wildberries.ru'
            }
        }
    ];

    for (const alt of alternatives) {
        try {
            // 使用数组格式的请求体
            const requestData = [
                {
                    method: 'getUserSuppliers',
                    params: {},
                    id: 'json-rpc_8',
                    jsonrpc: '2.0'
                }
            ];

            const response = await fetch('https://seller.wildberries.ru/ns/suppliers/suppliers-portal-core/suppliers', {
                method: 'POST',
                headers: alt.headers,
                body: JSON.stringify(requestData),
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                let suppliersResult = null;

                if (Array.isArray(data)) {
                    const suppliersResponse = data.find(item => item.id === 'json-rpc_8');
                    if (suppliersResponse && suppliersResponse.result) {
                        suppliersResult = suppliersResponse.result;
                    }
                } else if (data.result) {
                    suppliersResult = data.result;
                }

                if (suppliersResult && suppliersResult.suppliers && suppliersResult.suppliers.length > 0) {
                    await setStorage({
                        Authorizev3: token,
                        isHasCookies: true,
                        wbxValidKey: cookieString,
                        wbSuppliersList: suppliersResult,
                        supplierIds: extractSupplierIds(cookieString),
                        lastAuthCheck: Date.now()
                    });
                    return true;
                }
            }
        } catch (error) {
            // 静默处理错误
            console.warn('备用API调用失败:', error);
        }
    }

    return false;
}

// 获取当前选中的店铺ID（按照2.md的成功实现）
async function getCurrentSupplierId() {
    try {
        // 先从存储中获取
        const storage = await getStorage(['currentSupplierId']);
        if (storage.currentSupplierId) {
            return storage.currentSupplierId;
        }

        // 如果存储中没有，尝试从供应商列表中获取第一个
        const suppliersData = await getStorage(['wbSuppliersList']);
        if (suppliersData.wbSuppliersList && suppliersData.wbSuppliersList.suppliers) {
            const suppliers = suppliersData.wbSuppliersList.suppliers;
            if (suppliers.length > 0) {
                const firstSupplierId = suppliers[0].id;
                // 保存到存储中
                await setStorage({ currentSupplierId: firstSupplierId });
                return firstSupplierId;
            }
        }

        return null;
    } catch (error) {
        console.error('获取当前店铺ID失败:', error);
        return null;
    }
}

// 设置WB网站的店铺ID cookie
async function setCurrentSupplierIdInWB(supplierId, supplierIdExternal) {
    try {
        const domains = ['.wildberries.ru', '.seller.wildberries.ru'];

        for (const domain of domains) {
            // 设置 x-supplier-id-external cookie
            if (supplierIdExternal) {
                await chrome.cookies.set({
                    url: `https://seller.wildberries.ru`,
                    name: 'x-supplier-id-external',
                    value: supplierIdExternal,
                    domain: domain,
                    path: '/',
                    secure: true,
                    httpOnly: false
                });
            }

            // 设置 x-supplier-id cookie
            if (supplierId) {
                await chrome.cookies.set({
                    url: `https://seller.wildberries.ru`,
                    name: 'x-supplier-id',
                    value: supplierId,
                    domain: domain,
                    path: '/',
                    secure: true,
                    httpOnly: false
                });
            }
        }


        return true;
    } catch (error) {
        console.error('设置店铺ID cookie失败:', error);
        throw error;
    }
}

// 切换到指定的店铺
async function switchToSupplier(supplierId) {
    try {
        // 保存当前店铺ID到存储
        await setStorage({ currentSupplierId: supplierId });

        // 设置WB网站的Cookie
        await setCurrentSupplierIdInWB(supplierId, supplierId);
        return { success: true };
    } catch (error) {
        console.error('切换店铺失败:', error);
        return { success: false, error: error.message };
    }
}

// 自动预约供应计划交货（基于2.md的成功实现）
async function autoBookSupplyDelivery(preorderID, optimalDate, userFilterCriteria = null) {
    try {
        console.log(`📋 开始自动预约供应计划: ${preorderID}`);
        console.log(`📅 目标日期: ${optimalDate}`);
        console.log(`🔧 传入的用户筛选条件:`, userFilterCriteria);

        // 验证和处理用户筛选条件
        if (userFilterCriteria) {
            if (!Array.isArray(userFilterCriteria.selectedDates)) {
                userFilterCriteria.selectedDates = [];
            }
            if (typeof userFilterCriteria.maxCoefficient !== 'number' || userFilterCriteria.maxCoefficient < 0) {
                userFilterCriteria.maxCoefficient = 20;
            }
            if (typeof userFilterCriteria.bufferDays !== 'number') {
                userFilterCriteria.bufferDays = 1;
            }
            if (userFilterCriteria.bufferDays < 0) {
                userFilterCriteria.bufferDays = 0;
            }
        }

        // 2. 在后台打开供应详情页面（不激活，不干扰用户）
        const supplyDetailUrl = `https://seller.wildberries.ru/supplies-management/all-supplies/supply-detail?preorderId=${preorderID}&supplyId`;

        const tab = await chrome.tabs.create({
            url: supplyDetailUrl,
            active: false  // 不激活标签页，在后台静默执行
        });

        // 记录标签页ID到监控任务中
        const monitoring = activeMonitorings.get(preorderID);
        if (monitoring) {
            monitoring.tabId = tab.id;
            monitoring.tabUrl = supplyDetailUrl;
            monitoring.tabCreateTime = new Date().toISOString();
            activeMonitorings.set(preorderID, monitoring);
            
            // 添加到标签页关联映射
            monitoringTabs.set(tab.id, {
                orderId: preorderID,
                taskId: monitoring.taskId,
                createTime: new Date().toISOString()
            });
            
            console.log(`🔗 已关联标签页 ${tab.id} 到监控任务 ${preorderID}`);
        }

        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 记录预约前的订单状态（用于后续验证）
        let preBookingStatus = null;
        let preBookingOrder = null;
        try {
            const preBookingOrders = await getAllOrdersFromWBNewAPI();
            preBookingOrder = preBookingOrders.find(order => 
                order.preorderId && order.preorderId.toString() === preorderID.toString()
            );
            if (preBookingOrder) {
                preBookingStatus = {
                    statusId: preBookingOrder.statusId,        // ✅ 使用实际API字段
                    supplyId: preBookingOrder.supplyId,        // ✅ 使用实际API字段  
                    statusName: preBookingOrder.statusName,    // ✅ 使用实际API字段
                    warehouseId: preBookingOrder.warehouseId,
                    detailsQuantity: preBookingOrder.detailsQuantity
                };
                console.log(`📊 预约前状态:`, preBookingStatus);
                console.log(`📊 预约前完整订单:`, preBookingOrder);
            } else {
                console.warn(`⚠️ 未找到preorderId为 ${preorderID} 的订单`);
            }
        } catch (error) {
            console.warn('无法获取预约前状态，将依赖脚本返回值:', error.message);
        }

        // 3. 注入脚本执行自动预约
        const result = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: performAutoBooking,
            args: [optimalDate, userFilterCriteria]
        });

        // 🔧 修复：动态等待supplyId获取完成后再关闭标签页
        const closeTabAfterSupplyIdCheck = async () => {
            let maxWaitTime = 300000; // 最多等待300秒
            let checkInterval = 2000; // 每2秒检查一次
            let startTime = Date.now();
            
            while (Date.now() - startTime < maxWaitTime) {
                try {
                    // 检查脚本是否已经返回结果
                    if (result && result[0] && result[0].result) {
                        const scriptResult = result[0].result;
                        
                        // 如果脚本获取到了supplyId，立即关闭标签页
                        if (scriptResult.newSupplyId) {
                            console.log(`✅ 获取到supplyId: ${scriptResult.newSupplyId}，关闭标签页`);
                            await chrome.tabs.remove(tab.id);
                            return;
                        }
                        
                        // 如果脚本返回成功但没有supplyId，继续等待
                        if (scriptResult.success) {
                            console.log(`⏳ 预约成功但supplyId为空，继续等待...`);
                        }
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, checkInterval));
                } catch (error) {
                    console.warn('检查supplyId获取状态时出错:', error);
                    break;
                }
            }
            
            // 超时后强制关闭标签页
            console.warn(`⏰ 等待supplyId超时(${maxWaitTime}ms)，强制关闭标签页`);
            try {
                await chrome.tabs.remove(tab.id);
            } catch (error) {
                // 静默处理关闭标签页失败
            }
        };
        
        // 延迟启动关闭检查，给脚本执行留出时间
        setTimeout(closeTabAfterSupplyIdCheck, 3000);

        // 🔧 修复：增强成功检测逻辑
        let scriptSuccess = false;
        let scriptResult = null;

        // 🔧 增强验证：检查脚本返回值和supplyId获取情况
        if (result && result[0] && result[0].result) {
            scriptResult = result[0].result;
            
            // 检查脚本是否真正成功获取到supplyId
            if (scriptResult.success && scriptResult.newSupplyId) {
                scriptSuccess = true;
                console.log('✅ 脚本返回预约成功且获取到supplyId:', scriptResult.newSupplyId);
            } else if (scriptResult.success && !scriptResult.newSupplyId) {
                console.warn('⚠️ 脚本返回预约成功但未获取到supplyId，需要通过API验证');
                // 不设置scriptSuccess，继续进行WB系统验证
            } else {
                console.warn('⚠️ 脚本返回预约失败或状态不明确');
            }
        } else {
            console.warn('⚠️ 脚本未返回有效结果，需要验证WB系统状态');
        }

        // 🚨 关键修复：如果脚本返回值不明确，通过WB系统验证真实状态
        if (!scriptSuccess) {
            console.log('🔍 开始验证WB系统中的实际预约状态...');
            
            // 等待一下让WB系统更新状态
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 智能等待：多次检查直到检测到转换或超时
            let conversionResult = null;
            const maxAttempts = 15; // 最多尝试15次
            const checkInterval = 2000; // 每2秒检查一次
            
            for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                console.log(`⏳ 第${attempt}次检查订单转换状态...`);
                
                try {
                    const postBookingOrders = await getAllOrdersFromWBNewAPI();
                    conversionResult = detectOrderConversion(preBookingOrder, postBookingOrders, preorderID);
                    
                    if (conversionResult.found) {
                        console.log(`✅ 在第${attempt}次检查中检测到订单转换!`);
                        break;
                    }
                    
                    if (attempt < maxAttempts) {
                        console.log(`💤 等待${checkInterval}ms后再次检查...`);
                        await new Promise(resolve => setTimeout(resolve, checkInterval));
                    }
                } catch (error) {
                    console.warn(`第${attempt}次检查时出错:`, error);
                    if (attempt < maxAttempts) {
                        await new Promise(resolve => setTimeout(resolve, checkInterval));
                    }
                }
            }
            
            if (conversionResult && conversionResult.found) {
                const postBookingOrder = conversionResult.order;
                
                // 如果发生了转换，保存映射关系
                if (conversionResult.conversionStatus === 'converted' && postBookingOrder.supplyId) {
                    await saveOrderConversionMapping(preorderID, postBookingOrder.supplyId, postBookingOrder);
                }
                
                const currentStatus = {
                    statusId: postBookingOrder.statusId,      // ✅ 使用实际API字段
                    supplyId: postBookingOrder.supplyId,      // ✅ 使用实际API字段
                    statusName: postBookingOrder.statusName,  // ✅ 使用实际API字段
                    conversionInfo: conversionResult
                };
                
                console.log(`📊 预约后状态:`, currentStatus);
                
                // 检查状态是否发生了有意义的变化（表示预约成功）
                const statusChanged = preBookingStatus && (
                    currentStatus.statusId !== preBookingStatus.statusId ||
                    currentStatus.supplyId !== preBookingStatus.supplyId ||
                    currentStatus.statusName !== preBookingStatus.statusName
                );
                
                // 检查是否为已预约状态 (基于实际API数据结构)
                const isScheduledStatus = (
                    currentStatus.statusId === 1 || // statusId为1表示已计划 ✅
                    currentStatus.statusName === 'Запланировано' || // 状态名为已计划 ✅
                    (currentStatus.supplyId && currentStatus.supplyId !== preorderID) // 有supplyId且不同于preorderID ✅
                );
                
                // 检查是否发生了转换
                const hasConversion = conversionResult.conversionStatus === 'converted';
                
                if (statusChanged || isScheduledStatus || hasConversion) {
                    console.log('✅ WB系统验证：预约确实成功了！');
                    console.log(`🎯 成功检测方法: ${conversionResult.matchMethod}`);
                    console.log(`🔄 转换状态: ${conversionResult.conversionStatus}`);
                    
                    // 🔧 简单修复：预约成功后立即关闭标签页，不再等待页面脚本获取supplyId
                    console.log(`🎯 预约验证成功，supplyId: ${currentStatus.supplyId}，立即关闭标签页`);
                    try {
                        await chrome.tabs.remove(tab.id);
                        console.log('✅ 标签页已关闭');
                    } catch (error) {
                        console.warn('⚠️ 关闭标签页失败，但预约已成功:', error);
                    }
                    
                    scriptSuccess = true;
                    scriptResult = {
                        success: true,
                        message: '通过智能转换检测验证预约成功',
                        verificationMethod: 'smart_conversion_detection',
                        selectedDate: { text: '已预约（具体日期请在WB系统查看）' },
                        originalOptimalDate: optimalDate,
                        newSupplyId: currentStatus.supplyId,
                        conversionDetails: conversionResult,
                        wbStatusChange: {
                            before: preBookingStatus,
                            after: currentStatus
                        }
                    };
                } else {
                    console.log('❌ WB系统验证：预约确实失败了');
                    console.log(`📊 转换检测结果:`, conversionResult);
                }
            } else {
                console.log('❌ WB系统验证：无法检测到订单转换，预约可能失败');
                if (conversionResult) {
                    console.log(`❌ 检测失败原因: ${conversionResult.error}`);
                }
            }
        }

        // 🔧 最终判断和返回：确保获取到有效supplyId
        if (scriptSuccess) {
            // 额外验证：确保最终结果包含有效的supplyId
            const hasValidSupplyId = (
                (scriptResult.newSupplyId && String(scriptResult.newSupplyId).trim()) ||
                (scriptResult.conversionDetails?.order?.supplyId)
            );
            
            if (hasValidSupplyId) {
                const finalSupplyId = scriptResult.newSupplyId || scriptResult.conversionDetails?.order?.supplyId;
                console.log('🎉 自动预约最终确认成功，supplyId:', finalSupplyId);
                
                return {
                    success: true,
                    message: `供应计划 ${preorderID} 自动预约成功 (supplyId: ${finalSupplyId})`,
                    data: {
                        ...scriptResult,
                        finalSupplyId: finalSupplyId,
                        verificationPassed: true
                    }
                };
            } else {
                console.error('❌ 预约显示成功但未获取到有效supplyId，视为失败');
                throw new Error(`预约完成但无法获取supplyId，可能需要手动检查订单状态 (preorderId: ${preorderID})`);
            }
        } else {
            throw new Error('自动预约失败：脚本执行和WB系统验证均未确认成功');
        }

    } catch (error) {
        console.error('自动预约供应计划交货失败:', error);
        
        // 过滤Chrome扩展技术错误，不显示给用户
        let userMessage = '自动预约失败: ' + error.message;
        if (error.message.includes('Frame with ID') && error.message.includes('was removed')) {
            userMessage = '预约过程中标签页被关闭，已自动停止预约';
        } else if (error.message.includes('Extension context invalidated') || 
                   error.message.includes('Receiving end does not exist')) {
            userMessage = '预约过程中连接中断，请重试';
        }
        
        return {
            success: false,
            message: userMessage,
            error: error.message
        };
    }
}

// 在页面中执行自动预约的函数（将被注入到页面中执行）
function performAutoBooking(optimalDate, userFilterCriteria = null) {
    return new Promise((resolve, reject) => {
        let actualSelectedDate = null; // 存储实际选择的日期信息
        
        try {
            console.log('🎯 performAutoBooking 开始执行');
            console.log('📅 目标日期:', optimalDate);
            console.log('🔧 用户筛选条件:', JSON.stringify(userFilterCriteria, null, 2));

            // 步骤1: 等待页面加载完成
            const waitForPageLoad = () => {
                return new Promise((resolve) => {
                    if (document.readyState === 'complete') {
                        resolve();
                    } else {
                        window.addEventListener('load', resolve);
                    }
                });
            };

            // 步骤2: 查找并点击"安排交货"按钮
            const clickScheduleButton = () => {
                return new Promise((resolve, reject) => {
                    const findAndClickButton = () => {
                        const scheduleButton = Array.from(document.querySelectorAll('button')).find(btn =>
                            btn.textContent.includes('安排交货') || btn.textContent.includes('Запланировать')
                        );

                        if (scheduleButton) {
                            scheduleButton.click();

                            // 等待日历出现
                            const waitForCalendar = () => {
                                const calendar = document.querySelector('[data-testid*="calendar"]') ||
                                                document.querySelector('.calendar') ||
                                                document.querySelectorAll('button').length > 10;

                                if (calendar) {
                                    resolve();
                                } else {
                                    setTimeout(waitForCalendar, 100);
                                }
                            };

                            setTimeout(waitForCalendar, 200);
                        } else {
                            setTimeout(findAndClickButton, 100);
                        }
                    };

                    findAndClickButton();
                    setTimeout(() => reject(new Error('未找到安排交货按钮')), 10000);
                });
            };

            // 辅助函数：从日期元素中提取日期和系数信息
            const extractDateInfo = (element, index) => {
                const text = element.textContent || element.innerText || '';
                
                // 提取系数值，参考项目的方式
                let coefficient = 0; // 默认免费
                
                // 1. 检查是否包含"还没有"（不可用日期）
                if (text.includes('还没有')) {
                    return null; // 不可用日期
                }
                
                // 2. 使用参考项目的正则提取系数
                const coefficientRegex = /承兑✕(\d)/;
                const match = text.match(coefficientRegex);
                if (match) {
                    coefficient = parseInt(match[1]);
                } else {
                    // 3. 检查其他可能的系数格式
                    const altRegex = /([0-9.]+)x|免费|бесплатно/i;
                    const altMatch = text.match(altRegex);
                    if (altMatch) {
                        const matchText = altMatch[0].toLowerCase();
                        if (matchText.includes('免费') || matchText.includes('бесплатно')) {
                            coefficient = 0;
                        } else if (matchText.includes('x')) {
                            const numStr = matchText.replace('x', '');
                            coefficient = parseFloat(numStr) || 0;
                        }
                    }
                }
                
                // 4. 解析日期信息
                let parsedDate = null;
                
                // 尝试从文本中提取日期，支持俄语和中文格式
                const datePatterns = [
                    // 新增中文格式: "25 八月"
                    /(\d{1,2})\s+(一月|二月|三月|四月|五月|六月|七月|八月|九月|十月|十一月|十二月)/i,
                    // 俄语格式: "25 августа, пн"
                    /(\d{1,2})\s+(января|февраля|марта|апреля|мая|июня|июля|августа|сентября|октября|ноября|декабря)/i,
                    // 中文格式: "8月25日"
                    /(\d{1,2})月(\d{1,2})日/,
                    // 数字格式: "25.08" 或 "08/25"
                    /(\d{1,2})[./](\d{1,2})/
                ];
                
                for (const pattern of datePatterns) {
                    const dateMatch = text.match(pattern);
                    if (dateMatch) {
                        if (pattern.source.includes('一月|二月')) { // 新增中文格式 "25 八月"
                            const day = parseInt(dateMatch[1]);
                            const monthName = dateMatch[2];
                            const monthMap = {
                                '一月': 0, '二月': 1, '三月': 2, '四月': 3, '五月': 4, '六月': 5,
                                '七月': 6, '八月': 7, '九月': 8, '十月': 9, '十一月': 10, '十二月': 11
                            };
                            if (monthMap[monthName] !== undefined) {
                                const currentYear = new Date().getFullYear();
                                parsedDate = new Date(currentYear, monthMap[monthName], day);
                            }
                        } else if (pattern.source.includes('января|февраля')) {
                            // 俄语月份
                            const day = parseInt(dateMatch[1]);
                            const monthName = dateMatch[2].toLowerCase();
                            const monthMap = {
                                'января': 0, 'февраля': 1, 'марта': 2, 'апреля': 3,
                                'мая': 4, 'июня': 5, 'июля': 6, 'августа': 7,
                                'сентября': 8, 'октября': 9, 'ноября': 10, 'декабря': 11
                            };
                            if (monthMap[monthName] !== undefined) {
                                const currentYear = new Date().getFullYear();
                                parsedDate = new Date(currentYear, monthMap[monthName], day);
                            }
                        } else if (pattern.source.includes('月')) {
                            // 中文格式 "8月25日"
                            const month = parseInt(dateMatch[1]) - 1; // 月份从0开始
                            const day = parseInt(dateMatch[2]);
                            const currentYear = new Date().getFullYear();
                            parsedDate = new Date(currentYear, month, day);
                        } else {
                            // 数字格式 - 需要判断是日/月还是月/日
                            const num1 = parseInt(dateMatch[1]);
                            const num2 = parseInt(dateMatch[2]);
                            const currentYear = new Date().getFullYear();
                            
                            // 假设是日/月格式（欧洲格式）
                            if (num2 <= 12) {
                                parsedDate = new Date(currentYear, num2 - 1, num1);
                            }
                        }
                        break;
                    }
                }
                
                return {
                    element,
                    index,
                    coefficient,
                    text,
                    parsedDate
                };
            };


            // 辅助函数：模拟鼠标悬停（参考项目的实现）
            const simulateHover = (element) => {
                element.dispatchEvent(new PointerEvent('pointerover', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                }));
                element.dispatchEvent(new MouseEvent('mouseover', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                }));
                element.dispatchEvent(new PointerEvent('pointerenter', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                }));
                element.dispatchEvent(new MouseEvent('mouseenter', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                }));
            };

            // 辅助函数：等待元素出现（参考项目的实现）
            const waitForElementInContainer = (container, selector, condition, timeout = 5000) => {
                return new Promise((resolve) => {
                    const startTime = Date.now();

                    // 立即检查是否已存在
                    const existing = Array.from(container.querySelectorAll(selector)).find(condition);
                    if (existing) return resolve(existing);

                    // 设置MutationObserver监听变化
                    const observer = new MutationObserver((mutations) => {
                        const found = Array.from(container.querySelectorAll(selector)).find(condition);
                        if (found) {
                            observer.disconnect();
                            resolve(found);
                        }
                        // 超时检查
                        else if (Date.now() - startTime > timeout) {
                            observer.disconnect();
                            resolve(null);
                        }
                    });

                    observer.observe(container, {
                        childList: true,
                        subtree: true,
                        attributes: true,
                        characterData: true
                    });
                });
            };

            // 步骤3: 选择最优日期（使用参考项目的方式）
            const selectOptimalDate = () => {
                return new Promise((resolve, reject) => {
                    const findAndSelectDate = () => {
                        // 使用参考项目的方式：查找表格结构
                        const calendarTableView = document.querySelector('div.Calendar-plan-table-view');
                        if (!calendarTableView) {
                            console.log('未找到日历表格上层DIV');
                            setTimeout(findAndSelectDate, 200);
                            return;
                        }

                        const calendarTable = calendarTableView.querySelector('table');
                        if (!calendarTable) {
                            console.log('未找到日历表格');
                            setTimeout(findAndSelectDate, 200);
                            return;
                        }

                        // 获取所有表格单元格
                        const tds = calendarTable.querySelectorAll('tbody > tr > td');
                        console.log(`找到 ${tds.length} 个表格单元格`);
                        
                        if (tds.length === 0) {
                            setTimeout(findAndSelectDate, 200);
                            return;
                        }

                        // 过滤掉空内容的td
                        const notEmptyTds = Array.from(tds).filter(td => td.textContent.trim() !== '');
                        
                        // 提取每个元素的日期信息
                        const dateInfos = [];
                        notEmptyTds.forEach((td, index) => {
                            const info = extractDateInfo(td, index);
                            if (info) {
                                dateInfos.push(info);
                            }
                        });
                        
                        console.log(`解析到 ${dateInfos.length} 个有效日期:`, dateInfos.map(d => ({
                            index: d.index,
                            coefficient: d.coefficient,
                            parsedDate: d.parsedDate ? d.parsedDate.toISOString().split('T')[0] : 'null',
                            text: d.text.substring(0, 50) + '...'
                        })));
                        
                        if (dateInfos.length === 0) {
                            setTimeout(findAndSelectDate, 200);
                            return;
                        }
                        
                        // 按系数排序（免费优先）
                        dateInfos.sort((a, b) => {
                            if (a.coefficient === 0 && b.coefficient !== 0) return -1;
                            if (a.coefficient !== 0 && b.coefficient === 0) return 1;
                            return a.coefficient - b.coefficient;
                        });
                        
                        console.log('排序后的所有日期:', dateInfos.map(d => `索引${d.index}:系数${d.coefficient}`));
                        
                        // 根据用户条件筛选（如果有的话）
                        let eligibleDates = dateInfos;
                        
                        // 先按系数筛选
                        if (userFilterCriteria && userFilterCriteria.maxCoefficient !== undefined) {
                            const beforeFilter = eligibleDates.length;
                            eligibleDates = dateInfos.filter(info => info.coefficient <= userFilterCriteria.maxCoefficient);
                            console.log(`按最大系数 ${userFilterCriteria.maxCoefficient} 筛选：${beforeFilter} -> ${eligibleDates.length} 个日期`);
                        }
                        
                        // 再按用户选择的日期范围筛选
                        if (userFilterCriteria && userFilterCriteria.selectedDates && userFilterCriteria.selectedDates.length > 0) {
                            console.log('开始按用户选择的日期筛选...');
                            console.log('用户选择的日期:', userFilterCriteria.selectedDates);
                            console.log('缓冲天数:', userFilterCriteria.bufferDays);
                            
                            // 计算目标日期范围
                            // 严格使用用户选择的日期
                            const targetDates = userFilterCriteria.selectedDates.map(dateStr => new Date(dateStr));
                            
                            console.log('目标日期范围:', targetDates.map(d => d.toISOString().split('T')[0]));
                            
                            // 筛选出在目标日期范围内的日期
                            const beforeDateFilter = eligibleDates.length;
                            eligibleDates = eligibleDates.filter(dateInfo => {
                                if (!dateInfo.parsedDate) {
                                    console.log('⚠️  跳过未解析的日期:', dateInfo.text.substring(0, 50));
                                    return false; // 跳过无法解析日期的项
                                }
                                
                                // 检查是否在目标日期范围内
                                const isInRange = targetDates.some(targetDate => {
                                    const isSameDate = dateInfo.parsedDate.getFullYear() === targetDate.getFullYear() &&
                                                      dateInfo.parsedDate.getMonth() === targetDate.getMonth() &&
                                                      dateInfo.parsedDate.getDate() === targetDate.getDate();
                                    
                                    if (isSameDate) {
                                        console.log('✅ 找到匹配日期:', {
                                            parsed: dateInfo.parsedDate.toISOString().split('T')[0],
                                            target: targetDate.toISOString().split('T')[0],
                                            text: dateInfo.text.substring(0, 50),
                                            coefficient: dateInfo.coefficient
                                        });
                                    }
                                    
                                    return isSameDate;
                                });
                                
                                if (!isInRange) {
                                    console.log('❌ 日期不在范围内:', {
                                        parsed: dateInfo.parsedDate.toISOString().split('T')[0],
                                        text: dateInfo.text.substring(0, 50)
                                    });
                                }
                                
                                return isInRange;
                            });
                            
                            console.log(`按日期范围筛选：${beforeDateFilter} -> ${eligibleDates.length} 个日期`);
                            
                            if (eligibleDates.length === 0) {
                                console.log('⚠️  没有找到用户指定日期范围内的可用日期，回退到系数筛选');
                                // 回退到之前的系数筛选结果
                                eligibleDates = dateInfos.filter(info => 
                                    !userFilterCriteria.maxCoefficient || 
                                    info.coefficient <= userFilterCriteria.maxCoefficient
                                );
                            }
                        }
                        
                        console.log('最终符合条件的日期:', eligibleDates.map(d => `索引${d.index}:系数${d.coefficient}:${d.parsedDate ? d.parsedDate.toISOString().split('T')[0] : 'null'}:${d.text.substring(0, 20)}...`));
                        
                        if (eligibleDates.length > 0) {
                            // 从符合条件的日期中选择系数最低的（已经按系数排序过了）
                            const selectedDateInfo = eligibleDates[0];
                            console.log('🎯 最终选择的日期:', {
                                index: selectedDateInfo.index,
                                coefficient: selectedDateInfo.coefficient,
                                parsedDate: selectedDateInfo.parsedDate ? selectedDateInfo.parsedDate.toISOString().split('T')[0] : 'null',
                                text: selectedDateInfo.text.substring(0, 50)
                            });
                            console.log(`✅ 自动选择了系数为 ${selectedDateInfo.coefficient} 的日期（索引: ${selectedDateInfo.index}），日期: ${selectedDateInfo.parsedDate ? selectedDateInfo.parsedDate.toISOString().split('T')[0] : '未解析'}`);
                            
                            // 保存实际选择的日期信息
                            actualSelectedDate = {
                                index: selectedDateInfo.index,
                                coefficient: selectedDateInfo.coefficient,
                                text: selectedDateInfo.text,
                                parsedDate: selectedDateInfo.parsedDate // 添加解析后的日期
                            };
                            
                            // 使用参考项目的方式：先悬停再点击
                            simulateHover(selectedDateInfo.element);
                            
                            // 等待动态内容出现（"选择"按钮）
                            waitForElementInContainer(
                                selectedDateInfo.element,
                                'button, span',
                                el => el.textContent.trim() === '选择' || el.textContent.trim() === 'Выбрать',
                                2000
                            ).then(selectButton => {
                                if (selectButton) {
                                    console.log('找到"选择"按钮并点击');
                                    selectButton.click();
                                    
                                    // 查找Schedule按钮
                                    const checkScheduleButton = () => {
                                        const scheduleButton = document.querySelector('div.Calendar-plan-buttons__transfer > button');
                                        if (scheduleButton && !scheduleButton.disabled) {
                                            console.log('Schedule按钮可点击，执行点击');
                                            scheduleButton.click();
                                            resolve();
                                        } else {
                                            setTimeout(checkScheduleButton, 100);
                                        }
                                    };
                                    
                                    setTimeout(checkScheduleButton, 100);
                                } else {
                                    console.log('未找到"选择"按钮');
                                    setTimeout(findAndSelectDate, 200);
                                }
                            });
                        } else {
                            setTimeout(findAndSelectDate, 200);
                        }
                    };

                    findAndSelectDate();
                    setTimeout(() => {
                        const criteriaMsg = userFilterCriteria 
                            ? `最大系数: ${userFilterCriteria.maxCoefficient}` 
                            : '仅免费日期';
                        reject(new Error(`未找到符合条件的日期 (${criteriaMsg})`));
                    }, 15000); // 增加超时时间
                });
            };

            // 步骤4: 提交预约
            const submitBooking = () => {
                return new Promise((resolve, reject) => {
                    const findAndSubmit = () => {
                        const submitButton = Array.from(document.querySelectorAll('button')).find(btn =>
                            btn.textContent.includes('日程') || btn.textContent.includes('Запланировать')
                        );

                        if (submitButton && !submitButton.disabled) {
                            submitButton.click();

                            const checkSubmitSuccess = () => {
                                const isSuccess = document.querySelector('.success') ||
                                                document.querySelector('[class*="success"]') ||
                                                !document.querySelector('[data-testid*="calendar"]') ||
                                                document.title.includes('supply-detail');

                                if (isSuccess) {
                                    // 尝试获取新生成的supplyId
                                    let newSupplyId = null;

                                    // 方法1: 从URL中获取supplyId
                                    const urlMatch = window.location.href.match(/supplyId=([^&]+)/);
                                    if (urlMatch) {
                                        newSupplyId = urlMatch[1];
                                    }

                                    // 方法2: 从页面元素中查找supplyId
                                    if (!newSupplyId) {
                                        const supplyElements = document.querySelectorAll('[data-testid*="supply"], [class*="supply"]');
                                        for (const element of supplyElements) {
                                            const text = element.textContent || element.innerText;
                                            const match = text.match(/WB\d{8,}/);
                                            if (match) {
                                                newSupplyId = match[0];
                                                break;
                                            }
                                        }
                                    }

                                    // 方法3: 智能等待页面跳转后获取supplyId
                                    if (!newSupplyId) {
                                        console.log('🔍 方法3: 开始智能等待页面状态变化...');
                                        
                                        const waitForSupplyId = () => {
                                            return new Promise((resolveWait) => {
                                                const maxWait = 8000; // 最多等待8秒
                                                const checkInterval = 500; // 每500ms检查一次
                                                let elapsed = 0;
                                                
                                                const checkSupplyId = () => {
                                                    // 重新检查URL
                                                    const urlMatch = window.location.href.match(/supplyId=([^&]+)/);
                                                    if (urlMatch) {
                                                        console.log('✅ 从URL获取到supplyId:', urlMatch[1]);
                                                        resolveWait(urlMatch[1]);
                                                        return;
                                                    }
                                                    
                                                    // 重新检查DOM元素
                                                    const supplyElements = document.querySelectorAll('[data-testid*="supply"], [class*="supply"], [id*="supply"]');
                                                    for (const element of supplyElements) {
                                                        const text = element.textContent || element.innerText;
                                                        const match = text.match(/WB\d{8,}/);
                                                        if (match) {
                                                            console.log('✅ 从DOM元素获取到supplyId:', match[0]);
                                                            resolveWait(match[0]);
                                                            return;
                                                        }
                                                    }
                                                    
                                                    // 检查页面是否跳转到供应单详情页
                                                    if (document.title.includes('supply-detail') || 
                                                        window.location.href.includes('supply-detail')) {
                                                        
                                                        const detailUrlMatch = window.location.href.match(/supplyId=([^&]+)/);
                                                        if (detailUrlMatch) {
                                                            console.log('✅ 从详情页URL获取到supplyId:', detailUrlMatch[1]);
                                                            resolveWait(detailUrlMatch[1]);
                                                            return;
                                                        }
                                                    }
                                                    
                                                    elapsed += checkInterval;
                                                    if (elapsed < maxWait) {
                                                        setTimeout(checkSupplyId, checkInterval);
                                                    } else {
                                                        console.warn('⏰ 等待supplyId超时');
                                                        resolveWait(null);
                                                    }
                                                };
                                                
                                                checkSupplyId();
                                            });
                                        };
                                        
                                        // 异步等待并设置supplyId
                                        waitForSupplyId().then(foundSupplyId => {
                                            if (foundSupplyId) {
                                                newSupplyId = foundSupplyId;
                                            }
                                        });
                                        
                                                                                 // 等待一段时间让异步检查有机会完成
                                    }

                                    resolve({
                                        success: true,
                                        message: '预约提交成功',
                                        selectedDate: actualSelectedDate || {text: '未知日期'},
                                        originalOptimalDate: optimalDate,
                                        newSupplyId: newSupplyId
                                    });
                                } else {
                                    setTimeout(checkSubmitSuccess, 200);
                                }
                            };

                            setTimeout(checkSubmitSuccess, 500);
                        } else {
                            setTimeout(findAndSubmit, 100);
                        }
                    };

                    findAndSubmit();
                    setTimeout(() => reject(new Error('未找到可用的日程按钮')), 10000);
                });
            };

            // 执行自动预约流程
            waitForPageLoad()
                .then(() => {
                    return new Promise(resolve => {
                        const checkPageReady = () => {
                            const hasButtons = document.querySelectorAll('button').length > 0;
                            const hasContent = document.body.textContent.length > 100;

                            if (hasButtons && hasContent) {
                                resolve();
                            } else {
                                setTimeout(checkPageReady, 200);
                            }
                        };

                        setTimeout(checkPageReady, 500);
                    });
                })
                .then(clickScheduleButton)
                .then(selectOptimalDate)
                .then(submitBooking)
                .then(resolve)
                .catch(reject);

        } catch (error) {
            console.error('自动预约执行失败:', error);
            reject(error);
        }
    });
}

// 主要的认证检查函数
async function checkAuth() {
    try {
        const tabId = await createOrActivateAuthTab();
        if (!tabId) {
            return { success: false, authenticated: false, message: '无法创建标签页' };
        }

        await new Promise(resolve => setTimeout(resolve, 2000));
        const isValid = await checkWbxCookieValidity(tabId);

        return {
            success: true,
            authenticated: isValid,
            message: isValid ? '认证成功' : '请登录 WB 卖家中心',
            tabId: tabId
        };

    } catch (error) {
        console.error('认证失败:', error);
        return { success: false, authenticated: false, message: '认证失败' };
    }
}

// 处理来自内容脚本的消息
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    // 处理来自content script的WBAssist认证消息
    if (request.action === 'auth_success') {
        console.log('🎉 Background 收到认证成功消息:', request.data);
        
        // 保存认证信息到chrome.storage
        chrome.storage.local.set({
            wbassist_auth: {
                access_token: request.data.access_token,
                user: request.data.user,
                supabase_url: request.data.supabase_url,
                authenticated: true,
                timestamp: Date.now()
            }
        }).then(() => {
            console.log('✅ 认证信息已保存到storage');
            
            // 广播认证成功消息给所有popup和content script
            chrome.runtime.sendMessage({
                action: 'auth_status_updated',
                authenticated: true,
                user: request.data.user
            }).catch(() => {
                // popup可能没有打开，忽略错误
                console.log('📢 无active popup，消息广播跳过');
            });
        });
        
        sendResponse({ success: true });
        return true;
    }

    if (request.action === 'auth_logout') {
        console.log('🚪 Background 收到登出消息');
        
        // 🔧 彻底清除所有认证相关的存储
        const keysToRemove = ['wbassist_auth', 'user_profile', 'auth_token', 'supabase_session'];
        chrome.storage.local.remove(keysToRemove).then(() => {
            console.log('🗑️ 所有认证信息已清除');
            
            // 重置内存中的认证状态
            if (typeof globalAuthData !== 'undefined') {
                globalAuthData = null;
            }
            
            // 广播登出消息
            chrome.runtime.sendMessage({
                action: 'auth_status_updated',
                authenticated: false,
                user: null
            }).catch(() => {
                console.log('📢 无active popup，登出消息广播跳过');
            });
        });
        
        sendResponse({ success: true });
        return true;
    }

    // 获取WBAssist认证状态
    if (request.action === 'get_wbassist_auth') {
        chrome.storage.local.get(['wbassist_auth']).then(result => {
            const authData = result.wbassist_auth;
            if (authData && authData.authenticated) {
                sendResponse({
                    success: true,
                    authenticated: true,
                    user: authData.user,
                    access_token: authData.access_token,
                    supabase_url: authData.supabase_url
                });
            } else {
                sendResponse({
                    success: false,
                    authenticated: false,
                    message: '未找到认证信息'
                });
            }
        });
        return true;
    }

    if (request.action === 'checkAuth') {
        checkAuth().then(sendResponse).catch(() => {
            sendResponse({ success: false, authenticated: false, message: '认证失败' });
        });
        return true;
    }

    if (request.action === 'getAuthData') {
        getStorage(['Authorizev3', 'wbxValidKey', 'supplierIds', 'wbSuppliersList']).then(data => {
            sendResponse({ success: true, data });
        }).catch(() => {
            sendResponse({ success: false, message: '获取数据失败' });
        });
        return true;
    }

    // 处理用户主动刷新数据的请求
    if (request.action === 'refreshData') {
        checkAuth().then(async (authResult) => {
            if (authResult.success && authResult.authenticated) {
                // 重新获取最新的认证数据
                const data = await getStorage(['Authorizev3', 'wbxValidKey', 'supplierIds', 'wbSuppliersList']);
                sendResponse({ success: true, data, refreshed: true });
            } else {
                sendResponse({ success: false, message: '认证失败，无法刷新数据' });
            }
        }).catch(() => {
            sendResponse({ success: false, message: '刷新失败' });
        });
        return true;
    }

    // 获取当前选中的店铺ID
    if (request.action === 'getCurrentSupplierId') {
        getCurrentSupplierId().then(supplierId => {
            sendResponse({ success: true, supplierId });
        }).catch(error => {
            sendResponse({ success: false, message: error.message });
        });
        return true;
    }

    // 切换店铺
    if (request.action === 'switchSupplier') {
        switchToSupplier(request.supplierId).then(sendResponse);
        return true;
    }

    // 自动预约供应计划交货（基于2.md的成功实现）
    if (request.action === 'autoBookSupply') {
        autoBookSupplyDelivery(request.preorderID, request.optimalDate).then(sendResponse);
        return true;
    }

    // 启动预约监控服务
    if (request.action === 'startMonitoring') {
        startBookingMonitoring(request.config).then(sendResponse);
        return true;
    }

    // 停止预约监控服务
    if (request.action === 'stopMonitoring') {
        stopBookingMonitoring(request.orderId).then(sendResponse);
        return true;
    }

    // 取消预约（用户手动取消）
    if (request.action === 'cancelBooking') {
        cancelBookingByUser(request.orderId).then(sendResponse);
        return true;
    }

    // 获取监控状态
    if (request.action === 'getMonitoringStatus') {
        getMonitoringStatus(request.orderId).then(sendResponse);
        return true;
    }
});

// 监控系统全局变量
let activeMonitorings = new Map(); // 存储活跃的监控任务
let monitoringIntervals = new Map(); // 存储监控定时器

// 启动预约监控服务
async function startBookingMonitoring(config) {
    try {
        console.log('启动预约监控服务:', config);

        const { orderId } = config;

        // 如果已经有该订单的监控，先停止
        if (activeMonitorings.has(orderId)) {
            console.log(`⚠️ 订单 ${orderId} 已有监控运行，先停止旧监控`);
            await stopBookingMonitoring(orderId);
        }

        // 🔧 关键修复：获取并保存当前店铺的完整认证上下文
        const authContext = await captureCurrentStoreAuthContext();
        if (!authContext || !authContext.isValid) {
            throw new Error('无法获取有效的店铺认证上下文，请确保已正确登录WB');
        }

        console.log(`🔐 [${new Date().toLocaleString()}] 已保存店铺认证上下文:`, {
            店铺ID: authContext.supplierId,
            外部店铺ID: authContext.supplierIdExternal,
            认证有效: authContext.isValid,
            店铺名称: authContext.supplierName || '未知'
        });

        // 保存监控配置（包含独立的认证上下文）
        activeMonitorings.set(orderId, {
            ...config,
            status: 'monitoring',
            startTime: new Date().toISOString(),
            lastCheckTime: null,
            checkCount: 0,
            // 🔧 新增：保存店铺特定的认证上下文
            storeAuthContext: authContext
        });

        // 更新持久化存储中的状态
        await updateBookingStateInStorage(orderId, 'booking', {
            startTime: new Date().toISOString(),
            config: config
        });

        // 启动轮询
        startPollingForOrder(orderId);

        console.log(`🚀 [${new Date().toLocaleString()}] 订单 ${orderId} 的监控服务已启动`, {
            监控目标: {
                订单ID: orderId,
                选择日期: config.selectedDates || [],
                缓冲天数: config.bufferDays || 0,
                最大系数: config.maxCoefficient || 20
            },
            监控频率: '每5秒-30秒随机间隔查询WB仓位',
            查询范围: '未来60天'
        });
        return { success: true, message: '监控服务启动成功' };

    } catch (error) {
        console.error('启动监控服务失败:', error);
        return { success: false, message: error.message };
    }
}

// 停止预约监控服务
async function stopBookingMonitoring(orderId) {
    try {
        console.log(`🛑 停止订单 ${orderId} 的监控服务`);

        // 由于可能存在ID映射问题（preorderId vs supplyId），我们需要查找所有可能的相关监控
        const relatedIds = new Set([orderId]);
        
        // 检查ID映射，找到所有相关的ID
        // 注意：background.js 是 service worker 环境，没有 window 对象
        // 暂时跳过ID映射检查，依靠强制清理机制

        console.log(`🔍 查找到相关ID: [${Array.from(relatedIds).join(', ')}]`);

        let clearedTimers = 0;
        let clearedConfigs = 0;

        // 清除所有相关ID的定时器和监控配置
        for (const id of relatedIds) {
            // 清除定时器
            if (monitoringIntervals.has(id)) {
                const timeoutId = monitoringIntervals.get(id);
                clearTimeout(timeoutId);
                monitoringIntervals.delete(id);
                console.log(`✅ 已清除ID ${id} 的定时器 (ID: ${timeoutId})`);
                clearedTimers++;
            }

            // 清除监控配置
            if (activeMonitorings.has(id)) {
                activeMonitorings.delete(id);
                console.log(`✅ 已移除ID ${id} 的监控配置`);
                clearedConfigs++;
            }
        }

        if (clearedTimers === 0 && clearedConfigs === 0) {
            console.log(`⚠️ 订单 ${orderId} 没有找到任何监控实例，尝试强制清理...`);
            
            // 强制清理：遍历所有活跃监控，查找匹配的preorderID
            const toRemove = [];
            for (const [key, monitoring] of activeMonitorings.entries()) {
                if (monitoring.preorderID === orderId || 
                    monitoring.orderId === orderId || 
                    key === orderId ||
                    String(monitoring.preorderID) === String(orderId) ||
                    String(key) === String(orderId)) {
                    toRemove.push(key);
                    console.log(`🔍 找到匹配的监控实例: Key=${key}, PreorderID=${monitoring.preorderID}`);
                }
            }
            
            for (const key of toRemove) {
                // 清除定时器
                if (monitoringIntervals.has(key)) {
                    const timeoutId = monitoringIntervals.get(key);
                    clearTimeout(timeoutId);
                    monitoringIntervals.delete(key);
                    console.log(`🔧 强制清除定时器: ${key} (ID: ${timeoutId})`);
                    clearedTimers++;
                }
                
                // 清除监控配置
                if (activeMonitorings.has(key)) {
                    activeMonitorings.delete(key);
                    console.log(`🔧 强制清除监控配置: ${key}`);
                    clearedConfigs++;
                }
            }
        }

        console.log(`🏁 订单 ${orderId} 监控清理完成 (清理了 ${clearedTimers} 个定时器, ${clearedConfigs} 个监控配置)`);
        console.log(`📊 当前活跃监控数量: ${activeMonitorings.size}, 当前定时器数量: ${monitoringIntervals.size}`);
        
        // 🚨 紧急修复：如果仍有监控残留，强制清理所有监控
        if (activeMonitorings.size > 0 || monitoringIntervals.size > 0) {
            console.warn(`🚨 检测到监控残留，执行紧急全局清理...`);
            
            // 清理所有定时器
            for (const [key, timeoutId] of monitoringIntervals.entries()) {
                clearTimeout(timeoutId);
                console.log(`🧹 紧急清理定时器: ${key} (ID: ${timeoutId})`);
            }
            monitoringIntervals.clear();
            
            // 清理所有监控配置
            activeMonitorings.clear();
            
            console.log(`✅ 紧急全局清理完成`);
        }
        
        // 如果清理后仍有监控实例，显示详情
        if (activeMonitorings.size > 0) {
            console.log(`🔍 剩余监控实例详情:`);
            for (const [key, monitoring] of activeMonitorings.entries()) {
                console.log(`  - Key: ${key}, PreorderID: ${monitoring.preorderID || 'N/A'}, Status: ${monitoring.status || 'N/A'}`);
            }
        }

        return { success: true, message: '监控服务已停止' };

    } catch (error) {
        console.error('停止监控服务失败:', error);
        return { success: false, message: error.message };
    }
}

// 用户手动取消预约（扩展版 - 同时处理本地监控和云端任务）
async function cancelBookingByUser(orderId) {
    try {
        console.log(`用户取消订单 ${orderId} 的预约`);

        // 1. 停止本地监控服务
        await stopBookingMonitoring(orderId);

        // 2. 尝试取消云端任务（如果有相关任务）
        let cloudCancelAttempted = false;
        try {
            // 获取监控配置中的taskId（如果存在）
            const monitoring = activeMonitorings.get(orderId);
            let taskId = monitoring?.taskId;
            
            // 如果没有直接的taskId，尝试通过preorderId查找
            if (!taskId) {
                console.log(`未找到直接的taskId，尝试通过orderId查找云端任务: ${orderId}`);
                
                // 在后台脚本中，我们无法直接调用WBAssistApiService
                // 但我们可以通过消息传递机制来处理
                // 这里记录需要取消的orderId，让前端处理
                await setStorage({
                    [`pending_cloud_cancel_${orderId}`]: {
                        orderId: orderId,
                        cancelRequested: true,
                        requestTime: new Date().toISOString()
                    }
                });
                
                console.log(`🔄 已标记orderId ${orderId} 需要取消云端任务`);
                cloudCancelAttempted = true;
            } else {
                // 如果有taskId，我们同样标记需要取消
                await setStorage({
                    [`pending_cloud_cancel_${orderId}`]: {
                        orderId: orderId,
                        taskId: taskId,
                        cancelRequested: true,
                        requestTime: new Date().toISOString()
                    }
                });
                
                console.log(`🔄 已标记taskId ${taskId} 需要取消云端任务`);
                cloudCancelAttempted = true;
            }
        } catch (cloudError) {
            console.error('标记云端任务取消失败:', cloudError);
            // 云端取消失败不影响本地取消
        }

        // 3. 更新持久化存储状态为未计划
        await updateBookingStateInStorage(orderId, 'not_planned', {
            cancelledAt: new Date().toISOString(),
            reason: 'user_cancelled',
            cloudCancelAttempted: cloudCancelAttempted
        });

        // 4. 通知前端状态变化
        notifyFrontendMonitoringResult(orderId, 'not_planned', {
            cancelledAt: new Date().toISOString(),
            reason: 'user_cancelled',
            cloudCancelAttempted: cloudCancelAttempted
        });

        return { 
            success: true, 
            message: cloudCancelAttempted ? 
                '本地预约已取消，云端任务取消请求已提交' : 
                '本地预约已取消'
        };

    } catch (error) {
        console.error('取消预约失败:', error);
        return { success: false, message: error.message };
    }
}

// 获取监控状态
async function getMonitoringStatus(orderId) {
    try {
        const monitoring = activeMonitorings.get(orderId);
        if (!monitoring) {
            return { success: false, message: '未找到监控任务' };
        }

        return {
            success: true,
            status: monitoring.status,
            config: monitoring,
            isActive: monitoringIntervals.has(orderId)
        };

    } catch (error) {
        console.error('获取监控状态失败:', error);
        return { success: false, message: error.message };
    }
}

// 为特定订单启动轮询
function startPollingForOrder(orderId) {
    const monitoring = activeMonitorings.get(orderId);
    if (!monitoring) return;



    // 立即执行一次检查
    performMonitoringCheck(orderId);

    // 设置不定时轮询（随机间隔，避免被检测）
    const scheduleNextCheck = () => {
        // 检查监控是否仍然活跃
        if (!activeMonitorings.has(orderId)) {
            console.log(`⚠️ 订单 ${orderId} 的监控已停止，取消调度`);
            return;
        }

        // 清除可能存在的旧定时器
        if (monitoringIntervals.has(orderId)) {
            clearTimeout(monitoringIntervals.get(orderId));
        }

        // 随机间隔：5秒到30秒之间
        const minInterval = 5 * 1000; // 5秒
        const maxInterval = 30 * 1000; // 30秒
        const randomInterval = Math.random() * (maxInterval - minInterval) + minInterval;

        // 🔍 调试信息：显示下次检查的时间
        const nextCheckTime = new Date(Date.now() + randomInterval);
        console.log(`⏰ [${new Date().toLocaleString()}] 订单 ${orderId} 已安排下次检查，间隔: ${Math.round(randomInterval/1000)}秒，下次检查时间: ${nextCheckTime.toLocaleString()}`);

        const timeoutId = setTimeout(() => {
            performMonitoringCheck(orderId);
            scheduleNextCheck(); // 递归调度下一次检查
        }, randomInterval);

        // 保存timeout ID以便后续清理
        monitoringIntervals.set(orderId, timeoutId);
    };

    scheduleNextCheck();
}

// 执行监控检查
async function performMonitoringCheck(orderId) {
    try {
        const monitoring = activeMonitorings.get(orderId);
        if (!monitoring) {
            console.log(`⚠️ 订单 ${orderId} 的监控已停止，跳过检查`);
            return;
        }

        // 检查是否达到任务停止时间（根据缓冲天数计算）
        if (monitoring.stopTime && new Date() > new Date(monitoring.stopTime)) {
            console.log(`🛑 [${new Date().toLocaleString()}] 订单 ${orderId} 的监控已达到停止时间 (缓冲天数)，任务终止。`);
            
            // 上报任务失败到后端API
            if (monitoring.taskId) {
                try {
                    const apiService = new WBAssistApiService();
                    await apiService.initializeFromWebsite();
                    
                    if (apiService.isAuthenticated) {
                        await apiService.reportTaskFailure(
                            monitoring.taskId,
                            'failed',
                            {
                                error_code: 'BUFFER_DAYS_TIMEOUT',
                                error_message: '已达到缓冲天数设置的停止时间，任务自动终止',
                                timeout_at: new Date().toISOString(),
                                buffer_days: monitoring.bufferDays || 0
                            }
                        );
                        console.log(`✅ 缓冲天数超时失败已上报: ${monitoring.taskId}`);
                    }
                } catch (error) {
                    console.error(`❌ 上报缓冲天数超时失败失败:`, error);
                }
            }
            
            await stopBookingMonitoring(orderId);
            notifyFrontendMonitoringResult(orderId, 'failed', {
                error: '已达到缓冲天数设置的停止时间，任务自动终止。',
                reason: 'buffer_days_timeout'
            });
            return;
        }

        // 🔍 调试信息：监控检查开始
        const currentTime = new Date();
        const lastCheckTime = monitoring.lastCheckTime ? new Date(monitoring.lastCheckTime) : null;
        const actualInterval = lastCheckTime ? Math.round((currentTime - lastCheckTime) / 1000) : 0;
        
        // 更新检查统计
        monitoring.lastCheckTime = currentTime.toISOString();
        monitoring.checkCount++;
        
        console.log(`🔄 [${currentTime.toLocaleString()}] 执行第${monitoring.checkCount}次监控检查 - 订单 ${orderId}`, {
            监控配置: {
                选择日期: monitoring.selectedDates || [],
                缓冲天数: monitoring.bufferDays || 0,
                最大系数: monitoring.maxCoefficient || 20
            },
            上次检查: lastCheckTime ? lastCheckTime.toLocaleString() : '无',
            实际间隔: actualInterval > 0 ? `${actualInterval}秒` : '首次检查'
        });

        // 🔧 关键修复：使用任务特定的认证上下文而非当前认证数据
        const storeAuthContext = monitoring.storeAuthContext;
        const validation = validateStoreAuthContext(storeAuthContext);
        if (!validation.valid) {
            throw new Error(`订单 ${orderId} 的店铺认证上下文无效: ${validation.reason}`);
        }

        console.log(`🔐 [${new Date().toLocaleString()}] 使用任务专用认证上下文:`, {
            任务订单ID: orderId,
            绑定店铺ID: storeAuthContext.supplierId,
            绑定店铺名称: storeAuthContext.supplierName || '未知',
            上下文捕获时间: storeAuthContext.capturedAt
        });

        // 调用WB API获取费用数据（使用任务特定的认证上下文）
        const costsData = await getAcceptanceCostsForMonitoring(
            orderId,
            storeAuthContext
        );

        if (!costsData || !costsData.costs) {
            console.warn(`⚠️ [${new Date().toLocaleString()}] 订单 ${orderId} 无法获取费用数据`);
            return;
        }

        // 应用用户筛选条件
        const matchingSlots = filterCostsByUserConditions(costsData.costs, monitoring);
        
        console.log(`🎯 [${new Date().toLocaleString()}] 筛选后符合条件的时段: ${matchingSlots.length}个`, 
            matchingSlots.length > 0 ? matchingSlots.slice(0, 3).map(slot => ({
                日期: new Date(slot.date).toLocaleDateString('zh-CN'),
                系数: slot.coefficient
            })) : []);
        

        if (matchingSlots.length > 0) {

            // 选择最优时段（免费优先，然后按系数排序）
            const optimalSlot = matchingSlots[0];

            // 更新监控状态
            monitoring.status = 'found';
            monitoring.optimalSlot = optimalSlot;

            // 停止监控
            await stopBookingMonitoring(orderId);

            // 立即发起自动预约，传递用户筛选条件
            const userFilterCriteria = {
                selectedDates: monitoring.selectedDates || [],
                bufferDays: monitoring.bufferDays || 0,
                maxCoefficient: monitoring.maxCoefficient || 20
            };
            const bookingResult = await autoBookSupplyDelivery(orderId, optimalSlot.date, userFilterCriteria);

            // 通知前端结果
            const finalStatus = bookingResult.success ? 'scheduled' : 'failed';
            
            // 提取实际预约的日期
            let actualBookedDate = optimalSlot.date; // 默认使用最优时段的日期
            
            console.log(`🔍 [${new Date().toLocaleString()}] 提取预约日期信息:`, {
                optimalSlot_date: optimalSlot.date,
                bookingResult_success: bookingResult.success,
                bookingResult_selectedDate: bookingResult.selectedDate
            });
            
            if (bookingResult.success && bookingResult.selectedDate) {
                // 尝试从selectedDate中提取日期信息
                if (bookingResult.selectedDate.parsedDate) {
                    actualBookedDate = bookingResult.selectedDate.parsedDate.toISOString();
                    console.log(`✅ [${new Date().toLocaleString()}] 使用解析后的日期:`, actualBookedDate);
                } else if (bookingResult.selectedDate.text && bookingResult.selectedDate.text !== '未知日期') {
                    // 从text中解析日期
                    console.log(`⚠️ [${new Date().toLocaleString()}] 尝试从selectedDate.text解析日期:`, bookingResult.selectedDate.text);
                }
            } else {
                console.log(`⚠️ [${new Date().toLocaleString()}] 使用默认的最优时段日期:`, actualBookedDate);
            }
            
            // 确保bookingResult中也有bookedDate字段
            const enhancedBookingResult = {
                ...bookingResult,
                bookedDate: actualBookedDate
            };

            // 🚨 关键修复：预约成功时立即上报给后端API进行配额扣减
            if (bookingResult.success) {
                console.log(`🎯 [${new Date().toLocaleString()}] 预约成功，开始上报给后端API进行配额扣减...`);
                
                try {
                    // 从监控配置中获取taskId（这应该在启动监控时传入）
                    const taskId = monitoring.taskId;
                    
                    if (!taskId) {
                        console.error('❌ 监控配置中缺少taskId，无法上报成功结果');
                        enhancedBookingResult.backendReported = false;
                        enhancedBookingResult.quotaDeducted = false;
                        enhancedBookingResult.errorMessage = '缺少taskId，无法上报';
                    } else {
                        // 获取supplyId（预约成功页面的"送货单号: XXX"）
                        let supplyId = bookingResult.data?.newSupplyId || bookingResult.data?.supplyId;
                        
                        // 🔧 确保supplyId是字符串格式（API验证需要）
                        if (supplyId) {
                            supplyId = String(supplyId);
                        }
                        
                        // 如果没有从预约结果获取到supplyId，尝试从WB API获取
                        if (!supplyId) {
                            // 🔧 实现重试机制：多次尝试获取supplyId
                            const maxRetries = 5;
                            const retryInterval = 3000; // 3秒间隔
                            
                            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                                try {
                                    console.log(`🔍 [${attempt}/${maxRetries}] 尝试从WB API获取supplyId，orderId: ${orderId}`);
                                    
                                    // 首先尝试从映射存储中查找
                                    supplyId = await getSupplyIdFromMapping(orderId);
                                    if (supplyId) {
                                        supplyId = String(supplyId); // 🔧 确保是字符串格式
                                        console.log(`✅ [${attempt}/${maxRetries}] 从映射存储找到supplyId: ${supplyId}`);
                                        break;
                                    }
                                    
                                    // 如果映射存储中没有，则从API获取并智能匹配
                                    const orders = await getAllOrdersFromWBNewAPI();
                                    console.log(`📊 [${attempt}/${maxRetries}] 获取到 ${orders.length} 个订单`);
                                    
                                    // 智能查找：使用转换检测逻辑
                                    const conversionResult = detectOrderConversion(null, orders, orderId);
                                    
                                    if (conversionResult.found && conversionResult.order) {
                                        const foundOrder = conversionResult.order;
                                        supplyId = String(foundOrder.supplyId);  // 🔧 确保是字符串格式
                                        
                                        console.log(`✅ [${attempt}/${maxRetries}] 通过智能检测找到supplyId: ${supplyId}`);
                                        console.log(`🎯 检测方法: ${conversionResult.matchMethod}`);
                                        console.log(`🔍 置信度: ${conversionResult.confidence}`);
                                        
                                        // 保存映射关系供下次使用
                                        if (supplyId && conversionResult.conversionStatus === 'converted') {
                                            await saveOrderConversionMapping(orderId, supplyId, foundOrder);
                                        }
                                        break;
                                    } else {
                                        // 备用方案：直接查找（适用于状态还未完全转换的情况）
                                        const directMatch = orders.find(order => 
                                            order.preorderId && order.preorderId.toString() === orderId.toString()
                                        );
                                        if (directMatch) {
                                            supplyId = directMatch.supplyId ? String(directMatch.supplyId) : null;  // 🔧 确保是字符串格式
                                            if (supplyId) {
                                                console.log(`✅ [${attempt}/${maxRetries}] 通过直接匹配找到supplyId: ${supplyId}`);
                                                break;
                                            }
                                        }
                                        
                                        console.log(`❌ [${attempt}/${maxRetries}] 未找到supplyId，转换检测结果:`, conversionResult);
                                    }
                                    
                                    // 如果不是最后一次尝试，等待后再试
                                    if (attempt < maxRetries) {
                                        console.log(`⏳ [${attempt}/${maxRetries}] 等待${retryInterval}ms后重试...`);
                                        await new Promise(resolve => setTimeout(resolve, retryInterval));
                                    }
                                    
                                } catch (error) {
                                    console.warn(`❌ [${attempt}/${maxRetries}] 从WB API获取supplyId失败:`, error);
                                    
                                    // 如果不是最后一次尝试，等待后再试
                                    if (attempt < maxRetries) {
                                        await new Promise(resolve => setTimeout(resolve, retryInterval));
                                    }
                                }
                            }
                            
                            if (supplyId) {
                                console.log(`🎉 最终成功获取到supplyId: ${supplyId}`);
                            } else {
                                console.error(`💥 重试${maxRetries}次后仍无法获取supplyId，orderId: ${orderId}`);
                            }
                        }

                        // 🛡️ 双重保障机制：即使没有完美的supplyId，也要尝试上报确保配额扣减
                        if (!supplyId) {
                            console.warn('⚠️ 无法获取完美supplyId，启用备用上报机制确保配额扣减');
                            
                            // 🛡️ 备用上报：使用FALLBACK_前缀 + preorderId
                            const fallbackSupplyId = `FALLBACK_${orderId}`;
                            const fallbackReason = '页面脚本未获取到supplyId但API检测到预约成功';
                            
                            try {
                                // 创建API服务实例并初始化
                                const apiService = new WBAssistApiService();
                                await apiService.initializeFromWebsite();
                                
                                if (!apiService.isAuthenticated) {
                                    console.warn('⚠️ 用户未认证，无法执行备用上报');
                                    enhancedBookingResult.backendReported = false;
                                    enhancedBookingResult.quotaDeducted = false;
                                    enhancedBookingResult.warningMessage = '用户未认证，配额未扣减';
                                } else {
                                    // 🛡️ 执行备用上报
                                    const fallbackBookingDetails = {
                                        booked_date: actualBookedDate.split('T')[0], // YYYY-MM-DD格式
                                        booked_time_slot: bookingResult.data?.selectedDate?.text || '已预约',
                                        booking_coefficient: optimalSlot.coefficient || 0,
                                        fallback_mode: true,
                                        fallback_reason: fallbackReason,
                                        original_preorder_id: orderId
                                    };
                                    
                                    console.log(`🛡️ 执行备用上报:`, {
                                        taskId,
                                        fallbackSupplyId,
                                        fallbackReason,
                                        originalPreorderId: orderId
                                    });
                                    
                                    const fallbackResult = await apiService.reportTaskSuccess(
                                        taskId,
                                        fallbackSupplyId, // 使用FALLBACK_前缀的supplyId
                                        new Date().toISOString(),
                                        fallbackBookingDetails
                                    );
                                    
                                    console.log(`✅ 备用上报成功:`, fallbackResult);
                                    
                                    // 更新结果状态
                                    enhancedBookingResult.backendReported = true;
                                    enhancedBookingResult.quotaDeducted = fallbackResult.quota_deducted || false;
                                    enhancedBookingResult.remainingQuota = fallbackResult.remaining_quota;
                                    enhancedBookingResult.taskId = taskId;
                                    enhancedBookingResult.supplyId = fallbackSupplyId;
                                    enhancedBookingResult.preorderId = monitoring.preorderId || orderId;
                                    enhancedBookingResult.fallbackMode = true;
                                    enhancedBookingResult.fallbackReason = fallbackReason;
                                }
                            } catch (fallbackError) {
                                console.error('❌ 备用上报也失败了:', fallbackError);
                                enhancedBookingResult.backendReported = false;
                                enhancedBookingResult.quotaDeducted = false;
                                enhancedBookingResult.errorMessage = `主要上报失败: 无法获取supplyId; 备用上报失败: ${fallbackError.message}`;
                            }
                        } else {
                            // 创建API服务实例并初始化
                            const apiService = new WBAssistApiService();
                            await apiService.initializeFromWebsite();
                            
                            if (!apiService.isAuthenticated) {
                                console.warn('⚠️ 用户未认证，无法上报预约成功');
                                enhancedBookingResult.backendReported = false;
                                enhancedBookingResult.quotaDeducted = false;
                                enhancedBookingResult.warningMessage = '用户未认证，配额未扣减';
                            } else {
                                // 构造预约详情
                                const bookingDetails = {
                                    booked_date: actualBookedDate.split('T')[0], // YYYY-MM-DD格式
                                    booked_time_slot: bookingResult.data?.selectedDate?.text || '已预约',
                                    booking_coefficient: optimalSlot.coefficient || 0
                                };
                                
                                console.log(`📊 准备上报预约成功:`, {
                                    taskId,
                                    supplyId,
                                    actualBookedDate,
                                    bookingDetails
                                });
                                
                                // 上报任务成功
                                const reportResult = await apiService.reportTaskSuccess(
                                    taskId,
                                    supplyId,  // 这是预约成功页面的"送货单号: XXX"
                                    new Date().toISOString(),
                                    bookingDetails
                                );
                                
                                console.log(`✅ [${new Date().toLocaleString()}] 预约成功已上报，配额已扣减:`, reportResult);
                                
                                // 更新增强结果
                                enhancedBookingResult.backendReported = true;
                                enhancedBookingResult.quotaDeducted = reportResult.quota_deducted || false;
                                enhancedBookingResult.remainingQuota = reportResult.remaining_quota;
                                enhancedBookingResult.taskId = taskId;
                                enhancedBookingResult.supplyId = supplyId;
                                enhancedBookingResult.preorderId = monitoring.preorderId || orderId;
                            }
                        }
                    }
                    
                } catch (error) {
                    console.error(`❌ [${new Date().toLocaleString()}] 上报预约成功失败:`, error);
                    enhancedBookingResult.backendReported = false;
                    enhancedBookingResult.quotaDeducted = false;
                    enhancedBookingResult.errorMessage = `上报失败: ${error.message}`;
                    
                    // 记录严重错误
                    console.error(`🚨 严重警告：预约成功但配额扣减失败！订单ID: ${orderId}, TaskID: ${monitoring.taskId}`);
                }
            }
            
            // 无论成功还是失败，都停止监控
            await stopBookingMonitoring(orderId);
            
            notifyFrontendMonitoringResult(orderId, finalStatus, {
                optimalSlot,
                bookingResult: enhancedBookingResult,
                completedAt: new Date().toISOString(),
                newSupplyId: bookingResult.data?.newSupplyId, // 传递新的supplyId
                bookedDate: actualBookedDate,
                coefficient: optimalSlot.coefficient
            });
        }

    } catch (error) {
        console.error(`订单 ${orderId} 监控检查失败:`, error);

        // 上报任务失败到后端API
        const monitoring = activeMonitorings.get(orderId);
        if (monitoring && monitoring.taskId) {
            try {
                const apiService = new WBAssistApiService();
                await apiService.initializeFromWebsite();
                
                if (apiService.isAuthenticated) {
                    let errorCode = 'MONITORING_ERROR';
                    let status = 'failed';
                    
                    // 根据错误类型确定错误码和状态
                    if (error.message.includes('认证') || error.message.includes('401')) {
                        errorCode = 'AUTH_FAILED';
                    } else if (error.message.includes('API调用失败')) {
                        errorCode = 'WB_API_ERROR';
                    } else if (error.message.includes('网络')) {
                        errorCode = 'NETWORK_ERROR';
                    }
                    
                    await apiService.reportTaskFailure(
                        monitoring.taskId,
                        status,
                        {
                            error_code: errorCode,
                            error_message: error.message,
                            failed_at: new Date().toISOString(),
                            monitoring_check_count: monitoring.checkCount || 0
                        }
                    );
                    console.log(`✅ 监控失败已上报: ${monitoring.taskId}`);
                }
            } catch (reportError) {
                console.error(`❌ 上报监控失败失败:`, reportError);
            }
        }

        // 如果是认证错误，停止监控
        if (error.message.includes('认证') || error.message.includes('401')) {
            await stopBookingMonitoring(orderId);
            notifyFrontendMonitoringResult(orderId, 'failed', { error: error.message });
        }
    }
}

// 🔧 新增：验证认证上下文是否仍然有效
function validateStoreAuthContext(storeAuthContext) {
    if (!storeAuthContext) {
        return { valid: false, reason: '认证上下文不存在' };
    }

    if (!storeAuthContext.isValid) {
        return { 
            valid: false, 
            reason: `认证上下文已失效: ${storeAuthContext.invalidationReason || '未知原因'}`,
            invalidatedAt: storeAuthContext.invalidatedAt
        };
    }

    // 检查必要字段
    const requiredFields = ['authToken', 'wbxValidKey', 'supplierId', 'supplierIdExternal', 'fullCookieString'];
    for (const field of requiredFields) {
        if (!storeAuthContext[field]) {
            return { valid: false, reason: `缺少必要字段: ${field}` };
        }
    }

    // 检查时效性（认证上下文超过24小时考虑过期）
    if (storeAuthContext.capturedAt) {
        const capturedTime = new Date(storeAuthContext.capturedAt);
        const now = new Date();
        const ageHours = (now - capturedTime) / (1000 * 60 * 60);
        
        if (ageHours > 24) {
            return { 
                valid: false, 
                reason: `认证上下文已过期 (${Math.round(ageHours)}小时前捕获)`,
                ageHours: ageHours
            };
        }
    }

    return { valid: true };
}

// 🔧 新增：获取当前店铺的完整认证上下文
async function captureCurrentStoreAuthContext() {
    try {
        // 获取认证基础数据
        const authData = await getStorage(['Authorizev3', 'wbxValidKey', 'currentSupplierId', 'wbSuppliersList']);
        
        if (!authData.Authorizev3 || !authData.wbxValidKey) {
            console.error('❌ 缺少基础认证信息');
            return { isValid: false, error: '缺少基础认证信息' };
        }

        // 获取当前选中的店铺ID
        let currentSupplierId = authData.currentSupplierId;
        let supplierName = null;
        let supplierIdExternal = null;

        // 如果没有currentSupplierId，尝试从供应商列表获取
        if (!currentSupplierId && authData.wbSuppliersList && authData.wbSuppliersList.suppliers) {
            const suppliers = authData.wbSuppliersList.suppliers;
            if (suppliers.length > 0) {
                currentSupplierId = suppliers[0].id;
                supplierName = suppliers[0].name;
                console.log(`📋 使用第一个可用店铺: ${currentSupplierId} (${supplierName})`);
            }
        } else if (currentSupplierId && authData.wbSuppliersList && authData.wbSuppliersList.suppliers) {
            // 查找当前店铺的详细信息
            const currentSupplier = authData.wbSuppliersList.suppliers.find(
                supplier => supplier.id === currentSupplierId
            );
            if (currentSupplier) {
                supplierName = currentSupplier.name;
            }
        }

        if (!currentSupplierId) {
            console.error('❌ 无法确定当前店铺ID');
            return { isValid: false, error: '无法确定当前店铺ID' };
        }

        // 🔧 关键修复：获取完整的Cookie字符串（与当前店铺关联）
        // 从浏览器Cookie中获取店铺特定的cookies
        let supplierCookies = '';
        try {
            const domains = ['.wildberries.ru', '.seller.wildberries.ru'];
            const cookieNames = ['x-supplier-id', 'x-supplier-id-external'];
            
            for (const domain of domains) {
                for (const name of cookieNames) {
                    try {
                        const cookies = await chrome.cookies.getAll({ domain, name });
                        if (cookies.length > 0) {
                            const cookie = cookies[0];
                            if (cookie.value === currentSupplierId) {
                                supplierCookies += `${name}=${cookie.value}; `;
                                if (name === 'x-supplier-id-external') {
                                    supplierIdExternal = cookie.value;
                                }
                            }
                        }
                    } catch (cookieError) {
                        // 静默处理Cookie获取错误
                    }
                }
            }
        } catch (error) {
            console.warn('⚠️ 获取店铺Cookies失败，将使用存储中的店铺ID');
        }

        // 如果没有从Cookie获取到外部店铺ID，使用当前店铺ID作为默认值
        if (!supplierIdExternal) {
            supplierIdExternal = currentSupplierId;
        }

        // 构建完整的认证上下文
        const authContext = {
            // 基础认证
            authToken: authData.Authorizev3,
            wbxValidKey: authData.wbxValidKey,
            
            // 店铺信息
            supplierId: currentSupplierId,
            supplierIdExternal: supplierIdExternal,
            supplierName: supplierName,
            
            // 完整Cookie字符串（用于API请求）
            fullCookieString: `external-locale=zh; locale=zh; ${authData.wbxValidKey}; x-supplier-id=${currentSupplierId}; x-supplier-id-external=${supplierIdExternal}`,
            
            // 上下文元信息
            capturedAt: new Date().toISOString(),
            isValid: true
        };

        console.log(`✅ 成功捕获店铺认证上下文`, {
            店铺ID: authContext.supplierId,
            外部店铺ID: authContext.supplierIdExternal,
            店铺名称: authContext.supplierName,
            认证令牌前缀: authContext.authToken.substring(0, 20) + '...'
        });

        return authContext;

    } catch (error) {
        console.error('❌ 捕获认证上下文失败:', error);
        return { isValid: false, error: error.message };
    }
}

// 为监控获取费用数据（使用任务特定的认证上下文）
async function getAcceptanceCostsForMonitoring(preorderID, storeAuthContext) {
    try {
        // 🔧 关键修复：验证认证上下文有效性
        const validation = validateStoreAuthContext(storeAuthContext);
        if (!validation.valid) {
            throw new Error(`无效的店铺认证上下文: ${validation.reason}`);
        }

        // 获取未来60天的费用数据（比用户选择的范围更大）
        const today = new Date();
        const futureDate = new Date(today.getTime() + 60 * 24 * 60 * 60 * 1000);

        // 🔧 关键修复：使用任务特定的认证上下文而非当前店铺的认证信息
        const cookieString = storeAuthContext.fullCookieString;

        const requestBody = {
            params: {
                dateFrom: today.toISOString(),
                dateTo: futureDate.toISOString(),
                preorderID: preorderID
            },
            jsonrpc: "2.0",
            id: `json-rpc_${Date.now()}`
        };

        // 🔍 调试信息：发送WB仓位查询请求（显示使用的店铺认证上下文）
        console.log(`🔍 [${new Date().toLocaleString()}] 向WB发送仓位查询请求:`, {
            preorderID: preorderID,
            dateRange: `${today.toISOString().split('T')[0]} 到 ${futureDate.toISOString().split('T')[0]}`,
            使用店铺ID: storeAuthContext.supplierId,
            外部店铺ID: storeAuthContext.supplierIdExternal,
            店铺名称: storeAuthContext.supplierName || '未知',
            认证上下文捕获时间: storeAuthContext.capturedAt,
            url: 'https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/getAcceptanceCosts'
        });

        const response = await fetch('https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/getAcceptanceCosts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'authorizev3': storeAuthContext.authToken,  // 🔧 使用任务特定的认证令牌
                'Cookie': cookieString,
                'origin': 'https://seller.wildberries.ru',
                'referer': 'https://seller.wildberries.ru/',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            console.log(`❌ [${new Date().toLocaleString()}] WB API调用失败: ${response.status} ${response.statusText}`);
            throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.error) {
            console.log(`❌ [${new Date().toLocaleString()}] WB API返回错误:`, data.error);
            
            // 🔧 检查是否是认证相关错误，如果是则标记认证上下文为无效
            if (data.error.code === 401 || data.error.code === 403 || 
                (data.error.message && data.error.message.includes('auth')) ||
                (data.error.message && data.error.message.includes('权限'))) {
                
                console.error(`🚨 检测到认证错误，标记店铺认证上下文为无效:`, {
                    店铺ID: storeAuthContext.supplierId,
                    错误信息: data.error.message || data.error.code
                });
                
                // 标记认证上下文为无效（但不删除，以便调试）
                storeAuthContext.isValid = false;
                storeAuthContext.invalidatedAt = new Date().toISOString();
                storeAuthContext.invalidationReason = `WB API认证错误: ${data.error.message || data.error.code}`;
            }
            
            throw new Error(`API返回错误: ${data.error.message || data.error.code}`);
        }

        // 🔍 调试信息：分析返回的仓位数据
        const costs = data.result?.costs || [];
        const availableSlots = costs.filter(cost => cost.coefficient !== null && cost.coefficient !== undefined);
        const freeSlots = availableSlots.filter(cost => cost.coefficient === 0);
        const paidSlots = availableSlots.filter(cost => cost.coefficient > 0);

        console.log(`✅ [${new Date().toLocaleString()}] WB仓位查询结果:`, {
            总共时段: costs.length,
            可用时段: availableSlots.length,
            免费时段: freeSlots.length,
            收费时段: paidSlots.length,
            收费系数范围: paidSlots.length > 0 ? `${Math.min(...paidSlots.map(s => s.coefficient))} - ${Math.max(...paidSlots.map(s => s.coefficient))}` : 'N/A'
        });

        if (availableSlots.length > 0) {
            const sampleSlots = availableSlots.slice(0, 5).map(slot => ({
                日期: new Date(slot.date).toLocaleDateString('zh-CN'),
                系数: slot.coefficient,
                价格: slot.price || 'N/A'
            }));
            console.log(`📅 [${new Date().toLocaleString()}] 前5个可用时段样本:`, sampleSlots);
        } else {
            console.log(`⚠️ [${new Date().toLocaleString()}] 当前没有找到任何可用的仓位时段`);
        }

        return data.result;

    } catch (error) {
        console.error('获取费用数据失败:', error);
        throw error;
    }
}

// 根据用户条件筛选费用数据
function filterCostsByUserConditions(costs, monitoring) {
    const { selectedDates, bufferDays, maxCoefficient } = monitoring;
    
    // 🔍 调试信息：显示筛选条件
    console.log(`🔍 [${new Date().toLocaleString()}] 开始筛选仓位，条件:`, {
        选择日期: selectedDates,
        缓冲天数: bufferDays,
        最大系数: maxCoefficient,
        原始数据量: costs.length
    });



    const filteredSlots = costs.filter((cost, index) => {
        // 1. 排除不可用日期 (coefficient: -1)
        if (cost.coefficient === -1) {
            if (index < 10) console.log(`❌ 跳过不可用日期: ${new Date(cost.date).toLocaleDateString('zh-CN')}, 系数: ${cost.coefficient}`);
            return false;
        }

        // 2. 应用系数上限筛选
        if (cost.coefficient > maxCoefficient) {
            if (index < 10) console.log(`❌ 系数过高: ${new Date(cost.date).toLocaleDateString('zh-CN')}, 系数: ${cost.coefficient} > ${maxCoefficient}`);
            return false;
        }

        // 3. 检查是否在用户选择的目标日期范围内（严格匹配）
        const costDate = new Date(cost.date);
        const isInDateRange = selectedDates.some(selectedDate => {
            const targetDate = new Date(selectedDate);
            if (isSameDay(costDate, targetDate)) {
                if (index < 10) console.log(`✅ 日期匹配: ${costDate.toLocaleDateString('zh-CN')} = ${targetDate.toLocaleDateString('zh-CN')}, 系数: ${cost.coefficient}`);
                return true;
            }
            return false;
        });
        
        if (!isInDateRange && index < 10) {
            console.log(`❌ 日期不匹配: ${costDate.toLocaleDateString('zh-CN')}, 系数: ${cost.coefficient}`);
        }
        
        return isInDateRange;
    });

    // 按优先级排序（免费优先，然后按系数从低到高）
    const sortedSlots = filteredSlots.sort((a, b) => {
        if (a.coefficient === 0 && b.coefficient !== 0) return -1;
        if (a.coefficient !== 0 && b.coefficient === 0) return 1;
        return a.coefficient - b.coefficient;
    });

    console.log(`🎯 [${new Date().toLocaleString()}] 筛选完成，结果: ${sortedSlots.length}个时段`, 
        sortedSlots.slice(0, 3).map(slot => ({
            日期: new Date(slot.date).toLocaleDateString('zh-CN'),
            系数: slot.coefficient
        })));

    return sortedSlots;
}

// 检查两个日期是否为同一天
function isSameDay(date1, date2) {
    return date1.toDateString() === date2.toDateString();
}

// 从存储中获取预约配置
async function getBookingConfigFromStorage(orderId) {
    try {
        // 使用与BookingStorageService相同的键名和逻辑
        const result = await chrome.storage.local.get(['wb_booking_configs']);
        const configs = result['wb_booking_configs'] || {};
        const config = configs[String(orderId)] || null;
        
        console.log(`尝试获取订单 ${orderId} 的配置:`, config);
        console.log('所有可用配置:', Object.keys(configs));
        
        return config;
    } catch (error) {
        console.error('获取预约配置失败:', error);
        return null;
    }
}

// 更新持久化存储中的预约状态
async function updateBookingStateInStorage(orderId, status, additionalData = {}) {
    try {
        const storageKey = 'wb_booking_states';
        const result = await getStorage([storageKey]);
        const states = result[storageKey] || {};

        const key = String(orderId);
        const timestamp = new Date().toISOString();

        // 如果是更新现有状态，保留历史信息
        const existingState = states[key];
        const stateData = {
            orderId: orderId,
            status: status,
            timestamp: timestamp,
            lastUpdated: timestamp,
            ...additionalData
        };

        if (existingState) {
            stateData.startTime = existingState.startTime || timestamp;
            stateData.history = existingState.history || [];
            stateData.history.push({
                status: existingState.status,
                timestamp: existingState.lastUpdated || existingState.timestamp
            });
        } else {
            stateData.startTime = timestamp;
            stateData.history = [];
        }

        states[key] = stateData;
        await setStorage({ [storageKey]: states });



    } catch (error) {
        console.error('更新预约状态到存储失败:', error);
    }
}

// 通知前端监控结果
async function notifyFrontendMonitoringResult(orderId, status, data = {}) {
    try {
        // 首先更新持久化存储
        await updateBookingStateInStorage(orderId, status, data);

        // 获取所有标签页
        const tabs = await chrome.tabs.query({});

        // 向所有包含插件页面的标签页发送消息
        for (const tab of tabs) {
            if (tab.url && tab.url.includes(chrome.runtime.id)) {
                try {
                    await chrome.tabs.sendMessage(tab.id, {
                        action: 'monitoringStatusChanged',
                        orderId: orderId,
                        status: status,
                        data: data
                    });
                } catch (error) {
                    // 静默处理发送失败的情况
                }
            }
        }



    } catch (error) {
        console.error('通知前端失败:', error);
    }
}

// 点击扩展图标时的处理
// 查找现有的插件标签页
async function findExistingPluginTab() {
    try {
        const pluginUrl = chrome.runtime.getURL('index.html');
        const tabs = await chrome.tabs.query({
            url: pluginUrl
        });

        // 过滤掉可能已经关闭或无效的标签页
        const validTabs = [];
        for (const tab of tabs) {
            try {
                // 尝试获取标签页信息来验证其有效性
                const tabInfo = await chrome.tabs.get(tab.id);
                if (tabInfo && tabInfo.url === pluginUrl) {
                    validTabs.push(tabInfo);
                }
            } catch (tabError) {
                // 标签页已关闭或无效，跳过
                console.log(`标签页 ${tab.id} 无效或已关闭`);
            }
        }

        return validTabs.length > 0 ? validTabs[0] : null;
    } catch (error) {
        console.error('查找插件标签页失败:', error);
        return null;
    }
}

// 创建或激活插件标签页
async function createOrActivatePluginTab() {
    try {
        // 首先查找现有的插件标签页
        const existingTab = await findExistingPluginTab();

        if (existingTab) {
            console.log('找到现有插件标签页，激活它:', existingTab.id);
            
            // 激活现有标签页
            await chrome.tabs.update(existingTab.id, { active: true });
            
            // 聚焦到该标签页所在的窗口
            try {
                await chrome.windows.update(existingTab.windowId, { focused: true });
            } catch (windowError) {
                console.log('聚焦窗口失败，可能窗口已关闭:', windowError.message);
                // 如果窗口聚焦失败，可能窗口已关闭，重新创建标签页
                throw new Error('窗口不存在，需要创建新标签页');
            }
            
            return existingTab;
        } else {
            console.log('未找到现有插件标签页，创建新的');
            
            // 获取当前活动窗口，在其中创建新标签页
            let targetWindowId = null;
            try {
                const currentWindow = await chrome.windows.getCurrent({ populate: false });
                if (currentWindow && currentWindow.focused) {
                    targetWindowId = currentWindow.id;
                }
            } catch (windowError) {
                console.log('获取当前窗口失败，将在默认窗口中创建标签页');
            }
            
            // 创建新的插件标签页
            const createOptions = {
                url: chrome.runtime.getURL('index.html'),
                active: true
            };
            
            if (targetWindowId) {
                createOptions.windowId = targetWindowId;
            }
            
            const newTab = await chrome.tabs.create(createOptions);
            
            return newTab;
        }
    } catch (error) {
        console.error('创建或激活插件标签页时出错:', error);
        
        // 如果所有尝试都失败，作为最后的回退，简单创建一个新标签页
        try {
            console.log('尝试简单创建新标签页作为回退方案');
            const fallbackTab = await chrome.tabs.create({
                url: chrome.runtime.getURL('index.html'),
                active: true
            });
            return fallbackTab;
        } catch (fallbackError) {
            console.error('回退方案也失败了:', fallbackError);
            throw error;
        }
    }
}

chrome.action.onClicked.addListener(async () => {
    try {
        console.log('用户点击了扩展图标');
        
        const pluginTab = await createOrActivatePluginTab();

        // 后台静默认证检查
        setTimeout(async () => {
            try {
                const authResult = await checkAuth();

                // 验证标签页仍然有效后再发送认证结果
                try {
                    // 先检查标签页是否仍然存在
                    const currentTab = await chrome.tabs.get(pluginTab.id);
                    if (currentTab && currentTab.url === chrome.runtime.getURL('index.html')) {
                        await chrome.tabs.sendMessage(pluginTab.id, {
                            action: 'authResult',
                            result: authResult
                        });
                        console.log('认证结果已发送到插件标签页');
                    } else {
                        console.log('插件标签页已关闭或导航到其他页面，跳过发送认证结果');
                    }
                } catch (messageError) {
                    if (messageError.message.includes('tab') || messageError.message.includes('closed')) {
                        console.log('插件标签页已关闭，无法发送认证结果');
                    } else {
                        console.log('发送认证结果失败，可能插件页面还未完全加载:', messageError.message);
                    }
                }
            } catch (authError) {
                console.error('后台认证检查失败:', authError);
            }
        }, 1000);

    } catch (error) {
        console.error('启动插件失败:', error);

        // 如果创建或激活标签页失败，尝试显示错误通知
        try {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'assets/icons/48.png',
                title: 'WBAssist 启动失败',
                message: '无法启动插件，请检查扩展权限设置'
            });
        } catch (notificationError) {
            console.error('显示错误通知也失败了:', notificationError);
        }
    }
});

// WBAssist API服务类 - 后台脚本专用版本
class WBAssistApiService {
    constructor() {
        this.supabaseUrl = 'https://cdnpddlbecxqnpkfkvid.supabase.co';
        this.authToken = null;
        this.isAuthenticated = false;
        this.userInfo = null;
    }

    // 从存储中初始化认证信息
    async initializeFromWebsite() {
        try {
            const storage = await getStorage(['wbassist_auth']);
            const authData = storage.wbassist_auth;
            
            if (authData && authData.access_token) {
                this.authToken = authData.access_token;
                this.userInfo = authData.user;
                this.isAuthenticated = true;
                console.log('Background API Service: 认证信息已加载');
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('Background API Service: 初始化失败:', error);
            return false;
        }
    }

    // 上报预约成功
    async reportTaskSuccess(taskId, wbSupplyId, bookingSuccessAt, bookingDetails) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        const response = await fetch(`${this.supabaseUrl}/functions/v1/tasks-report-success`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                task_id: taskId,
                wb_supply_id: wbSupplyId,  
                booking_success_at: bookingSuccessAt,
                booking_details: bookingDetails
            })
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Background API Service: 预约成功上报完成:', result);
            return result;
        } else {
            const error = await response.json();
            throw new Error(error.message || '上报成功结果失败');
        }
    }

    // 上报预约失败
    async reportTaskFailure(taskId, status, errorInfo) {
        if (!this.isAuthenticated || !this.authToken) {
            throw new Error('用户未认证，请先登录');
        }

        const response = await fetch(`${this.supabaseUrl}/functions/v1/tasks-report-failure`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                task_id: taskId,
                status: status,
                error_info: errorInfo
            })
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Background API Service: 预约失败上报完成:', result);
            return result;
        } else {
            const error = await response.json();
            throw new Error(error.message || '上报失败结果失败');
        }
    }
}

// 增强监控功能 - 支持标签页关闭检测
const monitoringTabs = new Map(); // 存储监控任务与标签页的关联

// 监听标签页关闭事件
chrome.tabs.onRemoved.addListener(async (tabId, removeInfo) => {
    console.log(`🗂️ 标签页已关闭: ${tabId}`);
    
    // 检查是否有关联的监控任务
    for (const [orderId, monitoring] of activeMonitorings.entries()) {
        if (monitoring.tabId === tabId) {
            console.log(`⚠️ 检测到预约标签页被关闭: ${orderId}`);
            
            // 如果任务ID存在，上报任务中断
            if (monitoring.taskId) {
                try {
                    const apiService = new WBAssistApiService();
                    await apiService.initializeFromWebsite();
                    
                    if (apiService.isAuthenticated) {
                        await apiService.reportTaskFailure(
                            monitoring.taskId,
                            'cancelled',
                            {
                                error_code: 'TAB_CLOSED',
                                error_message: '预约标签页被用户关闭',
                                closed_at: new Date().toISOString(),
                                monitoring_duration: monitoring.startTime ? 
                                    Math.round((Date.now() - new Date(monitoring.startTime).getTime()) / 1000) : 0
                            }
                        );
                        
                        console.log(`✅ 已上报标签页关闭: 任务 ${monitoring.taskId}`);
                    }
                } catch (error) {
                    console.error(`❌ 上报标签页关闭失败: ${error.message}`);
                }
            }
            
            // 停止监控
            stopBookingMonitoring(orderId);
            break;
        }
    }
    
    // 清理标签页关联
    monitoringTabs.delete(tabId);
});

// 监听标签页更新事件，检测加载状态
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    // 检查是否是监控中的标签页
    for (const [orderId, monitoring] of activeMonitorings.entries()) {
        if (monitoring.tabId === tabId) {
            // 如果标签页长时间处于loading状态，可能有问题
            if (changeInfo.status === 'loading') {
                monitoring.lastLoadingTime = Date.now();
            } else if (changeInfo.status === 'complete') {
                monitoring.lastCompleteTime = Date.now();
                
                // 检查是否是WB相关页面
                if (tab.url && (tab.url.includes('seller.wildberries.ru') || tab.url.includes('wildberries'))) {
                    console.log(`🔄 监控标签页加载完成: ${orderId} - ${tab.url}`);
                }
            }
            break;
        }
    }
});
