// 工具函数库
// 通用工具函数

class Utils {
    // 格式化日期
    static formatDate(date) {
        return new Date(date).toLocaleDateString();
    }

    // 防抖函数
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// 将工具类添加到全局对象
window.Utils = Utils;
