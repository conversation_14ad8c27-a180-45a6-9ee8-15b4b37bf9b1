import Link from 'next/link';

export const metadata = {
  title: '关于我们 - WBAssist',
  description: '了解WBAssist的使命、愿景和团队',
};

export default function AboutPage() {
  return (
    <div className="relative max-w-6xl mx-auto px-4 sm:px-6">
      <div className="pt-32 pb-12 md:pt-40 md:pb-20">
        <div className="max-w-3xl mx-auto">
          <h1 className="h1 font-playfair-display text-center mb-8">关于我们</h1>
          
          <div className="text-lg text-gray-600 mb-8">
            <p className="mb-4">
              欢迎来到<strong className="font-medium text-gray-900">WBAssist</strong>！我们是专业的Wildberries仓位预约助手，致力于为卖家提供智能化的供货计划管理解决方案。
            </p>
            
            <h2 className="h3 font-playfair-display mb-4 mt-6">我们的使命</h2>
            <p className="mb-4">
              我们的使命是通过智能技术和自动化工具，帮助Wildberries卖家提升预约成功率，优化供货流程，让每一位卖家都能专注于业务发展而非繁琐的预约操作。
            </p>
            
            <h2 className="h3 font-playfair-display mb-4 mt-6">我们的愿景</h2>
            <p className="mb-4">
              我们希望成为Wildberries生态系统中最值得信赖的工具平台，帮助千万卖家实现更高效的运营管理，共同推动电商行业的智能化发展。
            </p>
            
            <h2 className="h3 font-playfair-display mb-4 mt-6">我们的团队</h2>
            <p className="mb-4">
              我们的团队由经验丰富的电商专家、技术开发人员和产品设计师组成。我们深入了解Wildberries平台的运营特点，致力于为用户创造最佳的使用体验。
            </p>
            
            <h2 className="h3 font-playfair-display mb-4 mt-6">我们的优势</h2>
            <ul className="list-disc list-inside mb-4 space-y-2">
              <li><strong className="font-medium text-gray-900">智能监控</strong>：7×24小时不间断监控，第一时间发现预约机会</li>
              <li><strong className="font-medium text-gray-900">安全可靠</strong>：先进的反检测技术，确保账号安全</li>
              <li><strong className="font-medium text-gray-900">高成功率</strong>：基于机器学习的预约策略，大幅提升成功率</li>
              <li><strong className="font-medium text-gray-900">专业服务</strong>：提供专业客服支持和定制化解决方案</li>
            </ul>
            
            <p className="mb-4 mt-6">
              无论您是刚入门的新卖家，还是经验丰富的大卖家，WBAssist都能为您提供专业的预约解决方案，助力您的业务发展！
            </p>
            
            <div className="flex justify-center mt-8">
              <Link href="/contact" className="btn text-white bg-blue-600 hover:bg-blue-700 mb-4 sm:mb-0">
                联系我们
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 