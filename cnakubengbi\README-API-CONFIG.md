# WBAssist 插件 API 配置说明

## 🎯 统一环境配置

现在所有的 API 地址都统一管理在 `shared/constants.js` 文件中，您只需要修改一个地方就能切换开发和生产环境。

## 📁 配置文件位置

```
cnakubengbi/shared/constants.js
```

## 🔧 如何切换环境

### 方法一：直接设置环境（推荐）

在 `constants.js` 文件中找到这一行：

```javascript
// 当前环境设置 - 改这里来切换环境
CURRENT_ENV: 'PRODUCTION', // 改为 'DEVELOPMENT' 或 'PRODUCTION'
```

- **开发环境**：改为 `'DEVELOPMENT'`
- **生产环境**：改为 `'PRODUCTION'`

### 方法二：修改环境配置地址

如果需要修改具体的地址，在 `constants.js` 中找到：

```javascript
// API 配置常量
const API_CONFIG = {
    // 🔧 环境配置 - 只需要修改这里就能切换环境
    DEVELOPMENT: {
        BASE_URL: 'http://localhost:3000',           // 开发环境地址
        WS_URL: 'ws://localhost:3000'               // WebSocket地址
    },
    PRODUCTION: {
        BASE_URL: 'https://wb-assist-web.vercel.app', // 生产环境地址
        WS_URL: 'wss://wb-assist-web.vercel.app'     // WebSocket地址
    },
    
    // 当前环境设置 - 改这里来切换环境
    CURRENT_ENV: 'PRODUCTION', // 改这里！
}
```

## 📋 修改步骤

1. **编辑配置文件**
   ```
   打开：cnakubengbi/shared/constants.js
   ```

2. **修改环境设置**
   ```javascript
   // 生产环境
   CURRENT_ENV: 'PRODUCTION'
   
   // 或开发环境  
   CURRENT_ENV: 'DEVELOPMENT'
   ```

3. **重新加载插件**
   - 在 Chrome 扩展管理页面点击"重新加载"
   - 或者重新安装插件

## 🔄 智能环境检测

插件还包含智能环境检测功能：
- 当在 localhost 页面运行时，自动使用开发环境
- 当在生产页面运行时，自动使用生产环境
- 如果检测失败，使用 `CURRENT_ENV` 设置的默认环境

## ✅ 配置验证

修改配置后，可以在浏览器控制台中验证：

```javascript
// 查看当前配置
console.log('当前API地址:', window.API_CONFIG.getApiUrl());
console.log('当前环境:', window.API_CONFIG.CURRENT_ENV);
```

## 🚨 重要提示

- ✅ **生产发布前**：确保 `CURRENT_ENV: 'PRODUCTION'`
- ✅ **开发调试时**：确保 `CURRENT_ENV: 'DEVELOPMENT'`  
- ✅ **修改后**：必须重新加载插件
- ❌ **不要**：同时修改多个文件中的地址（现在只需要修改 constants.js）

## 📂 已统一的文件

以下文件已经使用统一配置，无需单独修改：

- ✅ `content-script.js` - 内容脚本
- ✅ `js/wbassist-api-service.js` - API服务
- ✅ `shared/constants.js` - 常量配置（主配置文件）

## 🔗 API 地址对照

| 环境 | 地址 | 用途 |
|------|------|------|
| 开发 | `http://localhost:3000` | 本地开发调试 |
| 生产 | `https://wb-assist-web.vercel.app` | 线上正式环境 |

---

**💡 小贴士**：现在您只需要修改一个地方（`constants.js` 中的 `CURRENT_ENV`），就能让整个插件切换环境，再也不用到处找地址了！ 