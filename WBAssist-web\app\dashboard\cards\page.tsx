'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'
import { ClipboardDocumentIcon, CheckIcon } from '@heroicons/react/24/outline'

interface PurchasedLicense {
  // 从zpay_transactions获取
  order_id: string
  product_name: string
  purchase_date: string
  purchase_amount: string
  // 从licenses获取
  license_id: string
  license_key: string
  license_type: string
  duration_days: number
  monthly_quota: number
  // 从license_bindings获取（如果已激活）
  binding_id?: string
  store_name?: string
  activated_at?: string
  expires_at?: string
  current_month_used?: number
  status?: string
}

export default function CardsPage() {
  const [licenses, setLicenses] = useState<PurchasedLicense[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [copiedLicense, setCopiedLicense] = useState<string | null>(null)
  
  const supabase = createClient()
  const router = useRouter()

  useEffect(() => {
    checkAuth()
    fetchPurchasedLicenses()
  }, [])

  const checkAuth = async () => {
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      router.push('/signin')
    }
  }

  const fetchPurchasedLicenses = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return

      // 获取用户购买的卡密信息
      const response = await fetch('/api/me/licenses')
      if (!response.ok) {
        throw new Error('获取卡密信息失败')
      }

      const data = await response.json()
      setLicenses(data.licenses || [])
    } catch (error: any) {
      console.error('获取卡密错误:', error)
      setError(error.message || '获取卡密信息失败')
    } finally {
      setLoading(false)
    }
  }

  const copyLicenseKey = async (licenseKey: string) => {
    try {
      await navigator.clipboard.writeText(licenseKey)
      setCopiedLicense(licenseKey)
      setTimeout(() => setCopiedLicense(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const getLicenseTypeDisplayName = (type: string) => {
    switch (type) {
      case 'basic': return '基础版'
      case 'pro': return '专业版'
      case 'ultimate': return '旗舰版'
      default: return type
    }
  }

  const getProductTypeFromName = (productName: string) => {
    if (productName.includes('月卡') || productName.includes('monthly')) return '月卡'
    if (productName.includes('年卡') || productName.includes('yearly')) return '年卡'
    return '未知'
  }

  const getLicenseTypeStyle = (type: string) => {
    switch (type) {
      case 'basic': 
        return 'bg-gradient-to-r from-gray-500 to-gray-600 text-white shadow-md'
      case 'pro': 
        return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md'
      case 'ultimate': 
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-md'
      default: 
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getProductTypeStyle = (productType: string) => {
    switch (productType) {
      case '月卡':
        return 'bg-gradient-to-r from-emerald-400 to-cyan-400 text-white shadow-lg transform hover:scale-105 transition-all duration-200'
      case '年卡':
        return 'bg-gradient-to-r from-orange-400 to-red-400 text-white shadow-lg transform hover:scale-105 transition-all duration-200'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusDisplayName = (license: PurchasedLicense) => {
    if (!license.binding_id) return '未激活'
    if (license.status === 'expired') return '已过期'
    if (license.status === 'suspended') return '已暂停'
    if (license.expires_at && new Date(license.expires_at) < new Date()) return '已过期'
    return '已激活'
  }

  const getStatusColor = (license: PurchasedLicense) => {
    const status = getStatusDisplayName(license)
    switch (status) {
      case '已激活': return 'text-green-600 bg-green-100'
      case '已过期': return 'text-red-600 bg-red-100'
      case '已暂停': return 'text-yellow-600 bg-yellow-100'
      case '未激活': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getQuotaDisplayText = (quota: number) => {
    if (quota >= 999) return '无限制'
    return `${quota}次/月`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="pt-32 pb-12 md:pt-40 md:pb-20">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <p className="mt-4">加载中...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6">
      <div className="pt-32 pb-12 md:pt-40 md:pb-20">
        {/* 页面标题 */}
        <div className="max-w-3xl mx-auto text-center pb-12">
          <h1 className="h2 font-cabinet-grotesk">卡密管理</h1>
          <p className="text-xl text-gray-500 mt-4">
            管理您的WBAssist卡密，激活新卡密或查看已有卡密状态
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="max-w-4xl mx-auto mb-8">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          </div>
        )}

        {/* 我的卡密 */}
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold">我的卡密</h3>
            <p className="text-sm text-gray-500">
              共 {licenses.length} 个卡密
            </p>
          </div>
          
          {licenses.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">您还没有任何卡密</p>
              <p className="text-sm text-gray-400 mt-2">
                购买卡密或激活新卡密后，它们将显示在这里
              </p>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* 表格头部 */}
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div className="grid grid-cols-8 gap-4 text-sm font-medium text-gray-700">
                  <div>卡密</div>
                  <div>等级</div>
                  <div>套餐</div>
                  <div>配额</div>
                  <div>购买日期</div>
                  <div>激活日期</div>
                  <div>到期日期</div>
                  <div>状态</div>
                </div>
              </div>

              {/* 表格内容 */}
              <div className="divide-y divide-gray-200">
                {licenses.map((license) => (
                  <div key={license.license_id} className="px-6 py-4 hover:bg-gray-50">
                    <div className="grid grid-cols-8 gap-4 items-center">
                      {/* 卡密 */}
                      <div className="flex items-center space-x-2">
                        <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                          {license.license_key}
                        </code>
                        <button
                          onClick={() => copyLicenseKey(license.license_key)}
                          className="text-gray-400 hover:text-gray-600 transition-colors"
                          title="复制卡密"
                        >
                          {copiedLicense === license.license_key ? (
                            <CheckIcon className="h-4 w-4 text-green-500" />
                          ) : (
                            <ClipboardDocumentIcon className="h-4 w-4" />
                          )}
                        </button>
                      </div>

                      {/* 等级 */}
                      <div>
                        <span className={`inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-bold ${getLicenseTypeStyle(license.license_type)}`}>
                          {getLicenseTypeDisplayName(license.license_type)}
                        </span>
                      </div>

                      {/* 套餐 */}
                      <div>
                        <span className={`inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-bold ${getProductTypeStyle(getProductTypeFromName(license.product_name))}`}>
                          {getProductTypeFromName(license.product_name)}
                        </span>
                      </div>

                      {/* 配额 */}
                      <div className="text-sm text-gray-900">
                        {getQuotaDisplayText(license.monthly_quota)}
                        {license.binding_id && license.current_month_used !== undefined && (
                          <div className="text-xs text-gray-500">
                            已用 {license.current_month_used} 次
                          </div>
                        )}
                      </div>

                      {/* 购买日期 */}
                      <div className="text-sm text-gray-900">
                        {formatDate(license.purchase_date)}
                        <div className="text-xs text-gray-500">
                          ¥{parseFloat(license.purchase_amount).toFixed(2)}
                        </div>
                      </div>

                      {/* 激活日期 */}
                      <div className="text-sm text-gray-900">
                        {license.activated_at ? (
                          <>
                            {formatDate(license.activated_at)}
                            {license.store_name && (
                              <div className="text-xs text-gray-500 truncate" title={license.store_name}>
                                {license.store_name}
                              </div>
                            )}
                          </>
                        ) : (
                          <span className="text-gray-400">未激活</span>
                        )}
                      </div>

                      {/* 到期日期 */}
                      <div className="text-sm text-gray-900">
                        {license.expires_at ? (
                          formatDate(license.expires_at)
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </div>

                      {/* 状态 */}
                      <div>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(license)}`}>
                          {getStatusDisplayName(license)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 说明信息 */}
        <div className="max-w-6xl mx-auto mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h4 className="text-sm font-medium text-blue-800 mb-2">说明</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 卡密在支付成功页面显示，请妥善保存</li>
            <li>• 到期日期按激活日期计算，不是购买日期</li>
            <li>• 每个卡密只能绑定一个店铺，绑定后无法更改</li>
            <li>• 配额按月度周期重置，基于激活日期计算</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
