# WBAssist 自动预约系统 - 全面测试场景文档

## 📋 文档概览

**文档版本**: v2.0  
**最后更新**: 2025-01-23  
**测试范围**: 完整的WBAssist系统功能测试  
**优先级**: P0(核心功能) > P1(重要功能) > P2(增强功能)

---

## 🎯 1. 测试环境准备

### 1.1 测试环境配置

| 组件 | 配置要求 | 备注 |
|------|----------|------|
| **Chrome扩展环境** | Chrome开发者模式，加载unpacked扩展 | 支持Chrome 90+ |
| **Web网站环境** | Next.js开发服务器 (`npm run dev`) | 本地3000端口 |
| **Supabase环境** | 项目ID: `cdnpddlbecxqnpkfkvid`，亚太东南区 | 生产环境 |
| **Wildberries环境** | 有效的WB卖家账户，包含供应单数据 | 真实测试账户 |

### 1.2 测试数据准备

#### 用户账户数据
- ✅ **WBAssist账户**: 至少3个不同状态账户（新用户、已验证、VIP用户）
- ✅ **WB卖家账户**: 至少2个不同的WB供应商账户
- ✅ **权限测试**: 包含免费版、专业版、旗舰版用户

#### 订单数据准备
- ✅ **测试订单**: 包含不同状态的preorder（草稿、待预约、已预约）
- ✅ **时间范围**: 涵盖过去、现在、未来的订单数据
- ✅ **商品类型**: 不同类别和价格范围的商品

#### 卡密测试数据
- ✅ **有效卡密**: 各种套餐的未使用卡密
- ✅ **无效卡密**: 过期、已使用、格式错误的卡密
- ✅ **特殊卡密**: 测试用无限期卡密

---

## 🧪 2. 功能测试场景

### 2.1 用户认证流程测试 `[P0]`

#### 📝 场景2.1.1：首次用户注册登录

**测试目标**: 验证新用户完整注册登录流程

**前置条件**: 
- 全新Chrome Profile，未安装过扩展

**测试步骤**:
1. **扩展安装**: 安装Chrome扩展，点击扩展图标
2. **初始界面**: 验证显示"请先登录WBAssist账户"界面
3. **登录触发**: 点击"立即登录"按钮，验证弹窗打开
4. **认证流程**: 在弹窗中完成注册/登录流程
5. **自动关闭**: 验证弹窗自动关闭，扩展显示初始化界面
6. **初始化流程**: 验证完整初始化步骤
   - ✅ 验证登录状态
   - ✅ 同步店铺信息  
   - ✅ 检查店铺权限
   - ✅ 加载订单数据
7. **状态指示**: 验证每个步骤显示正确的图标状态（旋转→绿色对勾）
8. **完成确认**: 验证初始化完成后显示主界面

**期望结果**:
- ✅ 弹窗登录体验流畅，无卡顿
- ✅ 初始化流程完整显示，状态更新正确
- ✅ 主界面显示完整功能区域

---

#### 📝 场景2.1.2：JWT Token过期重新登录

**测试目标**: 验证token过期后重新登录的用户体验

**前置条件**: 
- 已登录用户，手动使token过期

**测试步骤**:
1. **模拟过期**: 使用开发者工具模拟token过期（修改localStorage中的token）
2. **触发检测**: 刷新扩展或重新打开
3. **过期提示**: 验证显示"登录已过期，请重新登录"界面（时钟图标）
4. **重新登录**: 点击"重新登录"按钮，完成登录
5. **完整初始化**: 验证重新登录后显示完整初始化流程
6. **数据完整性**: 验证主界面数据完整加载，无空白组件

**期望结果**:
- ✅ 准确识别token过期状态
- ✅ 重新登录后完整走初始化流程
- ✅ 避免出现空白界面逐步填充的情况

---

#### 📝 场景2.1.3：主动登出后重新登录

**测试目标**: 验证用户主动登出后的重新登录体验

**前置条件**: 
- 已登录并初始化完成的用户

**测试步骤**:
1. **主动登出**: 在已初始化的主界面点击用户头像区域的"登出"按钮
2. **状态清理**: 验证立即显示登录界面，用户信息被清除
3. **重新登录**: 重新登录相同或不同账户
4. **完整流程**: 验证重新登录后显示完整初始化流程
5. **组件验证**: 验证所有组件正常加载，数据完整显示

**期望结果**:
- ✅ 登出后状态清理干净
- ✅ 重新登录体验与首次登录一致
- ✅ 多账户切换正常工作

---

### 2.2 店铺管理测试 `[P0]`

#### 📝 场景2.2.1：首次同步WB店铺

**测试目标**: 验证从WB账户同步店铺信息功能

**前置条件**: 
- 已登录WBAssist，但未同步店铺

**测试步骤**:
1. **WB登录**: 在浏览器中登录有效的WB卖家账户
2. **触发同步**: 在扩展中触发店铺同步功能
3. **认证获取**: 验证扩展能够获取WB认证信息（authToken和cookies）
4. **店铺列表**: 验证店铺列表正确显示从WB API获取的店铺信息
5. **信息完整性**: 验证店铺信息包含：
   - 店铺名称
   - INN
   - 地区
   - 审核状态
6. **免费配额**: 验证每个新店铺自动获得1次免费预约机会
7. **选择器功能**: 验证店铺选择器正常工作

**期望结果**:
- ✅ WB API集成正常，数据同步准确
- ✅ 免费版权限自动激活
- ✅ 店铺信息展示完整

---

#### 📝 场景2.2.2：多店铺管理切换

**测试目标**: 验证用户管理多个WB店铺的功能

**前置条件**: 
- 用户已同步多个WB供应商店铺

**测试步骤**:
1. **店铺列表**: 验证店铺选择器显示所有同步的店铺
2. **切换验证**: 切换不同店铺，验证对应信息更新：
   - 卡密绑定状态
   - 剩余配额数量
   - 历史订单数据
3. **权限隔离**: 验证不同店铺的权限隔离正确
4. **访问限制**: 测试无权限店铺的访问限制

**期望结果**:
- ✅ 多店铺数据完全隔离
- ✅ 店铺切换后数据更新准确
- ✅ 权限验证机制有效

---

### 2.3 卡密系统测试 `[P0]`

#### 📝 场景2.3.1：免费版权限测试

**测试目标**: 验证免费版自动激活和使用流程

**前置条件**: 
- 新同步的店铺，未绑定付费卡密

**测试步骤**:
1. **自动激活**: 验证新店铺自动显示"剩余免费配额：1次"
2. **任务创建**: 创建并执行一次预约任务
3. **配额扣减**: 验证预约成功后免费配额扣减到0
4. **升级提示**: 验证配额用尽后的升级提示
5. **功能限制**: 测试免费配额用尽后无法创建新任务

**期望结果**:
- ✅ 免费版权限自动激活无需用户操作
- ✅ 配额扣减逻辑精确
- ✅ 升级引导体验良好

---

#### 📝 场景2.3.2：付费卡密购买绑定流程

**测试目标**: 验证完整的付费套餐购买和卡密绑定流程

**前置条件**: 
- 免费配额已用完的用户

**测试流程**:

```mermaid
flowchart TD
    A[用户选择套餐] --> B[完成支付]
    B --> C[生成卡密]
    C --> D[复制卡密到扩展]
    D --> E[选择目标店铺]
    E --> F[输入卡密并绑定]
    F --> G[验证权限生效]
    G --> H[配额更新完成]
```

**测试步骤**:
1. **套餐选择**: 在网站选择付费套餐（专业版月卡）
2. **支付流程**: 完成支付流程（使用测试支付环境）
3. **卡密生成**: 验证支付成功页面显示生成的卡密
4. **卡密绑定**: 复制卡密，在扩展中进行绑定
5. **店铺选择**: 选择目标店铺，输入卡密，点击绑定
6. **权限验证**: 验证绑定成功后权限立即生效
7. **配额确认**: 验证配额更新：月度配额、剩余配额、有效期

**期望结果**:
- ✅ 支付到卡密生成的自动化流程正常
- ✅ 卡密格式符合规范
- ✅ 绑定后权限立即生效

---

### 2.4 订单管理和任务创建测试 `[P1]`

#### 📝 场景2.4.1：订单数据加载显示

**测试目标**: 验证从WB API获取订单数据的功能

**前置条件**: 
- 已绑定有效卡密的店铺，WB账户有订单数据

**测试步骤**:
1. **数据获取**: 验证订单列表正确显示WB系统中的供应单
2. **信息完整性**: 检查订单信息完整性
   - 供应单号（preorderId）
   - 创建时间
   - 状态（草稿、待预约、已预约）
   - 商品信息
3. **分页功能**: 验证订单分页功能
4. **筛选搜索**: 验证订单筛选和搜索功能
5. **刷新机制**: 测试订单数据刷新机制

**期望结果**:
- ✅ 订单数据与WB系统完全一致
- ✅ 分页和搜索功能正常
- ✅ 数据刷新及时准确

---

#### 📝 场景2.4.2：预约任务创建流程

**测试目标**: 验证基于preorderId创建预约任务的完整流程

**前置条件**: 
- 有可预约订单的店铺，剩余配额>0

**任务配置参数**:

| 参数类型 | 配置选项 | 说明 |
|----------|----------|------|
| **预约日期** | 单日/多日期范围 | 设置预约的时间窗口 |
| **时间段** | 特定时段/全时段 | 可接受的预约时间 |
| **承兑系数** | 0/设定上限/无限制 | 最大承兑系数限制 |
| **缓冲天数** | 1-30天 | 任务超时停止监控的天数 |

**测试步骤**:
1. **订单选择**: 从订单列表选择状态为"草稿"的订单
2. **参数配置**: 配置预约参数（见上表）
3. **权限预检**: 验证创建前的权限和配额检查
4. **任务创建**: 点击"开始预约"，验证任务创建成功
5. **状态确认**: 验证任务初始状态为"pending"
6. **监控启动**: 验证后台监控服务自动启动

**期望结果**:
- ✅ 任务配置参数验证完整
- ✅ 权限检查准确有效
- ✅ 任务创建后状态正确

---

### 2.5 自动预约执行测试 `[P0]`

#### 📝 场景2.5.1：后台监控服务测试

**测试目标**: 验证后台监控轮询和时段检查功能

**前置条件**: 
- 已创建预约任务，background.js监控服务运行

**监控机制**:

```mermaid
sequenceDiagram
    participant T as Task
    participant M as Monitor
    participant W as WB API
    participant U as User
    
    T->>M: 任务创建，启动监控
    M->>W: 定期调用getAcceptanceCosts
    W->>M: 返回可用时段数据
    M->>M: 根据用户配置筛选时段
    alt 发现匹配时段
        M->>U: 执行自动预约
        M->>M: 停止监控避免重复
    else 无匹配时段
        M->>M: 继续监控(随机间隔5-30秒)
    end
```

**测试步骤**:
1. **监控启动**: 验证任务创建后监控服务自动启动，检查控制台日志确认轮询开始
2. **API调用**: 验证定期调用getAcceptanceCosts API，检查获取的可用时段数据
3. **条件筛选**: 验证根据用户配置筛选时段
4. **轮询间隔**: 验证随机间隔（5-30秒）避免检测，测试高频轮询的性能影响
5. **匹配逻辑**: 验证条件匹配逻辑
   - 日期范围匹配
   - 系数限制匹配
   - 时间段限制匹配

**期望结果**:
- ✅ 监控服务稳定运行不崩溃
- ✅ WB API调用频率合理
- ✅ 条件匹配逻辑准确

---

#### 📝 场景2.5.2：自动预约执行测试

**测试目标**: 验证发现匹配时段时的自动预约执行

**前置条件**: 
- 监控服务运行中，WB系统有匹配的可用时段

**自动化流程**:

```mermaid
flowchart TD
    A[发现匹配时段] --> B[停止轮询监控]
    B --> C[打开WB预约页面]
    C --> D[注入页面脚本]
    D --> E[点击"安排交货"]
    E --> F[等待日历控件]
    F --> G[选择最优日期]
    G --> H[确认预约操作]
    H --> I[检测预约成功]
    I --> J[获取supplyId]
```

**测试步骤**:
1. **触发条件**: 等待监控服务发现匹配时段，验证立即停止轮询避免重复预约
2. **页面自动化**: 验证自动打开WB预约页面，检查页面脚本注入成功
3. **操作流程**: 验证自动化点击流程（见上图）
4. **成功检测**: 验证预约成功的多重确认机制，检查获取到新的supplyId

**期望结果**:
- ✅ 自动化操作流程稳定可靠
- ✅ 成功检测机制准确
- ✅ supplyId获取无误

---

### 2.6 异常处理测试 `[P1]`

#### 📝 场景2.6.1：网络异常处理测试

**测试目标**: 验证各种网络异常情况的处理

**异常类型与处理**:

| 异常类型 | 测试方法 | 期望处理 |
|----------|----------|----------|
| **WB API失败** | 模拟网络中断 | 自动重试，记录错误日志 |
| **后端API失败** | 模拟后端不可用 | 本地缓存，离线后重新同步 |
| **页面加载失败** | 模拟页面超时 | 重试机制，任务状态回滚 |

**期望结果**:
- ✅ 网络异常不导致数据丢失
- ✅ 重试机制合理有效
- ✅ 错误信息记录完整

---

## 📊 3. 性能测试场景

### 3.1 扩展性能测试 `[P1]`

#### 性能指标基准

| 测试项目 | 性能指标 | 测试方法 |
|----------|----------|----------|
| **扩展启动** | 首次启动 < 3秒<br/>后续启动 < 1秒 | 测量启动时间和内存使用 |
| **数据加载** | 订单列表 < 2秒<br/>店铺切换 < 500ms | 监控网络请求和UI响应 |
| **后台监控** | 24小时无崩溃<br/>内存增长 < 10MB/小时 | 长时间运行稳定性测试 |

### 3.2 并发性能测试 `[P2]`

#### 并发测试场景

- **多用户并发**: 支持10用户并发无错误
- **高频操作**: 支持每分钟100次操作，错误率 < 1%
- **API压力**: 响应时间 < 2秒，可用性 > 99%

---

## 🔒 4. 安全测试场景

### 4.1 权限安全测试 `[P0]`

#### 安全检查清单

- ✅ **数据隔离**: 用户只能访问自己的数据
- ✅ **卡密安全**: 传输加密，存储哈希，防重放攻击
- ✅ **API安全**: 防SQL注入、XSS、CSRF攻击
- ✅ **配额安全**: 防绕过配额限制，异常使用检测

### 4.2 业务安全测试 `[P0]`

#### 业务逻辑保护

- ✅ **任务安全**: 防重复创建，状态不可篡改
- ✅ **配额保护**: 扣减逻辑不可绕过，数据完整性
- ✅ **权限验证**: 严格的权限检查和验证机制

---

## 🌐 5. 兼容性测试场景

### 5.1 浏览器兼容性 `[P1]`

| 环境 | 支持版本 | 测试重点 |
|------|----------|----------|
| **Chrome** | 90+ 版本 | Manifest V3功能，扩展API兼容性 |
| **操作系统** | Windows/macOS/Linux | 跨平台功能一致性 |
| **网络环境** | 各种网络条件 | 适应性和恢复能力 |

---

## 🔄 6. 回归测试场景

### 6.1 测试执行策略

#### 每日回归测试 `[自动化]`
- **测试内容**: 核心功能（认证、店铺、卡密、基本预约）
- **执行频率**: 每日自动执行
- **通过标准**: 所有测试用例100%通过

#### 发布前完整回归 `[手动+自动]`
- **测试内容**: 本文档所有P0和P1级别测试场景
- **执行频率**: 每次发布前
- **通过标准**: P0级别100%通过，P1级别95%通过

---

## 🛠️ 7. 测试环境维护

### 7.1 测试数据管理

| 类型 | 工具/方法 | 维护频率 |
|------|-----------|----------|
| **数据准备** | 自动生成脚本 | 每周更新 |
| **数据清理** | 定期清理机制 | 每日执行 |
| **数据备份** | 快速恢复机制 | 实时备份 |
| **隐私保护** | 数据脱敏处理 | 持续执行 |

### 7.2 自动化测试支持

- 🤖 **UI自动化**: Selenium/Playwright自动化脚本
- 🔧 **API自动化**: Postman/Jest API测试集
- 📊 **性能监控**: 持续的性能基线监控
- 🚀 **CI/CD集成**: 集成流水线自动测试

### 7.3 测试报告和跟踪

- 📋 **执行报告**: 详细的测试结果记录
- 🐛 **缺陷跟踪**: Bug发现、修复、验证流程
- 📈 **覆盖率统计**: 代码和功能覆盖率
- 📊 **质量趋势**: 长期质量趋势监控

---

## 📝 总结

本测试文档涵盖了WBAssist自动预约系统的所有关键功能和场景，确保系统在各种条件下的：

- 🎯 **稳定性**: 核心功能稳定可靠
- 🔒 **安全性**: 数据和业务逻辑安全
- 👥 **用户体验**: 界面友好，操作流畅
- ⚡ **性能**: 响应及时，资源占用合理

### 测试优先级建议

| 优先级 | 测试范围 | 执行频率 |
|--------|----------|----------|
| **P0** | 核心功能（认证、卡密、预约） | 每次发布前必测 |
| **P1** | 重要功能（异常处理、性能） | 重要版本发布前 |
| **P2** | 增强功能（兼容性、优化） | 大版本发布前 |

---

**维护说明**: 本文档应随功能迭代持续更新，建议每个Sprint结束后review和更新相关测试场景。