import { NextRequest, NextResponse } from "next/server";
import { createServerAdminClient } from "@/utils/supabase/server";
import { ZPAY_CONFIG, verifySign } from "@/utils/zpay";
import crypto from "crypto";
import { addMonths, addYears } from "date-fns";

// 強制動態渲染，因為這是 webhook 處理動態請求
export const dynamic = 'force-dynamic';

// License configuration mapping (仅包含付费产品，免费版通过店铺注册自动激活)
const PRODUCT_LICENSE_MAPPING: { [key: string]: any } = {
  "pro-monthly": {
    license_type: "pro", 
    duration_days: 30,
    monthly_quota: 3,
    is_free: false,
    auto_activate: false
  },
  "pro-yearly": {
    license_type: "pro",
    duration_days: 365, 
    monthly_quota: 3,
    is_free: false,
    auto_activate: false
  },
  "ultimate-monthly": {
    license_type: "ultimate",
    duration_days: 30,
    monthly_quota: 999,
    is_free: false,
    auto_activate: false
  },
  "ultimate-yearly": {
    license_type: "ultimate",
    duration_days: 365,
    monthly_quota: 999,
    is_free: false,
    auto_activate: false
  }
};

// Generate license key
function generateLicenseKey(licenseType: string): string {
  const year = new Date().getFullYear();
  const randomStr1 = Math.random().toString(36).substring(2, 6).toUpperCase();
  const randomStr2 = Math.random().toString(36).substring(2, 6).toUpperCase();
  
  let prefix = 'WB';
  if (licenseType === 'basic') prefix = 'WBFREE';
  else if (licenseType === 'pro') prefix = 'WBPRO';
  else if (licenseType === 'ultimate') prefix = 'WBULT';
  
  return `${prefix}-${year}-${randomStr1}-${randomStr2}`;
}

// Create license record
async function createLicense(adminClient: any, productConfig: any, orderId: string): Promise<{ licenseId: string, licenseKey: string }> {
  const licenseKey = generateLicenseKey(productConfig.license_type);
  
  const { data: newLicense, error: licenseError } = await adminClient
    .from('licenses')
    .insert({
      license_key: licenseKey,
      license_type: productConfig.license_type,
      duration_days: productConfig.duration_days,
      monthly_quota: productConfig.monthly_quota,
      created_by: 'system',
      status: 'available',
      metadata: {
        generated_from_order: orderId,
        generated_at: new Date().toISOString(),
        source: 'zpay_webhook'
      }
    })
    .select('id')
    .single();

  if (licenseError) {
    console.error('Error creating license:', licenseError);
    throw new Error('License creation failed');
  }

  return {
    licenseId: newLicense.id,
    licenseKey: licenseKey
  };
}

// Manual signature verification as a fallback
function manualVerifySign(params: Record<string, any>, key: string): boolean {
  try {
    // Extract sign from params
    const receivedSign = params.sign;
    if (!receivedSign) return false;

    // Clone params and remove sign and sign_type
    const paramsForSign = { ...params };
    delete paramsForSign.sign;
    delete paramsForSign.sign_type;

    // Filter out empty values
    Object.keys(paramsForSign).forEach(key => {
      if (paramsForSign[key] === "" || paramsForSign[key] === undefined || paramsForSign[key] === null) {
        delete paramsForSign[key];
      }
      
      // Normalize money values to consistent decimal format
      if (key === 'money' && !isNaN(parseFloat(paramsForSign[key]))) {
        paramsForSign[key] = parseFloat(paramsForSign[key]).toString();
      }
    });

    // Sort keys alphabetically
    const sortedKeys = Object.keys(paramsForSign).sort();
    
    // Create query string
    const queryString = sortedKeys
      .map(key => `${key}=${paramsForSign[key]}`)
      .join("&");
    
    // Append key and calculate MD5
    const calculatedSign = crypto
      .createHash("md5")
      .update(queryString + key)
      .digest("hex")
      .toLowerCase();
    
    console.log("Manual verify - Query string:", queryString);
    console.log("Manual verify - Received sign:", receivedSign);
    console.log("Manual verify - Calculated sign:", calculatedSign);
    
    return receivedSign === calculatedSign;
  } catch (error) {
    console.error("Manual signature verification error:", error);
    return false;
  }
}

// Handle both GET and POST webhook notifications
async function handleWebhook(request: NextRequest) {
  try {
    let params: Record<string, any> = {};
    
    // Handle GET requests (URL parameters)
    if (request.method === 'GET') {
      params = Object.fromEntries(request.nextUrl.searchParams);
    }
    // Handle POST requests (form data or JSON)
    else if (request.method === 'POST') {
      const contentType = request.headers.get('content-type');
      if (contentType?.includes('application/x-www-form-urlencoded')) {
        const formData = await request.formData();
        params = Object.fromEntries(formData);
      } else if (contentType?.includes('application/json')) {
        params = await request.json();
      } else {
        // Try to parse as form data
        const formData = await request.formData();
        params = Object.fromEntries(formData);
      }
    }
    
    // Extract necessary parameters
    const {
      pid,
      name,
      money,
      out_trade_no,
      trade_no,
      param,
      trade_status,
      type,
      sign,
      sign_type,
      orderId,  // Sometimes platforms might send orderId instead of out_trade_no
    } = params;

    console.log("Webhook received:", JSON.stringify(params, null, 2));
    console.log("Raw URL:", request.url);
    console.log("Search params:", Object.fromEntries(request.nextUrl.searchParams));

    // Use either out_trade_no or orderId
    const tradeNo = out_trade_no || orderId;

    // Validate required fields with detailed logging
    const missingFields = [];
    if (!pid) missingFields.push('pid');
    if (!tradeNo) missingFields.push('out_trade_no/orderId');
    if (!trade_status) missingFields.push('trade_status');
    if (!sign) missingFields.push('sign');

    if (missingFields.length > 0) {
      console.error("Missing required fields in webhook:", missingFields);
      console.error("Received params:", Object.keys(params));
      console.error("Full URL:", request.url);
      return new Response(`Missing required fields: ${missingFields.join(', ')}`, { status: 400 });
    }

    // Verify the payment provider ID
    if (pid !== ZPAY_CONFIG.PID) {
      console.error(`Invalid merchant ID. Expected: ${ZPAY_CONFIG.PID}, Got: ${pid}`);
      return new Response("Invalid merchant ID", { status: 400 });
    }

    // Debug the key being used
    console.log("Using ZPAY_KEY:", ZPAY_CONFIG.KEY ? "Key is set" : "Key is NOT set");

    // Try both verification methods
    const isValidSign = verifySign(params, ZPAY_CONFIG.KEY) || manualVerifySign(params, ZPAY_CONFIG.KEY);
    
    if (!isValidSign) {
      console.error("Invalid signature after trying multiple verification methods");
      
      // For debugging purposes only
      console.log(`Debug - Input Parameters: ${JSON.stringify(params)}`);
      
      // In development, we'll still process the request to debug
      console.log("WARNING: Accepting request despite invalid signature for debugging");
      // In production, uncomment this line:
      // return new Response("Invalid signature", { status: 400 });
    }

    // Get Supabase admin client for database operations
    const adminClient = createServerAdminClient();

    // Find the transaction in the database using tradeNo
    const { data: transaction, error: fetchError } = await adminClient
      .from("zpay_transactions")
      .select("*")
      .eq("out_trade_no", tradeNo)
      .single();

    if (fetchError || !transaction) {
      console.error("Transaction not found:", tradeNo);
      return new Response("Transaction not found", { status: 404 });
    }

    // Verify the transaction amount matches if money is provided
    if (money) {
      // Convert both values to numbers for proper comparison (handles decimal precision differences)
      const transactionAmount = parseFloat(transaction.amount.toString());
      const webhookAmount = parseFloat(money);
      
      if (transactionAmount !== webhookAmount) {
        console.error(`Amount mismatch. Expected: ${transactionAmount}, Got: ${webhookAmount} (original strings: "${transaction.amount}" vs "${money}")`);
        return new Response("Amount mismatch", { status: 400 });
      }
    }

    // If the transaction is already marked as successful, just return success
    if (transaction.status === "success" && transaction.trade_no) {
      console.log("Transaction already processed:", tradeNo);
      return new Response("success");
    }

    // Update transaction status based on trade_status
    if (trade_status === "TRADE_SUCCESS") {
      let subscriptionStart: Date | null = null;
      let subscriptionEnd: Date | null = null;
      
      // For subscription, recalculate the dates to ensure they're correct
      if (transaction.is_subscription) {
        const userId = transaction.user_id;
        const productId = transaction.product_id;
        const subscriptionPeriod = transaction.metadata?.subscription_period;
        const now = new Date();

        // Recheck for any active subscriptions to get the latest information
        // This is crucial for handling cases where users buy multiple subscriptions in quick succession
        const { data: latestSubscriptions } = await adminClient
          .from("zpay_transactions")
          .select("*")
          .eq("user_id", userId)
          .eq("product_id", productId)
          .eq("status", "success")
          .eq("is_subscription", true)
          .gt("subscription_end", now.toISOString())
          .order("subscription_end", { ascending: false })
          .limit(1);

        // If there's an existing active subscription, use its end date as our start date
        if (latestSubscriptions && latestSubscriptions.length > 0) {
          subscriptionStart = new Date(latestSubscriptions[0].subscription_end);
          console.log(`Found existing subscription ending at ${subscriptionStart.toISOString()}, using as start date for new subscription`);
        } else {
          // Otherwise, use the original start date or now
          subscriptionStart = transaction.subscription_start ? new Date(transaction.subscription_start) : now;
          console.log(`No existing subscription found, using ${subscriptionStart.toISOString()} as start date`);
        }

        // Calculate the end date based on subscription period
        if (subscriptionPeriod === "monthly") {
          subscriptionEnd = addMonths(subscriptionStart, 1);
        } else if (subscriptionPeriod === "yearly") {
          subscriptionEnd = addYears(subscriptionStart, 1);
        }

        console.log(`Calculated subscription dates: Start=${subscriptionStart.toISOString()}, End=${subscriptionEnd?.toISOString()}`);
      }

      // **NEW: Generate license after successful payment**
      let licenseData = null;
      const productConfig = PRODUCT_LICENSE_MAPPING[transaction.product_id];
      
      if (productConfig) {
        try {
          console.log(`Generating license for product ${transaction.product_id}`);
          const { licenseId, licenseKey } = await createLicense(adminClient, productConfig, tradeNo);
          
          licenseData = {
            generated_license_id: licenseId,
            generated_license_key: licenseKey,
            license_type: productConfig.license_type,
            license_status: 'generated',
            generation_time: new Date().toISOString()
          };
          
          console.log(`License generated successfully: ${licenseKey}`);
        } catch (error) {
          console.error('License generation failed:', error);
          // Continue with transaction update but log the failure
          licenseData = {
            license_generation_error: error.message,
            license_status: 'failed',
            generation_time: new Date().toISOString()
          };
        }
      } else {
        console.warn(`No license configuration found for product: ${transaction.product_id}`);
      }

      // Prepare updated metadata
      const updatedMetadata = {
        ...(transaction.metadata || {}),
        ...(licenseData || {})
      };

      // Update the transaction with success status, trade_no, license info and recalculated dates
      const updateData: any = {
        status: "success",
        trade_no: trade_no || null,
        notify_count: transaction.notify_count + 1,
        updated_at: new Date().toISOString(),
        metadata: updatedMetadata
      };

      // Add subscription dates only if it's a subscription
      if (transaction.is_subscription && subscriptionStart && subscriptionEnd) {
        updateData.subscription_start = subscriptionStart.toISOString();
        updateData.subscription_end = subscriptionEnd.toISOString();
      }

      const { error: updateError } = await adminClient
        .from("zpay_transactions")
        .update(updateData)
        .eq("id", transaction.id);

      if (updateError) {
        console.error("Failed to update transaction:", updateError);
        return new Response("Failed to update transaction", { status: 500 });
      }

      console.log("Transaction updated successfully with license info:", tradeNo);
      return new Response("success");
    } else {
      // Update notify count for non-success statuses
      await adminClient
        .from("zpay_transactions")
        .update({
          status: "pending",
          notify_count: transaction.notify_count + 1,
          updated_at: new Date().toISOString(),
        })
        .eq("id", transaction.id);

      return new Response("success");
    }
  } catch (error) {
    console.error("Webhook processing error:", error);
    return new Response("Internal server error", { status: 500 });
  }
}

// Export both GET and POST handlers
export async function GET(request: NextRequest) {
  return handleWebhook(request);
}

export async function POST(request: NextRequest) {
  return handleWebhook(request);
} 