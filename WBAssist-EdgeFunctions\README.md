# WBAssist Edge Functions

这里包含了WBAssist系统的所有Supabase Edge Functions。

## 函数列表

### 认证相关
- **extension-auth**: 插件认证服务，验证JWT token并返回用户信息

### 权限管理
- **capabilities**: 查询用户对特定店铺的权限和配额状态，支持全局共享权限

### 店铺管理
- **stores-register**: 店铺注册和同步，从Wildberries API获取店铺信息并创建免费版权限

### 卡密管理
- **licenses-bind**: 卡密绑定到店铺，验证卡密有效性并创建绑定关系
- **license-upgrade**: 卡密升级功能，支持从低级别升级到高级别

### 任务管理
- **tasks-create**: 创建预约任务，支持全局店铺权限验证
- **tasks-report-success**: 上报任务成功结果，扣减配额并创建使用记录
- **tasks-report-failure**: 上报任务失败结果，不扣减配额
- **tasks-list**: 获取用户任务列表，支持分页和筛选

### 配额管理
- **credits-usage**: 查询用户配额使用情况和使用详情

### 产品管理
- **products**: 获取产品信息和价格配置，无需认证

### 支付管理
- **checkout-zpay-url**: 生成ZPay支付链接，创建订单记录
- **payment-license**: 支付完成后获取卡密信息

### 系统管理
- **ownership-management**: 店铺所有权管理，解决所有权冲突
- **device-management**: 设备管理，注册设备和同步状态

## 技术特点

1. **全局权限共享**: 支持多用户共享店铺权限，任何用户都可以使用已注册的店铺
2. **JWT认证**: 使用Supabase JWT进行用户认证
3. **配额管理**: 精确的月度配额计算和扣减
4. **错误处理**: 完善的错误处理和返回机制
5. **CORS支持**: 支持跨域请求

## 部署说明

这些Edge Functions需要部署到Supabase项目中，每个函数的入口文件都是 `source/index.ts`。

## 环境变量

需要配置以下环境变量：
- `SUPABASE_URL`: Supabase项目URL
- `SUPABASE_ANON_KEY`: Supabase匿名访问密钥
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase服务角色密钥
- `ZPAY_MERCHANT_ID`: ZPay商户ID（支付功能）
- `ZPAY_SECRET_KEY`: ZPay密钥（支付功能）
- `ZPAY_API_URL`: ZPay API地址（支付功能）
- `FRONTEND_URL`: 前端网站地址
- `ADMIN_EMAILS`: 管理员邮箱列表（逗号分隔） 